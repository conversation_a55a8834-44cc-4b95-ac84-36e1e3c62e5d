import React from 'react';
import { View, TouchableOpacity, ScrollView, findNodeHandle } from 'react-native';
import FormItemIcon from 'project_components/Icons/FormItemIcon';
import styles from './styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import { MenuPageDto } from '../Form';
import { StoreComponent } from 'utils/store-component';
import { FormStore } from 'stores/form-store';
import { ReactComponent } from 'redi-types';
import SideMenuItem from './SideMenuItem';
import AppButton from 'components/AppButton/AppButton';
import { scaleToDesign } from 'utils/design-scale';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

@StoreComponent({
	pages: FormStore.menuPages$
})
class SideMenu extends React.PureComponent<Props, State> {
	state = {
		open: false
	};

	render() {
		return (
			<View style={styles.root}>
				<TouchableOpacity
					style={{
						...styles.menuBtn,
						display: this.state.open ? 'none' : 'flex'
					}}
					onPress={() => this.setState({ open: true })}>
					<FormItemIcon colour="#fff" size={28} />
				</TouchableOpacity>
				<View
					style={{
						...styles.panel,
						display: this.state.open ? 'flex' : 'none'
					}}>
					<TouchableOpacity
						style={styles.menuBtn}
						onPress={() => this.setState({ open: false })}>
						<CloseIcon colour="#fff" />
					</TouchableOpacity>
					<ScrollView style={styles.items}>
						{this.props.pages?.map((page, i) =>
							<SideMenuItem
								key={i}
								isPage
								label={page.label}
								onClick={() => this.onClick(page.ref.current)}>
								{page.menuSections.map((section, ii) =>
									<SideMenuItem
										key={ii}
										label={section.label}
										onClick={() => this.onClick(section.ref.current)}>
										{section.menuFields.map((field, iii) =>
											<SideMenuItem
												key={iii}
												label={'-  ' + field.label}
												onClick={() => this.onClick(field.ref.current)}
												noChildren
											/>
										)}
									</SideMenuItem>
								)}
							</SideMenuItem>
						)}
					</ScrollView>
					<AppButton
						theme="green"
						width={scaleToDesign(440)}
						height={scaleToDesign(57)}
						rootStyle={{ marginLeft: scaleToDesign(30) }}
						content="Open Cheat Sheet" />
				</View>
			</View >
		);
	}

	private onClick(ref: View) {
		ref.measureLayout(
			findNodeHandle(this.props.scrollViewRef.current),
			(x, y) => {
				this.props.scrollViewRef.current.scrollToPosition(0, y, true);
			},
			() => { }
		);
		this.setState({ open: false });
	}
}
export default SideMenu as ReactComponent<PublicProps>;

interface Props extends PublicProps {
	pages: MenuPageDto[];
}

interface PublicProps {
	scrollViewRef: React.RefObject<KeyboardAwareScrollView>;
}

interface State {
	open: boolean;
}
