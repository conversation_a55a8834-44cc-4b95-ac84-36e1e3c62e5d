import React from 'react';
import Svg, { G, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class PolesIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Line x1="17.5" y1="0.997909822" x2="17.5" y2="34.0020902" stroke={this.props.colour} strokeWidth="2" />
					<Line
						x1="5.61849507"
						y1="12.8794148"
						x2="29.3815049"
						y2="12.8794148"
						stroke={this.props.colour}
						strokeWidth="2" />
					<Line
						x1="5.61849507"
						y1="7.59874589"
						x2="29.3815049"
						y2="7.59874589"
						stroke={this.props.colour}
						strokeWidth="2" />
					<Line
						x1="9.57899671"
						y1="7.59874589"
						x2="9.57899671"
						y2="3.63824425"
						stroke={this.props.colour}
						strokeWidth="2" />
					<Line
						x1="25.4210033"
						y1="7.59874589"
						x2="25.4210033"
						y2="3.63824425"
						stroke={this.props.colour}
						strokeWidth="2" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
