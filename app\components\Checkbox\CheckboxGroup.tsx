import React, { Component } from 'react';
import { View, Text, TouchableHighlight } from 'react-native';
import { string, number } from 'redi-ui-utils';
import styles from './styles';
import Checkbox from './Checkbox';

export default class CheckboxGroup<T> extends React.PureComponent<CheckboxGroupProps<T>, State> {

    constructor(props: CheckboxGroupProps<T>){
        super(props);
    }

    renderRow(values: Array<T>, key: any): React.ReactNode{
        return (
            <View style={[styles.rowWrapper, this.props.overrideStyles?.rowWrapper]} key={key}>
                {values.map((value, index) => 
                    <Checkbox
                    key={index} 
                    onChange={(selected) => this.props.onChange(selected, value)}
                    label={this.props.displayTransform ? this.props.displayTransform(value) : value as any}
                    selected={this.props.selected.indexOf(value) != -1}
                    overrideStyles={this.props.overrideStyles?.checkboxStyles || {}}
                />
                )}
            </View>
        );  
    }

	render() {

        let valuesPerRow: number = this.props.valuesPerRow ? this.props.valuesPerRow : 3;
        let rowCount: number = Math.floor((this.props.values.length + valuesPerRow - 1) / valuesPerRow);
        let rows: Array<React.ReactNode> = [];
        for(var ii = 0; ii < rowCount; ii++){
            rows.push(this.renderRow(this.props.values.slice(ii * valuesPerRow, (ii + 1) * valuesPerRow), ii));
        }

		return (
            <View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
               {rows.map(s => s)}
            </View>
		);
	}
}

export interface CheckboxGroupProps<T> {
    //This function is called whenever a checkbox is clicked.
    onChange: (selected: boolean, value: T) => void;
    //a loist of the options
    values: Array<T>;
    //a list of whcih options are selected. An empty array means none of the checkboxes are selected.
    selected: Array<T>;
    //number of values per row. Defaults to 3.
    valuesPerRow?: number;
    //optional function to transform the type of value to a string for display.
    displayTransform?: (value: T) => string;
    //optional. Ovveride the styles for this component
    overrideStyles?: {
        //The styles that go on the view that wraps the entire component
        wrapper?: object,
        //stylesthat go on the view that wraps each row
        rowWrapper?: object;
        //styles that get passed down inot each checkbox
        checkboxStyles: {
            //Styles that go on the view that the checkbox - inside of the touchable highlight
            checkboxWrapper?: object,
            //styles that go on the Text that displays the checbox label
            checkboxLabel?: object,
            //styles to go on the actual box part of the checkbox
            checkboxOuter?: object,
            //styles to go on the "check" part of the checkbox - does not actually need a check mark
            checkboxInner?: object
        }
    }
}

export interface CheckboxGroupDto<T>{
    selected: boolean;
    value: T;
}

interface State {

}