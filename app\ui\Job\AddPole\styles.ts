import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	button: {
		width: '100%',
		height: 60,
		borderRadius: 15,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: 10,
		backgroundColor: theme.PRIMARY_MEDIUM_BLUE
	},
	text: {
		color: theme.WHITE,
		fontSize: 22,
		fontWeight: 'bold'
	},
	message: {
		fontSize: 15,
		textAlign: 'center',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1.5,
		marginVertical: 20,
		color: theme.TEXT_DARK
	},
	assetCell: {
		flexDirection: 'row',
		justifyContent: 'flex-start'
	},
	assetName: {
		fontSize: 24,
		color: theme.TEXT_DARK,
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2.3,
		marginLeft: 18
	},
	suburb: {
		fontSize: 16,
		color: '#A0A0A0',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1,
		textAlign: 'right'
	},
	foundAsset: {
		marginTop: 20,
		flexDirection: 'row',
		backgroundColor: '#FAFAFA',
		borderRadius: 15,
		height: 53,
		marginVertical: 5,
		paddingHorizontal: 20,
		justifyContent: 'center',
		alignItems: 'center',
		textAlign: 'center',
		textAlignVertical: 'center'
	}
});
export default styles;