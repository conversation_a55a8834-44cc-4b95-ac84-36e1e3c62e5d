import { StoreBase } from 'utils/store-base';
import Database from 'services/database';
import { teamIdFilter } from 'utils/filter';
import { JobStatusCode } from 'dtos/job';
import { SyncingData } from './syncing-data';
import { map } from 'rxjs/operators';

class syncStore extends StoreBase<State> {
	constructor() {
		super('sync', {
			inProgress: 0,
			completed: 0,
			isSyncing: false,
			syncingProgress: 0
		});
	}

	readonly syncing$ = SyncingData.data$.pipe(map(x => x.length));
	readonly syncingProgress$ = SyncingData.data$.pipe(
		map(x => {
			const uploaded = x.reduce((pv, cv) => pv + cv.uploaded + (cv.dataUploaded ? 1 : 0), 0);
			const total = x.reduce((pv, cv) => pv + cv.total + 1, 0);
			const result = uploaded / total * 100;
			return isNaN(result) ? 0 : result;
		})
	);
	readonly inProgress$ = this.query.select(x => x.inProgress);
	readonly completed$ = this.query.select(x => x.completed);
	readonly isSyncing$ = this.query.select(x => x.isSyncing);

	async refreshCompletedAndInProgress(): Promise<void> {
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		const completedToday = await Database.select<{ count: number }>(`
			SELECT
				COUNT(*) as count
			FROM jobs
			${teamIdFilter()}
			AND statusCode = '${JobStatusCode.JComplete}'
			AND modifiedOnUtc > '${today.toISOString()}'
		`);

		const inProgress = await Database.select<{ count: number }>(`
			SELECT 
				COUNT(*) as count
			FROM jobs
			${teamIdFilter()}
			AND statusCode = '${JobStatusCode.JInProgress}'
		`);

		this.update({
			completed: completedToday[0].count,
			inProgress: inProgress[0].count
		});
	}
}
export const SyncStore = new syncStore();

interface State {
	inProgress: number;
	completed: number;
	syncingProgress: number;
	isSyncing: boolean;
}