import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';
import theme from 'config/styles/theme';

const styles = scaleAllToDesign({
	progress: {
		paddingTop: 30
	},
	asset: {
		paddingTop: 18
	},
	progressText: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginTop: 10
	},
	syncing: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'center',
		marginBottom: 30
	},
	text: {
		letterSpacing: 1.5,
		fontFamily: theme.FONT_REGULAR,
		color: theme.TEXT_DARK,
		fontSize: 16,
		marginLeft: 10
	}
});
export default styles;