import React from 'react';
import { View, Image, Text } from 'react-native';
import styles from './styles';

export default class Splash extends React.PureComponent<Props, never> {
	render() {
		const text = this.props.reset ? 'Resetting ...' : 'Loading ...';
		return (
			<View style={styles.root}>
				<Image
					style={styles.image}
					source={require('app/assets/logo.png')} />
				<Text style={styles.text}>{text}</Text>
			</View>
		);
	}
}


interface Props {
	reset?: boolean;
}