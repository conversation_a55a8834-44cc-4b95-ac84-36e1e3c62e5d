import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import DropdownMultiChoice, { DropdownMultiChoiceProps } from 'components/Dropdown/DropdownMultiChoice';

export default class FormDropdownMultiChoice extends React.Component<WrappedFieldInterface<string[], DropdownMultiChoiceProps<string>>, never> {
	constructor(props: any) {
		super(props);
		this.onChange = this.onChange.bind(this);
	}

	render() {
		const props = this.props.componentProps();

		return (
			<DropdownMultiChoice
				{...props}
				selected={this.props.fieldProps.value}
				onChange={this.onChange}
				error={this.props.form.field.error}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)} />
		);
	}

	private onChange(selected: boolean, value: string) {
		let newValues: Array<string> = [...this.props.fieldProps.value];
		if (selected) {
			newValues.push(value);
		} else {
			newValues = newValues.filter(s => s != value);
		}

		let orderedValues: Array<string> = [];
		for (var ii = 0; ii < this.props.values.length; ii++) {
			let wkValue: string = this.props.values[ii];
			if (newValues.indexOf(wkValue) != -1) {
				orderedValues.push(wkValue);
			}
		}
		this.props.fieldProps.onChange(orderedValues);
	}
}