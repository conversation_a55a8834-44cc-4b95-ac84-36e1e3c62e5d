import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	root: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		height: 60,
		paddingHorizontal: 15
	},
	selected: {
		backgroundColor: theme.WHITE,
		color: theme.PRIMARY_DARK_BLUE,
		borderRadius: 30
	},
	text: {
		fontSize: 22,
		color: theme.WHITE,
		textAlign: 'center',
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2,
		marginLeft: 10,
		marginBottom: 3
	}
});

export default styles;
