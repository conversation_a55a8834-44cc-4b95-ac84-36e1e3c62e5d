# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@angular-devkit/core@7.3.10", "@angular-devkit/core@^7.3.6":
  version "7.3.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@angular-devkit/core/-/@angular-devkit/core-7.3.10.tgz#35f34b54de35c7b3ffec37dd9bc88e369ebc29ea"
  integrity sha1-NfNLVN41x7P/7Dfdm8iONp68Keo=
  dependencies:
    ajv "6.9.1"
    chokidar "2.0.4"
    fast-json-stable-stringify "2.0.0"
    rxjs "6.3.3"
    source-map "0.7.3"

"@angular-devkit/schematics@^7.3.6":
  version "7.3.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@angular-devkit/schematics/-/@angular-devkit/schematics-7.3.10.tgz#2511595093c7d742b5d07acd40dccc6309e15817"
  integrity sha1-JRFZUJPH10K10HrNQNzMYwnhWBc=
  dependencies:
    "@angular-devkit/core" "7.3.10"
    rxjs "6.3.3"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/code-frame/-/@babel/code-frame-7.10.4.tgz#168da1a36e90da68ae8d49c0f1b48c7c6249213a"
  integrity sha1-Fo2ho26Q2miujUnA8bSMfGJJITo=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/core@^7.0.0", "@babel/core@^7.1.0", "@babel/core@^7.6.2":
  version "7.12.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/core/-/@babel/core-7.12.3.tgz#1b436884e1e3bff6fb1328dc02b208759de92ad8"
  integrity sha1-G0NohOHjv/b7EyjcArIIdZ3pKtg=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.12.1"
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helpers" "^7.12.1"
    "@babel/parser" "^7.12.3"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.2"
    lodash "^4.17.19"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.0.0", "@babel/generator@^7.12.1", "@babel/generator@^7.4.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/generator/-/@babel/generator-7.12.1.tgz#0d70be32bdaa03d7c51c8597dda76e0df1f15468"
  integrity sha1-DXC+Mr2qA9fFHIWX3aduDfHxVGg=
  dependencies:
    "@babel/types" "^7.12.1"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-annotate-as-pure/-/@babel/helper-annotate-as-pure-7.10.4.tgz#5bf0d495a3f757ac3bda48b5bf3b3ba309c72ba3"
  integrity sha1-W/DUlaP3V6w72ki1vzs7ownHK6M=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/@babel/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz#bb0b75f31bf98cbf9ff143c1ae578b87274ae1a3"
  integrity sha1-uwt18xv5jL+f8UPBrleLhydK4aM=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-builder-react-jsx-experimental@^7.12.1":
  version "7.12.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-builder-react-jsx-experimental/-/@babel/helper-builder-react-jsx-experimental-7.12.4.tgz#55fc1ead5242caa0ca2875dcb8eed6d311e50f48"
  integrity sha1-VfwerVJCyqDKKHXcuO7W0xHlD0g=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/types" "^7.12.1"

"@babel/helper-builder-react-jsx@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-builder-react-jsx/-/@babel/helper-builder-react-jsx-7.10.4.tgz#8095cddbff858e6fa9c326daee54a2f2732c1d5d"
  integrity sha1-gJXN2/+Fjm+pwyba7lSi8nMsHV0=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-create-class-features-plugin@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-create-class-features-plugin/-/@babel/helper-create-class-features-plugin-7.12.1.tgz#3c45998f431edd4a9214c5f1d3ad1448a6137f6e"
  integrity sha1-PEWZj0Me3UqSFMXx060USKYTf24=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-member-expression-to-functions" "^7.12.1"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.10.4"

"@babel/helper-create-regexp-features-plugin@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-create-regexp-features-plugin/-/@babel/helper-create-regexp-features-plugin-7.12.1.tgz#18b1302d4677f9dc4740fe8c9ed96680e29d37e8"
  integrity sha1-GLEwLUZ3+dxHQP6MntlmgOKdN+g=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"
    regexpu-core "^4.7.1"

"@babel/helper-define-map@^7.10.4":
  version "7.10.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-define-map/-/@babel/helper-define-map-7.10.5.tgz#b53c10db78a640800152692b13393147acb9bb30"
  integrity sha1-tTwQ23imQIABUmkrEzkxR6y5uzA=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/types" "^7.10.5"
    lodash "^4.17.19"

"@babel/helper-explode-assignable-expression@^7.10.4":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-explode-assignable-expression/-/@babel/helper-explode-assignable-expression-7.12.1.tgz#8006a466695c4ad86a2a5f2fb15b5f2c31ad5633"
  integrity sha1-gAakZmlcSthqKl8vsVtfLDGtVjM=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-function-name@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-function-name/-/@babel/helper-function-name-7.10.4.tgz#d2d3b20c59ad8c47112fa7d2a94bc09d5ef82f1a"
  integrity sha1-0tOyDFmtjEcRL6fSqUvAnV74Lxo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-get-function-arity@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-get-function-arity/-/@babel/helper-get-function-arity-7.10.4.tgz#98c1cbea0e2332f33f9a4661b8ce1505b2c19ba2"
  integrity sha1-mMHL6g4jMvM/mkZhuM4VBbLBm6I=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-member-expression-to-functions@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-member-expression-to-functions/-/@babel/helper-member-expression-to-functions-7.12.1.tgz#fba0f2fcff3fba00e6ecb664bb5e6e26e2d6165c"
  integrity sha1-+6Dy/P8/ugDm7LZku15uJuLWFlw=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-module-imports@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-module-imports/-/@babel/helper-module-imports-7.12.1.tgz#1644c01591a15a2f084dd6d092d9430eb1d1216c"
  integrity sha1-FkTAFZGhWi8ITdbQktlDDrHRIWw=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-module-transforms@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-module-transforms/-/@babel/helper-module-transforms-7.12.1.tgz#7954fec71f5b32c48e4b303b437c34453fd7247c"
  integrity sha1-eVT+xx9bMsSOSzA7Q3w0RT/XJHw=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-simple-access" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/helper-validator-identifier" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"
    lodash "^4.17.19"

"@babel/helper-optimise-call-expression@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-optimise-call-expression/-/@babel/helper-optimise-call-expression-7.10.4.tgz#50dc96413d594f995a77905905b05893cd779673"
  integrity sha1-UNyWQT1ZT5lad5BZBbBYk813lnM=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-plugin-utils/-/@babel/helper-plugin-utils-7.10.4.tgz#2f75a831269d4f677de49986dff59927533cf375"
  integrity sha1-L3WoMSadT2d95JmG3/WZJ1M883U=

"@babel/helper-regex@^7.10.4":
  version "7.10.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-regex/-/@babel/helper-regex-7.10.5.tgz#32dfbb79899073c415557053a19bd055aae50ae0"
  integrity sha1-Mt+7eYmQc8QVVXBToZvQVarlCuA=
  dependencies:
    lodash "^4.17.19"

"@babel/helper-remap-async-to-generator@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-remap-async-to-generator/-/@babel/helper-remap-async-to-generator-7.12.1.tgz#8c4dbbf916314f6047dc05e6a2217074238347fd"
  integrity sha1-jE27+RYxT2BH3AXmoiFwdCODR/0=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-wrap-function" "^7.10.4"
    "@babel/types" "^7.12.1"

"@babel/helper-replace-supers@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-replace-supers/-/@babel/helper-replace-supers-7.12.1.tgz#f15c9cc897439281891e11d5ce12562ac0cf3fa9"
  integrity sha1-8VycyJdDkoGJHhHVzhJWKsDPP6k=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.12.1"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"

"@babel/helper-simple-access@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-simple-access/-/@babel/helper-simple-access-7.12.1.tgz#32427e5aa61547d38eb1e6eaf5fd1426fdad9136"
  integrity sha1-MkJ+WqYVR9OOsebq9f0UJv2tkTY=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-skip-transparent-expression-wrappers/-/@babel/helper-skip-transparent-expression-wrappers-7.12.1.tgz#462dc63a7e435ade8468385c63d2b84cce4b3cbf"
  integrity sha1-Ri3GOn5DWt6EaDhcY9K4TM5LPL8=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-split-export-declaration@^7.10.4", "@babel/helper-split-export-declaration@^7.11.0":
  version "7.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-split-export-declaration/-/@babel/helper-split-export-declaration-7.11.0.tgz#f8a491244acf6a676158ac42072911ba83ad099f"
  integrity sha1-+KSRJErPamdhWKxCBykRuoOtCZ8=
  dependencies:
    "@babel/types" "^7.11.0"

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-validator-identifier/-/@babel/helper-validator-identifier-7.10.4.tgz#a78c7a7251e01f616512d31b10adcf52ada5e0d2"
  integrity sha1-p4x6clHgH2FlEtMbEK3PUq2l4NI=

"@babel/helper-wrap-function@^7.10.4":
  version "7.12.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helper-wrap-function/-/@babel/helper-wrap-function-7.12.3.tgz#3332339fc4d1fbbf1c27d7958c27d34708e990d9"
  integrity sha1-MzIzn8TR+78cJ9eVjCfTRwjpkNk=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helpers@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/helpers/-/@babel/helpers-7.12.1.tgz#8a8261c1d438ec18cb890434df4ec768734c1e79"
  integrity sha1-ioJhwdQ47BjLiQQ0307HaHNMHnk=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/highlight/-/@babel/highlight-7.10.4.tgz#7d1bdfd65753538fabe6c38596cdb76d9ac60143"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.1.0", "@babel/parser@^7.10.4", "@babel/parser@^7.12.1", "@babel/parser@^7.12.3", "@babel/parser@^7.4.3":
  version "7.12.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/parser/-/@babel/parser-7.12.3.tgz#a305415ebe7a6c7023b40b5122a0662d928334cd"
  integrity sha1-owVBXr56bHAjtAtRIqBmLZKDNM0=

"@babel/plugin-external-helpers@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-external-helpers/-/@babel/plugin-external-helpers-7.12.1.tgz#df474775860b3b8bdfeaedd45596cd2c7f36a2be"
  integrity sha1-30dHdYYLO4vf6u3UVZbNLH82or4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-class-properties@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-class-properties/-/@babel/plugin-proposal-class-properties-7.12.1.tgz#a082ff541f2a29a4821065b8add9346c0c16e5de"
  integrity sha1-oIL/VB8qKaSCEGW4rdk0bAwW5d4=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-decorators@^7.8.3":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-decorators/-/@babel/plugin-proposal-decorators-7.12.1.tgz#59271439fed4145456c41067450543aee332d15f"
  integrity sha1-WScUOf7UFFRWxBBnRQVDruMy0V8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-decorators" "^7.12.1"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-export-default-from/-/@babel/plugin-proposal-export-default-from-7.12.1.tgz#c6e62d668a8abcfe0d28b82f560395fecb611c5a"
  integrity sha1-xuYtZoqKvP4NKLgvVgOV/sthHFo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-export-default-from" "^7.12.1"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/@babel/plugin-proposal-nullish-coalescing-operator-7.12.1.tgz#3ed4fff31c015e7f3f1467f190dbe545cd7b046c"
  integrity sha1-PtT/8xwBXn8/FGfxkNvlRc17BGw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"

"@babel/plugin-proposal-object-rest-spread@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-object-rest-spread/-/@babel/plugin-proposal-object-rest-spread-7.12.1.tgz#def9bd03cea0f9b72283dac0ec22d289c7691069"
  integrity sha1-3vm9A86g+bcig9rA7CLSicdpEGk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.12.1"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-optional-catch-binding/-/@babel/plugin-proposal-optional-catch-binding-7.12.1.tgz#ccc2421af64d3aae50b558a71cede929a5ab2942"
  integrity sha1-zMJCGvZNOq5QtVinHO3pKaWrKUI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-optional-chaining@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-proposal-optional-chaining/-/@babel/plugin-proposal-optional-chaining-7.12.1.tgz#cce122203fc8a32794296fc377c6dedaf4363797"
  integrity sha1-zOEiID/IoyeUKW/Dd8be2vQ2N5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-class-properties/-/@babel/plugin-syntax-class-properties-7.12.1.tgz#bcb297c5366e79bebadef509549cd93b04f19978"
  integrity sha1-vLKXxTZueb663vUJVJzZOwTxmXg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-decorators@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-decorators/-/@babel/plugin-syntax-decorators-7.12.1.tgz#81a8b535b284476c41be6de06853a8802b98c5dd"
  integrity sha1-gai1NbKER2xBvm3gaFOogCuYxd0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-dynamic-import/-/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-export-default-from/-/@babel/plugin-syntax-export-default-from-7.12.1.tgz#a9eb31881f4f9a1115a3d2c6d64ac3f6016b5a9d"
  integrity sha1-qesxiB9PmhEVo9LG1krD9gFrWp0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.12.1", "@babel/plugin-syntax-flow@^7.2.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-flow/-/@babel/plugin-syntax-flow-7.12.1.tgz#a77670d9abe6d63e8acadf4c31bb1eb5a506bbdd"
  integrity sha1-p3Zw2avm1j6Kyt9MMbsetaUGu90=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-jsx/-/@babel/plugin-syntax-jsx-7.12.1.tgz#9d9d357cc818aa7ae7935917c1257f67677a0926"
  integrity sha1-nZ01fMgYqnrnk1kXwSV/Z2d6CSY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.0":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.0":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-object-rest-spread/-/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.0":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-optional-catch-binding/-/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.0":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-optional-chaining/-/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-typescript@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-syntax-typescript/-/@babel/plugin-syntax-typescript-7.12.1.tgz#460ba9d77077653803c3dd2e673f76d66b4029e5"
  integrity sha1-Rgup13B3ZTgDw90uZz921mtAKeU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-arrow-functions@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-arrow-functions/-/@babel/plugin-transform-arrow-functions-7.12.1.tgz#8083ffc86ac8e777fbe24b5967c4b2521f3cb2b3"
  integrity sha1-gIP/yGrI53f74ktZZ8SyUh88srM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-async-to-generator@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-async-to-generator/-/@babel/plugin-transform-async-to-generator-7.12.1.tgz#3849a49cc2a22e9743cbd6b52926d30337229af1"
  integrity sha1-OEmknMKiLpdDy9a1KSbTAzcimvE=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.12.1"

"@babel/plugin-transform-block-scoped-functions@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-block-scoped-functions/-/@babel/plugin-transform-block-scoped-functions-7.12.1.tgz#f2a1a365bde2b7112e0a6ded9067fdd7c07905d9"
  integrity sha1-8qGjZb3itxEuCm3tkGf918B5Bdk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-block-scoping@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-block-scoping/-/@babel/plugin-transform-block-scoping-7.12.1.tgz#f0ee727874b42a208a48a586b84c3d222c2bbef1"
  integrity sha1-8O5yeHS0KiCKSKWGuEw9IiwrvvE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-classes@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-classes/-/@babel/plugin-transform-classes-7.12.1.tgz#65e650fcaddd3d88ddce67c0f834a3d436a32db6"
  integrity sha1-ZeZQ/K3dPYjdzmfA+DSj1DajLbY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-define-map" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.10.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-computed-properties/-/@babel/plugin-transform-computed-properties-7.12.1.tgz#d68cf6c9b7f838a8a4144badbe97541ea0904852"
  integrity sha1-1oz2ybf4OKikFEutvpdUHqCQSFI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-destructuring@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-destructuring/-/@babel/plugin-transform-destructuring-7.12.1.tgz#b9a570fe0d0a8d460116413cb4f97e8e08b2f847"
  integrity sha1-uaVw/g0KjUYBFkE8tPl+jgiy+Ec=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-exponentiation-operator@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-exponentiation-operator/-/@babel/plugin-transform-exponentiation-operator-7.12.1.tgz#b0f2ed356ba1be1428ecaf128ff8a24f02830ae0"
  integrity sha1-sPLtNWuhvhQo7K8Sj/iiTwKDCuA=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-flow-strip-types@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-flow-strip-types/-/@babel/plugin-transform-flow-strip-types-7.12.1.tgz#8430decfa7eb2aea5414ed4a3fa6e1652b7d77c4"
  integrity sha1-hDDez6frKupUFO1KP6bhZSt9d8Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-flow" "^7.12.1"

"@babel/plugin-transform-for-of@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-for-of/-/@babel/plugin-transform-for-of-7.12.1.tgz#07640f28867ed16f9511c99c888291f560921cfa"
  integrity sha1-B2QPKIZ+0W+VEcmciIKR9WCSHPo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-function-name@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-function-name/-/@babel/plugin-transform-function-name-7.12.1.tgz#2ec76258c70fe08c6d7da154003a480620eba667"
  integrity sha1-LsdiWMcP4IxtfaFUADpIBiDrpmc=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-literals@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-literals/-/@babel/plugin-transform-literals-7.12.1.tgz#d73b803a26b37017ddf9d3bb8f4dc58bfb806f57"
  integrity sha1-1zuAOiazcBfd+dO7j03Fi/uAb1c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-member-expression-literals/-/@babel/plugin-transform-member-expression-literals-7.12.1.tgz#496038602daf1514a64d43d8e17cbb2755e0c3ad"
  integrity sha1-SWA4YC2vFRSmTUPY4Xy7J1Xgw60=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-modules-commonjs@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-modules-commonjs/-/@babel/plugin-transform-modules-commonjs-7.12.1.tgz#fa403124542636c786cf9b460a0ffbb48a86e648"
  integrity sha1-+kAxJFQmNseGz5tGCg/7tIqG5kg=
  dependencies:
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-simple-access" "^7.12.1"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-object-assign@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-object-assign/-/@babel/plugin-transform-object-assign-7.12.1.tgz#9102b06625f60a5443cc292d32b565373665e1e4"
  integrity sha1-kQKwZiX2ClRDzCktMrVlNzZl4eQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-object-super@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-object-super/-/@babel/plugin-transform-object-super-7.12.1.tgz#4ea08696b8d2e65841d0c7706482b048bed1066e"
  integrity sha1-TqCGlrjS5lhB0MdwZIKwSL7RBm4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.12.1":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-parameters/-/@babel/plugin-transform-parameters-7.12.1.tgz#d2e963b038771650c922eff593799c96d853255d"
  integrity sha1-0uljsDh3FlDJIu/1k3mclthTJV0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-property-literals@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-property-literals/-/@babel/plugin-transform-property-literals-7.12.1.tgz#41bc81200d730abb4456ab8b3fbd5537b59adecd"
  integrity sha1-QbyBIA1zCrtEVquLP71VN7Wa3s0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-react-display-name/-/@babel/plugin-transform-react-display-name-7.12.1.tgz#1cbcd0c3b1d6648c55374a22fc9b6b7e5341c00d"
  integrity sha1-HLzQw7HWZIxVN0oi/JtrflNBwA0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-react-jsx-source/-/@babel/plugin-transform-react-jsx-source-7.12.1.tgz#d07de6863f468da0809edcf79a1aa8ce2a82a26b"
  integrity sha1-0H3mhj9GjaCAntz3mhqoziqComs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-react-jsx/-/@babel/plugin-transform-react-jsx-7.12.1.tgz#c2d96c77c2b0e4362cc4e77a43ce7c2539d478cb"
  integrity sha1-wtlsd8Kw5DYsxOd6Q858JTnUeMs=
  dependencies:
    "@babel/helper-builder-react-jsx" "^7.10.4"
    "@babel/helper-builder-react-jsx-experimental" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-jsx" "^7.12.1"

"@babel/plugin-transform-regenerator@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-regenerator/-/@babel/plugin-transform-regenerator-7.12.1.tgz#5f0a28d842f6462281f06a964e88ba8d7ab49753"
  integrity sha1-Xwoo2EL2RiKB8GqWToi6jXq0l1M=
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-runtime@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-runtime/-/@babel/plugin-transform-runtime-7.12.1.tgz#04b792057eb460389ff6a4198e377614ea1e7ba5"
  integrity sha1-BLeSBX60YDif9qQZjjd2FOoee6U=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-shorthand-properties/-/@babel/plugin-transform-shorthand-properties-7.12.1.tgz#0bf9cac5550fce0cfdf043420f661d645fdc75e3"
  integrity sha1-C/nKxVUPzgz98ENCD2YdZF/cdeM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-spread@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-spread/-/@babel/plugin-transform-spread-7.12.1.tgz#527f9f311be4ec7fdc2b79bb89f7bf884b3e1e1e"
  integrity sha1-Un+fMRvk7H/cK3m7ife/iEs+Hh4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"

"@babel/plugin-transform-sticky-regex@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-sticky-regex/-/@babel/plugin-transform-sticky-regex-7.12.1.tgz#5c24cf50de396d30e99afc8d1c700e8bce0f5caf"
  integrity sha1-XCTPUN45bTDpmvyNHHAOi84PXK8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"

"@babel/plugin-transform-template-literals@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-template-literals/-/@babel/plugin-transform-template-literals-7.12.1.tgz#b43ece6ed9a79c0c71119f576d299ef09d942843"
  integrity sha1-tD7ObtmnnAxxEZ9XbSme8J2UKEM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-typescript@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-typescript/-/@babel/plugin-transform-typescript-7.12.1.tgz#d92cc0af504d510e26a754a7dbc2e5c8cd9c7ab4"
  integrity sha1-2SzAr1BNUQ4mp1Sn28LlyM2cerQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-typescript" "^7.12.1"

"@babel/plugin-transform-unicode-regex@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/plugin-transform-unicode-regex/-/@babel/plugin-transform-unicode-regex-7.12.1.tgz#cc9661f61390db5c65e3febaccefd5c6ac3faecb"
  integrity sha1-zJZh9hOQ21xl4/66zO/Vxqw/rss=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/register@^7.0.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/register/-/@babel/register-7.12.1.tgz#cdb087bdfc4f7241c03231f22e15d211acf21438"
  integrity sha1-zbCHvfxPckHAMjHyLhXSEazyFDg=
  dependencies:
    find-cache-dir "^2.0.0"
    lodash "^4.17.19"
    make-dir "^2.1.0"
    pirates "^4.0.0"
    source-map-support "^0.5.16"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.6.2", "@babel/runtime@^7.8.4":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/runtime/-/@babel/runtime-7.12.1.tgz#b4116a6b6711d010b2dad3b7b6e43bf1b9954740"
  integrity sha1-tBFqa2cR0BCy2tO3tuQ78bmVR0A=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.10.4", "@babel/template@^7.4.0":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/template/-/@babel/template-7.10.4.tgz#3251996c4200ebc71d1a8fc405fba940f36ba278"
  integrity sha1-MlGZbEIA68cdGo/EBfupQPNrong=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.10.4", "@babel/traverse@^7.12.1", "@babel/traverse@^7.4.3":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/traverse/-/@babel/traverse-7.12.1.tgz#941395e0c5cc86d5d3e75caa095d3924526f0c1e"
  integrity sha1-lBOV4MXMhtXT51yqCV05JFJvDB4=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.12.1"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/parser" "^7.12.1"
    "@babel/types" "^7.12.1"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/types@^7.0.0", "@babel/types@^7.10.4", "@babel/types@^7.10.5", "@babel/types@^7.11.0", "@babel/types@^7.12.1", "@babel/types@^7.3.0", "@babel/types@^7.4.0":
  version "7.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@babel/types/-/@babel/types-7.12.1.tgz#e109d9ab99a8de735be287ee3d6a9947a190c4ae"
  integrity sha1-4QnZq5mo3nNb4ofuPWqZR6GQxK4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@cnakazawa/watch/-/@cnakazawa/watch-1.0.4.tgz#f864ae85004d0fcab6f50be9141c4da368d1656a"
  integrity sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@datorama/akita@^4.18.1":
  version "4.23.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@datorama/akita/-/@datorama/akita-4.23.2.tgz#294fa703a2977e079735e15196aa9e690ca44feb"
  integrity sha1-KU+nA6KXfgeXNeFRlqqeaQykT+s=
  dependencies:
    schematics-utilities "^1.1.1"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@hapi/address/-/@hapi/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@hapi/bourne/-/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@hapi/hoek/-/@hapi/hoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.3":
  version "15.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@hapi/joi/-/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@hapi/topo/-/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@jest/console@^24.7.1", "@jest/console@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/console/-/@jest/console-24.9.0.tgz#79b1bc06fb74a8cfb01cbdedf945584b1b9707f0"
  integrity sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A=
  dependencies:
    "@jest/source-map" "^24.9.0"
    chalk "^2.0.1"
    slash "^2.0.0"

"@jest/core@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/core/-/@jest/core-24.9.0.tgz#2ceccd0b93181f9c4850e74f2a9ad43d351369c4"
  integrity sha1-LOzNC5MYH5xIUOdPKprUPTUTacQ=
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/reporters" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    graceful-fs "^4.1.15"
    jest-changed-files "^24.9.0"
    jest-config "^24.9.0"
    jest-haste-map "^24.9.0"
    jest-message-util "^24.9.0"
    jest-regex-util "^24.3.0"
    jest-resolve "^24.9.0"
    jest-resolve-dependencies "^24.9.0"
    jest-runner "^24.9.0"
    jest-runtime "^24.9.0"
    jest-snapshot "^24.9.0"
    jest-util "^24.9.0"
    jest-validate "^24.9.0"
    jest-watcher "^24.9.0"
    micromatch "^3.1.10"
    p-each-series "^1.0.0"
    realpath-native "^1.1.0"
    rimraf "^2.5.4"
    slash "^2.0.0"
    strip-ansi "^5.0.0"

"@jest/environment@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/environment/-/@jest/environment-24.9.0.tgz#21e3afa2d65c0586cbd6cbefe208bafade44ab18"
  integrity sha1-IeOvotZcBYbL1svv4gi6+t5Eqxg=
  dependencies:
    "@jest/fake-timers" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    jest-mock "^24.9.0"

"@jest/fake-timers@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/fake-timers/-/@jest/fake-timers-24.9.0.tgz#ba3e6bf0eecd09a636049896434d306636540c93"
  integrity sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM=
  dependencies:
    "@jest/types" "^24.9.0"
    jest-message-util "^24.9.0"
    jest-mock "^24.9.0"

"@jest/reporters@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/reporters/-/@jest/reporters-24.9.0.tgz#86660eff8e2b9661d042a8e98a028b8d631a5b43"
  integrity sha1-hmYO/44rlmHQQqjpigKLjWMaW0M=
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    glob "^7.1.2"
    istanbul-lib-coverage "^2.0.2"
    istanbul-lib-instrument "^3.0.1"
    istanbul-lib-report "^2.0.4"
    istanbul-lib-source-maps "^3.0.1"
    istanbul-reports "^2.2.6"
    jest-haste-map "^24.9.0"
    jest-resolve "^24.9.0"
    jest-runtime "^24.9.0"
    jest-util "^24.9.0"
    jest-worker "^24.6.0"
    node-notifier "^5.4.2"
    slash "^2.0.0"
    source-map "^0.6.0"
    string-length "^2.0.0"

"@jest/source-map@^24.3.0", "@jest/source-map@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/source-map/-/@jest/source-map-24.9.0.tgz#0e263a94430be4b41da683ccc1e6bffe2a191714"
  integrity sha1-DiY6lEML5LQdpoPMwea//ioZFxQ=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.1.15"
    source-map "^0.6.0"

"@jest/test-result@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/test-result/-/@jest/test-result-24.9.0.tgz#11796e8aa9dbf88ea025757b3152595ad06ba0ca"
  integrity sha1-EXluiqnb+I6gJXV7MVJZWtBroMo=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/istanbul-lib-coverage" "^2.0.0"

"@jest/test-sequencer@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/test-sequencer/-/@jest/test-sequencer-24.9.0.tgz#f8f334f35b625a4f2f355f2fe7e6036dad2e6b31"
  integrity sha1-+PM081tiWk8vNV8v5+YDba0uazE=
  dependencies:
    "@jest/test-result" "^24.9.0"
    jest-haste-map "^24.9.0"
    jest-runner "^24.9.0"
    jest-runtime "^24.9.0"

"@jest/transform@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/transform/-/@jest/transform-24.9.0.tgz#4ae2768b296553fadab09e9ec119543c90b16c56"
  integrity sha1-SuJ2iyllU/rasJ6ewRlUPJCxbFY=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^24.9.0"
    babel-plugin-istanbul "^5.1.0"
    chalk "^2.0.1"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.1.15"
    jest-haste-map "^24.9.0"
    jest-regex-util "^24.9.0"
    jest-util "^24.9.0"
    micromatch "^3.1.10"
    pirates "^4.0.1"
    realpath-native "^1.1.0"
    slash "^2.0.0"
    source-map "^0.6.1"
    write-file-atomic "2.4.1"

"@jest/types@^24.9.0":
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/types/-/@jest/types-24.9.0.tgz#63cb26cb7500d069e5a389441a7c6ab5e909fc59"
  integrity sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@jest/types@^25.5.0":
  version "25.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@jest/types/-/@jest/types-25.5.0.tgz#4d6a4793f7b9599fc3680877b856a97dbccf2a9d"
  integrity sha1-TWpHk/e5WZ/DaAh3uFapfbzPKp0=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"

"@mapbox/geo-viewport@^0.4.0":
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@mapbox/geo-viewport/-/@mapbox/geo-viewport-0.4.1.tgz#a184c0b161975858a2e855a1e333e66af342964b"
  integrity sha1-oYTAsWGXWFii6FWh4zPmavNClks=
  dependencies:
    "@mapbox/sphericalmercator" "~1.1.0"

"@mapbox/sphericalmercator@~1.1.0":
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@mapbox/sphericalmercator/-/@mapbox/sphericalmercator-1.1.0.tgz#f3b1af042620716a1289fc41e1e97f610823aefe"
  integrity sha1-87GvBCYgcWoSifxB4el/YQgjrv4=

"@nodelib/fs.scandir@2.1.3":
  version "2.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@nodelib/fs.scandir/-/@nodelib/fs.scandir-2.1.3.tgz#3a582bdb53804c6ba6d146579c46e52130cf4a3b"
  integrity sha1-Olgr21OATGum0UZXnEblITDPSjs=
  dependencies:
    "@nodelib/fs.stat" "2.0.3"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.3", "@nodelib/fs.stat@^2.0.2":
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@nodelib/fs.stat/-/@nodelib/fs.stat-2.0.3.tgz#34dc5f4cabbc720f4e60f75a747e7ecd6c175bd3"
  integrity sha1-NNxfTKu8cg9OYPdadH5+zWwXW9M=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@nodelib/fs.walk/-/@nodelib/fs.walk-1.2.4.tgz#011b9202a70a6366e436ca5c065844528ab04976"
  integrity sha1-ARuSAqcKY2bkNspcBlhEUoqwSXY=
  dependencies:
    "@nodelib/fs.scandir" "2.1.3"
    fastq "^1.6.0"

"@react-native-community/cli-debugger-ui@^3.0.0":
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli-debugger-ui/-/@react-native-community/cli-debugger-ui-3.0.0.tgz#d01d08d1e5ddc1633d82c7d84d48fff07bd39416"
  integrity sha1-0B0I0eXdwWM9gsfYTUj/8HvTlBY=
  dependencies:
    serve-static "^1.13.1"

"@react-native-community/cli-platform-android@^3.0.0":
  version "3.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli-platform-android/-/@react-native-community/cli-platform-android-3.1.4.tgz#61f964dc311623e60b0fb29c5f3732cc8a6f076f"
  integrity sha1-Yflk3DEWI+YLD7KcXzcyzIpvB28=
  dependencies:
    "@react-native-community/cli-tools" "^3.0.0"
    chalk "^2.4.2"
    execa "^1.0.0"
    jetifier "^1.6.2"
    logkitty "^0.6.0"
    slash "^3.0.0"
    xmldoc "^1.1.2"

"@react-native-community/cli-platform-ios@^3.0.0":
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli-platform-ios/-/@react-native-community/cli-platform-ios-3.2.0.tgz#c469444f5993c9e6737a4b16d78cf033e3702f00"
  integrity sha1-xGlET1mTyeZzeksW14zwM+NwLwA=
  dependencies:
    "@react-native-community/cli-tools" "^3.0.0"
    chalk "^2.4.2"
    js-yaml "^3.13.1"
    xcode "^2.0.0"

"@react-native-community/cli-tools@^3.0.0":
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli-tools/-/@react-native-community/cli-tools-3.0.0.tgz#fe48b80822ed7e49b8af051f9fe41e22a2a710b1"
  integrity sha1-/ki4CCLtfkm4rwUfn+QeIqKnELE=
  dependencies:
    chalk "^2.4.2"
    lodash "^4.17.5"
    mime "^2.4.1"
    node-fetch "^2.5.0"

"@react-native-community/cli-types@^3.0.0":
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli-types/-/@react-native-community/cli-types-3.0.0.tgz#488d46605cb05e88537e030f38da236eeda74652"
  integrity sha1-SI1GYFywXohTfgMPONojbu2nRlI=

"@react-native-community/cli@^3.0.0":
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/cli/-/@react-native-community/cli-3.2.1.tgz#2a466801eb6080a1f73358c5d740c53c24ed8c6f"
  integrity sha1-KkZoAetggKH3M1jF10DFPCTtjG8=
  dependencies:
    "@hapi/joi" "^15.0.3"
    "@react-native-community/cli-debugger-ui" "^3.0.0"
    "@react-native-community/cli-tools" "^3.0.0"
    "@react-native-community/cli-types" "^3.0.0"
    chalk "^2.4.2"
    command-exists "^1.2.8"
    commander "^2.19.0"
    compression "^1.7.1"
    connect "^3.6.5"
    cosmiconfig "^5.1.0"
    deepmerge "^3.2.0"
    didyoumean "^1.2.1"
    envinfo "^7.1.0"
    errorhandler "^1.5.0"
    execa "^1.0.0"
    find-up "^4.1.0"
    fs-extra "^7.0.1"
    glob "^7.1.1"
    graceful-fs "^4.1.3"
    inquirer "^3.0.6"
    lodash "^4.17.5"
    metro "^0.56.0"
    metro-config "^0.56.0"
    metro-core "^0.56.0"
    metro-react-native-babel-transformer "^0.56.0"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    morgan "^1.9.0"
    node-notifier "^5.2.1"
    open "^6.2.0"
    ora "^3.4.0"
    plist "^3.0.0"
    pretty-format "^25.1.0"
    semver "^6.3.0"
    serve-static "^1.13.1"
    shell-quote "1.6.1"
    strip-ansi "^5.2.0"
    sudo-prompt "^9.0.0"
    wcwidth "^1.0.1"
    ws "^1.1.0"

"@react-native-community/datetimepicker@^2.2.3":
  version "2.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/datetimepicker/-/@react-native-community/datetimepicker-2.6.2.tgz#552f703638ae8da9389d8610f9a880940d3a6192"
  integrity sha1-VS9wNjiujak4nYYQ+aiAlA06YZI=
  dependencies:
    invariant "^2.2.4"
  optionalDependencies:
    react-native-windows "^0.62.0-0"

"@react-native-community/eslint-config@^0.0.5":
  version "0.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@react-native-community/eslint-config/-/@react-native-community/eslint-config-0.0.5.tgz#584f6493258202a57efc22e7be66966e43832795"
  integrity sha1-WE9kkyWCAqV+/CLnvmaWbkODJ5U=
  dependencies:
    "@typescript-eslint/eslint-plugin" "^1.5.0"
    "@typescript-eslint/parser" "^1.5.0"
    babel-eslint "10.0.1"
    eslint-plugin-eslint-comments "^3.1.1"
    eslint-plugin-flowtype "2.50.3"
    eslint-plugin-jest "22.4.1"
    eslint-plugin-prettier "2.6.2"
    eslint-plugin-react "7.12.4"
    eslint-plugin-react-hooks "^1.5.1"
    eslint-plugin-react-native "3.6.0"
    prettier "1.16.4"

"@types/babel__core@^7.1.0":
  version "7.1.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/babel__core/-/@types/babel__core-7.1.10.tgz#ca58fc195dd9734e77e57c6f2df565623636ab40"
  integrity sha1-ylj8GV3Zc0535XxvLfVlYjY2q0A=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/babel__generator/-/@types/babel__generator-7.6.2.tgz#f3d71178e187858f7c45e30380f8f1b7415a12d8"
  integrity sha1-89cReOGHhY98ReMDgPjxt0FaEtg=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/babel__template/-/@types/babel__template-7.0.3.tgz#b8aaeba0a45caca7b56a5de9459872dde3727214"
  integrity sha1-uKrroKRcrKe1al3pRZhy3eNychQ=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.0.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/babel__traverse/-/@types/babel__traverse-7.0.15.tgz#db9e4238931eb69ef8aab0ad6523d4d4caa39d03"
  integrity sha1-255COJMetp74qrCtZSPU1MqjnQM=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/eslint-visitor-keys/-/@types/eslint-visitor-keys-1.0.0.tgz#1ee30d79544ca84d68d4b3cdb0af4f205663dd2d"
  integrity sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0=

"@types/history@*", "@types/history@^4.7.4":
  version "4.7.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/history/-/@types/history-4.7.8.tgz#49348387983075705fe8f4e02fb67f7daaec4934"
  integrity sha1-STSDh5gwdXBf6PTgL7Z/farsSTQ=

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/istanbul-lib-coverage/-/@types/istanbul-lib-coverage-2.0.3.tgz#4ba8ddb720221f432e443bd5f9117fd22cfd4762"
  integrity sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/istanbul-lib-report/-/@types/istanbul-lib-report-3.0.0.tgz#c14c24f18ea8190c118ee7562b7ff99a36552686"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/istanbul-reports/-/@types/istanbul-reports-1.1.2.tgz#e875cc689e47bce549ec81f3df5e6f6f11cfaeb2"
  integrity sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI=
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/jest@^24.0.24":
  version "24.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/jest/-/@types/jest-24.9.1.tgz#02baf9573c78f1b9974a5f36778b366aa77bd534"
  integrity sha1-Arr5Vzx48bmXSl82d4s2aqd71TQ=
  dependencies:
    jest-diff "^24.3.0"

"@types/json-schema@^7.0.3":
  version "7.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/json-schema/-/@types/json-schema-7.0.6.tgz#f4c7ec43e81b319a9815115031709f26987891f0"
  integrity sha1-9MfsQ+gbMZqYFRFQMXCfJph4kfA=

"@types/prop-types@*":
  version "15.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/prop-types/-/@types/prop-types-15.7.3.tgz#2ab0d5da2e5815f94b0b9d4b95d1e5f243ab2ca7"
  integrity sha1-KrDV2i5YFflLC51LldHl8kOrLKc=

"@types/react-native-canvas@^0.1.2":
  version "0.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-native-canvas/-/@types/react-native-canvas-0.1.5.tgz#2f32f4ad516ad1ab172781403f9c8c72ab041512"
  integrity sha1-LzL0rVFq0asXJ4FAP5yMcqsEFRI=
  dependencies:
    "@types/react" "*"
    "@types/react-native" "*"

"@types/react-native-sqlite-storage@^3.3.2":
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-native-sqlite-storage/-/@types/react-native-sqlite-storage-3.3.2.tgz#d5372c8e766995ba7f521f53f4c5a5ee40790da9"
  integrity sha1-1TcsjnZplbp/Uh9T9MWl7kB5Dak=

"@types/react-native@*":
  version "0.63.27"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-native/-/@types/react-native-0.63.27.tgz#c97ecad8183bc15e33a6f355420cf20ce990c633"
  integrity sha1-yX7K2Bg7wV4zpvNVQgzyDOmQxjM=
  dependencies:
    "@types/react" "*"

"@types/react-native@^0.60.25":
  version "0.60.31"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-native/-/@types/react-native-0.60.31.tgz#a7af12197f884ad8dd22cda2df9862ed72973ded"
  integrity sha1-p68SGX+IStjdIs2i35hi7XKXPe0=
  dependencies:
    "@types/prop-types" "*"
    "@types/react" "*"

"@types/react-router-native@^5.1.0":
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-router-native/-/@types/react-router-native-5.1.0.tgz#72eef279eb6fae34cded6fa1cc48a0b19e3e0fe0"
  integrity sha1-cu7yeetvrjTN7W+hzEigsZ4+D+A=
  dependencies:
    "@types/history" "*"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router@*":
  version "5.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-router/-/@types/react-router-5.1.8.tgz#4614e5ba7559657438e17766bb95ef6ed6acc3fa"
  integrity sha1-RhTlunVZZXQ44Xdmu5Xvbtasw/o=
  dependencies:
    "@types/history" "*"
    "@types/react" "*"

"@types/react-test-renderer@16.9.1":
  version "16.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react-test-renderer/-/@types/react-test-renderer-16.9.1.tgz#9d432c46c515ebe50c45fa92c6fb5acdc22e39c4"
  integrity sha1-nUMsRsUV6+UMRfqSxvtazcIuOcQ=
  dependencies:
    "@types/react" "*"

"@types/react@*":
  version "16.9.53"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/react/-/@types/react-16.9.53.tgz#40cd4f8b8d6b9528aedd1fff8fcffe7a112a3d23"
  integrity sha1-QM1Pi41rlSiu3R//j8/+ehEqPSM=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/stack-utils@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/stack-utils/-/@types/stack-utils-1.0.1.tgz#0a851d3bd96498fa25c33ab7278ed3bd65f06c3e"
  integrity sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=

"@types/uuid@^7.0.2":
  version "7.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/uuid/-/@types/uuid-7.0.4.tgz#00a5749810b4ad80bff73a61f9cc9d0d521feb3c"
  integrity sha1-AKV0mBC0rYC/9zph+cydDVIf6zw=

"@types/yargs-parser@*":
  version "15.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/yargs-parser/-/@types/yargs-parser-15.0.0.tgz#cb3f9f741869e20cce330ffbeb9271590483882d"
  integrity sha1-yz+fdBhp4gzOMw/765JxWQSDiC0=

"@types/yargs@^13.0.0":
  version "13.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/yargs/-/@types/yargs-13.0.11.tgz#def2f0c93e4bdf2c61d7e34899b17e34be28d3b1"
  integrity sha1-3vLwyT5L3yxh1+NImbF+NL4o07E=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^15.0.0":
  version "15.0.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@types/yargs/-/@types/yargs-15.0.9.tgz#524cd7998fe810cdb02f26101b699cccd156ff19"
  integrity sha1-UkzXmY/oEM2wLyYQG2mczNFW/xk=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^1.5.0":
  version "1.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/eslint-plugin/-/@typescript-eslint/eslint-plugin-1.13.0.tgz#22fed9b16ddfeb402fd7bcde56307820f6ebc49f"
  integrity sha1-Iv7ZsW3f60Av17zeVjB4IPbrxJ8=
  dependencies:
    "@typescript-eslint/experimental-utils" "1.13.0"
    eslint-utils "^1.3.1"
    functional-red-black-tree "^1.0.1"
    regexpp "^2.0.1"
    tsutils "^3.7.0"

"@typescript-eslint/eslint-plugin@^2.12.0":
  version "2.34.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/eslint-plugin/-/@typescript-eslint/eslint-plugin-2.34.0.tgz#6f8ce8a46c7dea4a6f1d171d2bb8fbae6dac2be9"
  integrity sha1-b4zopGx96kpvHRcdK7j7rm2sK+k=
  dependencies:
    "@typescript-eslint/experimental-utils" "2.34.0"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    tsutils "^3.17.1"

"@typescript-eslint/experimental-utils@1.13.0":
  version "1.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/experimental-utils/-/@typescript-eslint/experimental-utils-1.13.0.tgz#b08c60d780c0067de2fb44b04b432f540138301e"
  integrity sha1-sIxg14DABn3i+0SwS0MvVAE4MB4=
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "1.13.0"
    eslint-scope "^4.0.0"

"@typescript-eslint/experimental-utils@2.34.0":
  version "2.34.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/experimental-utils/-/@typescript-eslint/experimental-utils-2.34.0.tgz#d3524b644cdb40eebceca67f8cf3e4cc9c8f980f"
  integrity sha1-01JLZEzbQO687KZ/jPPkzJyPmA8=
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "2.34.0"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^1.5.0":
  version "1.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/parser/-/@typescript-eslint/parser-1.13.0.tgz#61ac7811ea52791c47dc9fd4dd4a184fae9ac355"
  integrity sha1-Yax4EepSeRxH3J/U3UoYT66aw1U=
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "1.13.0"
    "@typescript-eslint/typescript-estree" "1.13.0"
    eslint-visitor-keys "^1.0.0"

"@typescript-eslint/parser@^4.5.0":
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/parser/-/@typescript-eslint/parser-4.5.0.tgz#b2d659f25eec0041c7bc5660b91db1eefe8d7122"
  integrity sha1-stZZ8l7sAEHHvFZguR2x7v6NcSI=
  dependencies:
    "@typescript-eslint/scope-manager" "4.5.0"
    "@typescript-eslint/types" "4.5.0"
    "@typescript-eslint/typescript-estree" "4.5.0"
    debug "^4.1.1"

"@typescript-eslint/scope-manager@4.5.0":
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/scope-manager/-/@typescript-eslint/scope-manager-4.5.0.tgz#8dfd53c3256d4357e7d66c2fc8956835f4d239be"
  integrity sha1-jf1TwyVtQ1fn1mwvyJVoNfTSOb4=
  dependencies:
    "@typescript-eslint/types" "4.5.0"
    "@typescript-eslint/visitor-keys" "4.5.0"

"@typescript-eslint/types@4.5.0":
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/types/-/@typescript-eslint/types-4.5.0.tgz#98256e07bad1c8d15d0c9627ebec82fd971bb3c3"
  integrity sha1-mCVuB7rRyNFdDJYn6+yC/Zcbs8M=

"@typescript-eslint/typescript-estree@1.13.0":
  version "1.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/typescript-estree/-/@typescript-eslint/typescript-estree-1.13.0.tgz#8140f17d0f60c03619798f1d628b8434913dc32e"
  integrity sha1-gUDxfQ9gwDYZeY8dYouENJE9wy4=
  dependencies:
    lodash.unescape "4.0.1"
    semver "5.5.0"

"@typescript-eslint/typescript-estree@2.34.0":
  version "2.34.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/typescript-estree/-/@typescript-eslint/typescript-estree-2.34.0.tgz#14aeb6353b39ef0732cc7f1b8285294937cf37d5"
  integrity sha1-FK62NTs57wcyzH8bgoUpSTfPN9U=
  dependencies:
    debug "^4.1.1"
    eslint-visitor-keys "^1.1.0"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/typescript-estree@4.5.0":
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/typescript-estree/-/@typescript-eslint/typescript-estree-4.5.0.tgz#d50cf91ae3a89878401111031eb6fb6d03554f64"
  integrity sha1-1Qz5GuOomHhAEREDHrb7bQNVT2Q=
  dependencies:
    "@typescript-eslint/types" "4.5.0"
    "@typescript-eslint/visitor-keys" "4.5.0"
    debug "^4.1.1"
    globby "^11.0.1"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/visitor-keys@4.5.0":
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/@typescript-eslint/visitor-keys/-/@typescript-eslint/visitor-keys-4.5.0.tgz#b59f26213ac597efe87f6b13cf2aabee70542af0"
  integrity sha1-tZ8mITrFl+/of2sTzyqr7nBUKvA=
  dependencies:
    "@typescript-eslint/types" "4.5.0"
    eslint-visitor-keys "^2.0.0"

abab@^2.0.0:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/abab/-/abab-2.0.5.tgz#c0b678fb32d60fc1219c784d6a826fe385aeb79a"
  integrity sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/abort-controller/-/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

absolute-path@^0.0.0:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/absolute-path/-/absolute-path-0.0.0.tgz#a78762fbdadfb5297be99b15d35a785b2f095bf7"
  integrity sha1-p4di+9rftSl76ZsV01p4Wy8JW/c=

accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/accepts/-/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^4.1.0:
  version "4.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn-globals/-/acorn-globals-4.3.4.tgz#9fa1926addc11c97308c4e66d7add0d40c3272e7"
  integrity sha1-n6GSat3BHJcwjE5m163Q1Awycuc=
  dependencies:
    acorn "^6.0.1"
    acorn-walk "^6.0.1"

acorn-jsx@^5.2.0:
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn-jsx/-/acorn-jsx-5.3.1.tgz#fc8661e11b7ac1539c47dbfea2e72b3af34d267b"
  integrity sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=

acorn-walk@^6.0.1:
  version "6.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn-walk/-/acorn-walk-6.2.0.tgz#123cb8f3b84c2171f1f7fb252615b1c78a6b1a8c"
  integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=

acorn@^5.5.3:
  version "5.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn/-/acorn-5.7.4.tgz#3e8d8a9947d0599a1796d10225d7432f4a4acf5e"
  integrity sha1-Po2KmUfQWZoXltECJddDL0pKz14=

acorn@^6.0.1:
  version "6.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn/-/acorn-6.4.2.tgz#35866fd710528e92de10cf06016498e47e39e1e6"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

acorn@^7.1.1:
  version "7.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/acorn/-/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

ajv@6.9.1:
  version "6.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ajv/-/ajv-6.9.1.tgz#a4d3683d74abc5670e75f0b16520f70a20ea8dc1"
  integrity sha1-pNNoPXSrxWcOdfCxZSD3CiDqjcE=
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.3:
  version "6.12.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-colors@^1.0.1:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-colors/-/ansi-colors-1.1.0.tgz#6374b4dd5d4718ff3ce27a671a3b1cad077132a9"
  integrity sha1-Y3S03V1HGP884npnGjscrQdxMqk=
  dependencies:
    ansi-wrap "^0.1.0"

ansi-cyan@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-cyan/-/ansi-cyan-0.1.1.tgz#538ae528af8982f28ae30d86f2f17456d2609873"
  integrity sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM=
  dependencies:
    ansi-wrap "0.1.0"

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-escapes/-/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-escapes/-/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-fragments@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-fragments/-/ansi-fragments-0.2.1.tgz#24409c56c4cc37817c3d7caa99d8969e2de5a05e"
  integrity sha1-JECcVsTMN4F8PXyqmdiWni3loF4=
  dependencies:
    colorette "^1.0.7"
    slice-ansi "^2.0.0"
    strip-ansi "^5.0.0"

ansi-gray@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-gray/-/ansi-gray-0.1.1.tgz#2962cf54ec9792c48510a3deb524436861ef7251"
  integrity sha1-KWLPVOyXksSFEKPetSRDaGHvclE=
  dependencies:
    ansi-wrap "0.1.0"

ansi-red@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-red/-/ansi-red-0.1.1.tgz#8c638f9d1080800a353c9c28c8a81ca4705d946c"
  integrity sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw=
  dependencies:
    ansi-wrap "0.1.0"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.0.0, ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-regex/-/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-regex/-/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-wrap@0.1.0, ansi-wrap@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ansi-wrap/-/ansi-wrap-0.1.0.tgz#a82250ddb0015e9a27ca82e82ea603bbfa45efaf"
  integrity sha1-qCJQ3bABXponyoLoLqYDu/pF768=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/anymatch/-/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

aproba@^1.0.3:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/aproba/-/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

are-we-there-yet@~1.1.2:
  version "1.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz#4b35c2944f062a8bfcda66410760350fe9ddfc21"
  integrity sha1-SzXClE8GKov82mZBB2A1D+nd/CE=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^1.0.1:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/arr-diff/-/arr-diff-1.1.0.tgz#687c32758163588fef7de7b36fabe495eb1a399a"
  integrity sha1-aHwydYFjWI/vfeezb6vklesaOZo=
  dependencies:
    arr-flatten "^1.0.1"
    array-slice "^0.2.3"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^2.0.1:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/arr-union/-/arr-union-2.1.0.tgz#20f9eab5ec70f5c7d215b1077b1c39161d292c7d"
  integrity sha1-IPnqtexw9cfSFbEHexw5Fh0pLH0=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-equal@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-equal/-/array-equal-1.0.0.tgz#8c2a5ef2472fd9ea742b04c77a75093ba2757c93"
  integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=

array-filter@~0.0.0:
  version "0.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-filter/-/array-filter-0.0.1.tgz#7da8cf2e26628ed732803581fd21f67cacd2eeec"
  integrity sha1-fajPLiZijtcygDWB/SH2fKzS7uw=

array-includes@^3.0.3, array-includes@^3.1.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-includes/-/array-includes-3.1.1.tgz#cdd67e6852bdf9c1215460786732255ed2459348"
  integrity sha1-zdZ+aFK9+cEhVGB4ZzIlXtJFk0g=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0"
    is-string "^1.0.5"

array-map@~0.0.0:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-map/-/array-map-0.0.0.tgz#88a2bab73d1cf7bcd5c1b118a003f66f665fa662"
  integrity sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI=

array-reduce@~0.0.0:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-reduce/-/array-reduce-0.0.0.tgz#173899d3ffd1c7d9383e4479525dbe278cab5f2b"
  integrity sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys=

array-slice@^0.2.3:
  version "0.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-slice/-/array-slice-0.2.3.tgz#dd3cfb80ed7973a75117cdac69b0b99ec86186f5"
  integrity sha1-3Tz7gO15c6dRF82sabC5nshhhvU=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

art@^0.10.0:
  version "0.10.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/art/-/art-0.10.3.tgz#b01d84a968ccce6208df55a733838c96caeeaea2"
  integrity sha1-sB2EqWjMzmII31WnM4OMlsrurqI=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/asn1/-/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/assign-symbols/-/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

assign@>=0.1.7:
  version "0.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/assign/-/assign-0.1.7.tgz#e63bfe3a887b8630913c27663e4cc9bff1ddd25f"
  integrity sha1-5jv+Ooh7hjCRPCdmPkzJv/Hd0l8=
  dependencies:
    fusing "0.4.x"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/astral-regex/-/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.0:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/async-each/-/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/async-limiter/-/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async@0.6.x:
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/async/-/async-0.6.2.tgz#41fd038a3812c0a8bc1842ecf08ba63eb0392bef"
  integrity sha1-Qf0DijgSwKi8GELs8IumPrA5K+8=

async@^2.4.0:
  version "2.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/async/-/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

async@~1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/async/-/async-1.2.1.tgz#a4816a17cd5ff516dfa2c7698a453369b9790de0"
  integrity sha1-pIFqF81f9RbfosdpikUzabl5DeA=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/atob/-/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.10.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/aws4/-/aws4-1.10.1.tgz#e1e82e4f3e999e2cfd61b161280d16a111f86428"
  integrity sha1-4eguTz6Zniz9YbFhKA0WoRH4ZCg=

babel-eslint@10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-eslint/-/babel-eslint-10.0.1.tgz#919681dc099614cd7d31d45c8908695092a1faed"
  integrity sha1-kZaB3AmWFM19MdRciQhpUJKh+u0=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-jest@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-jest/-/babel-jest-24.9.0.tgz#3fc327cb8467b89d14d7bc70e315104a783ccd54"
  integrity sha1-P8Mny4RnuJ0U17xw4xUQSng8zVQ=
  dependencies:
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/babel__core" "^7.1.0"
    babel-plugin-istanbul "^5.1.0"
    babel-preset-jest "^24.9.0"
    chalk "^2.4.2"
    slash "^2.0.0"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz#84fda19c976ec5c6defef57f9427b3def66e17a3"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-istanbul@^5.1.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-plugin-istanbul/-/babel-plugin-istanbul-5.2.0.tgz#df4ade83d897a92df069c4d9a25cf2671293c854"
  integrity sha1-30reg9iXqS3wacTZolzyZxKTyFQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    find-up "^3.0.0"
    istanbul-lib-instrument "^3.3.0"
    test-exclude "^5.2.3"

babel-plugin-jest-hoist@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.9.0.tgz#4f837091eb407e01447c8843cbec546d0002d756"
  integrity sha1-T4NwketAfgFEfIhDy+xUbQAC11Y=
  dependencies:
    "@types/babel__traverse" "^7.0.6"

babel-plugin-module-resolver@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-plugin-module-resolver/-/babel-plugin-module-resolver-4.0.0.tgz#8f3a3d9d48287dc1d3b0d5595113adabd36a847f"
  integrity sha1-jzo9nUgofcHTsNVZUROtq9NqhH8=
  dependencies:
    find-babel-config "^1.2.0"
    glob "^7.1.6"
    pkg-up "^3.1.0"
    reselect "^4.0.0"
    resolve "^1.13.1"

babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0:
  version "7.0.0-beta.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz#aa213c1435e2bffeb6fca842287ef534ad05d5cf"
  integrity sha1-qiE8FDXiv/62/KhCKH71NK0F1c8=

babel-preset-fbjs@^3.1.2, babel-preset-fbjs@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-preset-fbjs/-/babel-preset-fbjs-3.3.0.tgz#a6024764ea86c8e06a22d794ca8b69534d263541"
  integrity sha1-pgJHZOqGyOBqIteUyotpU00mNUE=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    babel-plugin-syntax-trailing-function-commas "^7.0.0-beta.0"

babel-preset-jest@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/babel-preset-jest/-/babel-preset-jest-24.9.0.tgz#192b521e2217fb1d1f67cf73f70c336650ad3cdc"
  integrity sha1-GStSHiIX+x0fZ89z9wwzZlCtPNw=
  dependencies:
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    babel-plugin-jest-hoist "^24.9.0"

back@1.0.x:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/back/-/back-1.0.2.tgz#a93f5e6ce69729984d5901a2bb16e3b01a4d6369"
  integrity sha1-qT9ebOaXKZhNWQGiuxbjsBpNY2k=
  dependencies:
    xtend "^4.0.0"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base-64@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/base-64/-/base-64-0.1.0.tgz#780a99c84e7d600260361511c4877613bf24f6bb"
  integrity sha1-eAqZyE59YAJgNhURxId2E78k9rs=

base64-js@^1.0.2, base64-js@^1.1.2, base64-js@^1.2.3:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/base64-js/-/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base@^0.11.1:
  version "0.11.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

basic-auth@~2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/basic-auth/-/basic-auth-2.0.1.tgz#b998279bf47ce38344b4f3cf916d4679bbf51e3a"
  integrity sha1-uZgnm/R844NEtPPPkW1Gebv1Hjo=
  dependencies:
    safe-buffer "5.1.2"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

big-integer@^1.6.44:
  version "1.6.48"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/big-integer/-/big-integer-1.6.48.tgz#8fd88bd1632cba4a1c8c3e3d7159f08bb95b4b9e"
  integrity sha1-j9iL0WMsukocjD49cVnwi7lbS54=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/binary-extensions/-/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

bplist-creator@0.0.8:
  version "0.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bplist-creator/-/bplist-creator-0.0.8.tgz#56b2a6e79e9aec3fc33bf831d09347d73794e79c"
  integrity sha1-VrKm556a7D/DO/gx0JNH1zeU55w=
  dependencies:
    stream-buffers "~2.2.0"

bplist-parser@0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bplist-parser/-/bplist-parser-0.2.0.tgz#43a9d183e5bf9d545200ceac3e712f79ebbe8d0e"
  integrity sha1-Q6nRg+W/nVRSAM6sPnEveeu+jQ4=
  dependencies:
    big-integer "^1.6.44"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.0, braces@^2.3.1:
  version "2.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz#3c9b4b7d782c8121e56f10106d84c0d0ffc94626"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browser-resolve@^1.11.3:
  version "1.11.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/browser-resolve/-/browser-resolve-1.11.3.tgz#9b7cbb3d0f510e4cb86bdbd796124d28b5890af6"
  integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
  dependencies:
    resolve "1.1.7"

bser@2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bser/-/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-crc32@^0.2.13, buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/buffer-crc32/-/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/buffer-from/-/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

"buffer@^4.9.1 || ^5.0.7":
  version "5.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/buffer/-/buffer-5.6.0.tgz#a31749dc7d81d84db08abf937b6b8c4033f62786"
  integrity sha1-oxdJ3H2B2E2wir+Te2uMQDP2J4Y=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

builtins@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/builtins/-/builtins-1.0.3.tgz#cb94faeb61c8696451db36534e1422f94f0aee88"
  integrity sha1-y5T662HIaWRR2zZTThQi+U8K7og=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/caller-callsite/-/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/caller-path/-/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/capture-exit/-/capture-exit-2.0.0.tgz#fb953bfaebeb781f62898239dabb426d08a509a4"
  integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
  dependencies:
    rsvp "^4.8.4"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chalk/-/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chalk/-/chalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.4.0:
  version "0.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chardet/-/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chardet/-/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

chokidar@2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/chokidar/-/chokidar-2.0.4.tgz#356ff4e2b0e8e43e322d18a372460bbcf3accd26"
  integrity sha1-NW/04rDo5D4yLRijckYLvPOszSY=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.0"
    braces "^2.3.0"
    glob-parent "^3.1.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    lodash.debounce "^4.0.8"
    normalize-path "^2.1.1"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
    upath "^1.0.5"
  optionalDependencies:
    fsevents "^1.2.2"

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ci-info/-/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cli-cursor/-/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.0.0, cli-spinners@^2.2.0:
  version "2.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cli-spinners/-/cli-spinners-2.5.0.tgz#12763e47251bf951cb75c201dfa58ff1bcb2d047"
  integrity sha1-EnY+RyUb+VHLdcIB36WP8byy0Ec=

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cli-width/-/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cli-width/-/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cliui/-/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^4.0.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cliui/-/cliui-4.1.0.tgz#348422dbe82d800b3022eef4f6ac10bf2e4d1b49"
  integrity sha1-NIQi2+gtgAswIu709qwQvy5NG0k=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cliui/-/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/clone/-/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^0.5.0:
  version "0.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-convert/-/color-convert-0.5.3.tgz#bdb6c69ce660fadffe0b0007cc447e1b9f7282bd"
  integrity sha1-vbbGnOZg+t/+CwAHzER+G59ygr0=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-string/-/color-string-0.3.0.tgz#27d46fb67025c5c2fa25993bfbf579e47841b991"
  integrity sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE=
  dependencies:
    color-name "^1.0.0"

color-support@^1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color-support/-/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"
  integrity sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=

color@0.8.x:
  version "0.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/color/-/color-0.8.0.tgz#890c07c3fd4e649537638911cf691e5458b6fca5"
  integrity sha1-iQwHw/1OZJU3Y4kRz2keVFi2/KU=
  dependencies:
    color-convert "^0.5.0"
    color-string "^0.3.0"

colorette@^1.0.7:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/colorette/-/colorette-1.2.1.tgz#4d0b921325c14faf92633086a536db6e89564b1b"
  integrity sha1-TQuSEyXBT6+SYzCGpTbbbolWSxs=

colornames@0.0.2:
  version "0.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/colornames/-/colornames-0.0.2.tgz#d811fd6c84f59029499a8ac4436202935b92be31"
  integrity sha1-2BH9bIT1kClJmorEQ2ICk1uSvjE=

colorspace@1.0.x:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/colorspace/-/colorspace-1.0.1.tgz#c99c796ed31128b9876a52e1ee5ee03a4a719749"
  integrity sha1-yZx5btMRKLmHalLh7l7gOkpxl0k=
  dependencies:
    color "0.8.x"
    text-hex "0.0.x"

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

command-exists@^1.2.8:
  version "1.2.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/command-exists/-/command-exists-1.2.9.tgz#c50725af3808c8ab0260fd60b01fbfa25b954f69"
  integrity sha1-xQclrzgIyKsCYP1gsB+/oluVT2k=

commander@^2.19.0:
  version "2.20.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@~2.13.0:
  version "2.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/commander/-/commander-2.13.0.tgz#6964bca67685df7c1f1430c584f07d7597885b9c"
  integrity sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.1:
  version "1.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.2, concat-stream@^1.6.0, concat-stream@^1.6.2:
  version "1.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect@^3.6.5:
  version "3.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/connect/-/connect-3.7.0.tgz#5d49348910caa5e07a01800b030d0c35f20484f8"
  integrity sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg=
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

console-polyfill@0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/console-polyfill/-/console-polyfill-0.3.0.tgz#84900902a18c47a5eba932be75fa44d23e8af861"
  integrity sha1-hJAJAqGMR6XrqTK+dfpE0j6K+GE=

convert-source-map@^1.4.0, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/convert-source-map/-/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/copy-descriptor/-/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js@^2.2.2, core-js@^2.4.1:
  version "2.6.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/core-js/-/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
  integrity sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.5, cosmiconfig@^5.1.0:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cosmiconfig/-/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-react-class@^15.6.3:
  version "15.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/create-react-class/-/create-react-class-15.7.0.tgz#7499d7ca2e69bb51d13faf59bd04f0c65a1d6c1e"
  integrity sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4=
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

cross-spawn@^5.0.1, cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cross-spawn/-/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

css-select@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/css-select/-/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-tree@^1.0.0-alpha.39:
  version "1.0.0-alpha.39"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/css-tree/-/css-tree-1.0.0-alpha.39.tgz#2bff3ffe1bb3f776cf7eefd91ee5cba77a149eeb"
  integrity sha1-K/8//huz93bPfu/ZHuXLp3oUnus=
  dependencies:
    mdn-data "2.0.6"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/css-what/-/css-what-3.4.2.tgz#ea7026fcb01777edbde52124e21f327e7ae950e4"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

cssom@0.3.x, "cssom@>= 0.3.2 < 0.4.0":
  version "0.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cssom/-/cssom-0.3.8.tgz#9f1276f5b2b463f2114d3f2c75250af8c1a36f4a"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^1.0.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/cssstyle/-/cssstyle-1.4.0.tgz#9d31328229d3c565c61e586b02041a28fccdccf1"
  integrity sha1-nTEyginTxWXGHlhrAgQaKPzNzPE=
  dependencies:
    cssom "0.3.x"

csstype@^3.0.2:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/csstype/-/csstype-3.0.3.tgz#2b410bbeba38ba9633353aff34b05d9755d065f8"
  integrity sha1-K0ELvro4upYzNTr/NLBdl1XQZfg=

ctx-polyfill@^1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ctx-polyfill/-/ctx-polyfill-1.1.4.tgz#08420bc5c540d08ac36d05720ca503c65e302d65"
  integrity sha1-CEILxcVA0IrDbQVyDKUDxl4wLWU=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^1.0.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/data-urls/-/data-urls-1.1.0.tgz#15ee0582baa5e22bb59c77140da8f9c76963bbfe"
  integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
  dependencies:
    abab "^2.0.0"
    whatwg-mimetype "^2.2.0"
    whatwg-url "^7.0.0"

date-fns@^2.9.0:
  version "2.16.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/date-fns/-/date-fns-2.16.1.tgz#05775792c3f3331da812af253e1a935851d3834b"
  integrity sha1-BXdXksPzMx2oEq8lPhqTWFHTg0s=

dayjs@^1.8.15:
  version "1.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/dayjs/-/dayjs-1.9.3.tgz#b7f94b22ad2a136a4ca02a01ab68ae893fe1a268"
  integrity sha1-t/lLIq0qE2pMoCoBq2iuiT/homg=

debug@0.7.x:
  version "0.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/debug/-/debug-0.7.4.tgz#06e1ea8082c2cb14e39806e22e2f6f757f92af39"
  integrity sha1-BuHqgILCyxTjmAbiLi9vdX+Srzk=

debug@0.8.x:
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/debug/-/debug-0.8.1.tgz#20ff4d26f5e422cb68a1bacbbb61039ad8c1c130"
  integrity sha1-IP9NJvXkIstoobrLu2EDmtjBwTA=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.9:
  version "2.6.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/debug/-/debug-4.2.0.tgz#7f150f93920e94c58f5574c2fd01a3110effe7f1"
  integrity sha1-fxUPk5IOlMWPVXTC/QGjEQ7/5/E=
  dependencies:
    ms "2.1.2"

decache@^3.0.5:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/decache/-/decache-3.1.0.tgz#4f5036fbd6581fcc97237ac3954a244b9536c2da"
  integrity sha1-T1A2+9ZYH8yXI3rDlUokS5U2wto=
  dependencies:
    find "^0.2.4"

decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/decode-uri-component/-/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/deepmerge/-/deepmerge-3.3.0.tgz#d3c47fd6f3a93d517b14426b0628a17b0125f5f7"
  integrity sha1-08R/1vOpPVF7FEJrBiihewEl9fc=

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/defaults/-/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/define-properties/-/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/define-property/-/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denodeify@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/denodeify/-/denodeify-1.2.1.tgz#3a36287f5034e699e7577901052c2e6c94251631"
  integrity sha1-OjYof1A05pnnV3kBBSwubJQlFjE=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@~2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/depd/-/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-newline@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/detect-newline/-/detect-newline-2.1.0.tgz#f41f1c10be4b00e87b5f13da680759f2c5bfd3e2"
  integrity sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=

diagnostics@1.0.x:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/diagnostics/-/diagnostics-1.0.1.tgz#accdb080c82bb25d0dd73430a9e6a87fbb431541"
  integrity sha1-rM2wgMgrsl0N1zQwqeaof7tDFUE=
  dependencies:
    colorspace "1.0.x"
    enabled "1.0.x"
    kuler "0.0.x"

didyoumean@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/didyoumean/-/didyoumean-1.2.1.tgz#e92edfdada6537d484d73c0172fd1eba0c4976ff"
  integrity sha1-6S7f2tplN9SE1zwBcv0eugxJdv8=

diff-sequences@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/diff-sequences/-/diff-sequences-24.9.0.tgz#5715d6244e2aa65f48bba0bc972db0b0b11e95b5"
  integrity sha1-VxXWJE4qpl9Iu6C8ly2wsLEelbU=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/doctrine/-/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/dom-serializer/-/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domelementtype@1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/domelementtype/-/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/domelementtype/-/domelementtype-2.0.2.tgz#f3b6e549201e46f588b59463dd77187131fe6971"
  integrity sha1-87blSSAeRvWItZRj3XcYcTH+aXE=

domexception@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/domexception/-/domexception-1.0.1.tgz#937442644ca6a31261ef36e3ec677fe805582c90"
  integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
  dependencies:
    webidl-conversions "^4.0.2"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

emits@1.0.x:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/emits/-/emits-1.0.2.tgz#db20ec6668325071c313441e30cfe2a69ea73859"
  integrity sha1-2yDsZmgyUHHDE0QeMM/ipp6nOFk=

emits@3.0.x:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/emits/-/emits-3.0.0.tgz#32752bba95e1707b219562384ab9bb8b1fd62f70"
  integrity sha1-MnUrupXhcHshlWI4Srm7ix/WL3A=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/emoji-regex/-/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

enabled@1.0.x:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/enabled/-/enabled-1.0.2.tgz#965f6513d2c2d1c5f4652b64a2e3396467fc2f93"
  integrity sha1-ll9lE9LC0cX0ZStkouM5ZGf8L5M=
  dependencies:
    env-variable "0.0.x"

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/encoding/-/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

entities@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/entities/-/entities-2.1.0.tgz#992d3129cf7df6870b96c57858c249a120f8b8b5"
  integrity sha1-mS0xKc999ocLlsV4WMJJoSD4uLU=

env-variable@0.0.x:
  version "0.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/env-variable/-/env-variable-0.0.6.tgz#74ab20b3786c545b62b4a4813ab8cf22726c9808"
  integrity sha1-dKsgs3hsVFtitKSBOrjPInJsmAg=

envinfo@^7.1.0, envinfo@^7.5.0:
  version "7.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/envinfo/-/envinfo-7.7.3.tgz#4b2d8622e3e7366afb8091b23ed95569ea0208cc"
  integrity sha1-Sy2GIuPnNmr7gJGyPtlVaeoCCMw=

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.4:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/error-stack-parser/-/error-stack-parser-2.0.6.tgz#5a99a707bd7a4c58a797902d48d82803ede6aad8"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

errorhandler@^1.5.0:
  version "1.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/errorhandler/-/errorhandler-1.5.1.tgz#b9ba5d17cf90744cd1e851357a6e75bf806a9a91"
  integrity sha1-ubpdF8+QdEzR6FE1em51v4BqmpE=
  dependencies:
    accepts "~1.3.7"
    escape-html "~1.0.3"

es-abstract@^1.17.0, es-abstract@^1.17.0-next.1, es-abstract@^1.17.2:
  version "1.17.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/es-abstract/-/es-abstract-1.17.7.tgz#a4de61b2f66989fc7421676c1cb9787573ace54c"
  integrity sha1-pN5hsvZpifx0IWdsHLl4dXOs5Uw=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-abstract@^1.18.0-next.0, es-abstract@^1.18.0-next.1:
  version "1.18.0-next.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/es-abstract/-/es-abstract-1.18.0-next.1.tgz#6e3a0a4bda717e5023ab3b8e90bec36108d22c68"
  integrity sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-negative-zero "^2.0.0"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escodegen@^1.9.1:
  version "1.14.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/escodegen/-/escodegen-1.14.3.tgz#4e7b81fba61581dc97582ed78cab7f0e8d63f503"
  integrity sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-plugin-eslint-comments@^3.1.1:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-eslint-comments/-/eslint-plugin-eslint-comments-3.2.0.tgz#9e1cd7b4413526abb313933071d7aba05ca12ffa"
  integrity sha1-nhzXtEE1JquzE5MwcderoFyhL/o=
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-flowtype@2.50.3:
  version "2.50.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-flowtype/-/eslint-plugin-flowtype-2.50.3.tgz#61379d6dce1d010370acd6681740fd913d68175f"
  integrity sha1-YTedbc4dAQNwrNZoF0D9kT1oF18=
  dependencies:
    lodash "^4.17.10"

eslint-plugin-jest@22.4.1:
  version "22.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-jest/-/eslint-plugin-jest-22.4.1.tgz#a5fd6f7a2a41388d16f527073b778013c5189a9c"
  integrity sha1-pf1veipBOI0W9ScHO3eAE8UYmpw=

eslint-plugin-prettier@2.6.2:
  version "2.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-prettier/-/eslint-plugin-prettier-2.6.2.tgz#71998c60aedfa2141f7bfcbf9d1c459bf98b4fad"
  integrity sha1-cZmMYK7fohQfe/y/nRxFm/mLT60=
  dependencies:
    fast-diff "^1.1.1"
    jest-docblock "^21.0.0"

eslint-plugin-react-hooks@^1.5.1:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-1.7.0.tgz#6210b6d5a37205f0b92858f895a4e827020a7d04"
  integrity sha1-YhC21aNyBfC5KFj4laToJwIKfQQ=

eslint-plugin-react-native-globals@^0.1.1:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-react-native-globals/-/eslint-plugin-react-native-globals-0.1.2.tgz#ee1348bc2ceb912303ce6bdbd22e2f045ea86ea2"
  integrity sha1-7hNIvCzrkSMDzmvb0i4vBF6obqI=

eslint-plugin-react-native@3.6.0:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-react-native/-/eslint-plugin-react-native-3.6.0.tgz#7cad3b7c6159df6d26fe3252c6c5417a17f27b4b"
  integrity sha1-fK07fGFZ320m/jJSxsVBehfye0s=
  dependencies:
    eslint-plugin-react-native-globals "^0.1.1"

eslint-plugin-react@7.12.4:
  version "7.12.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-plugin-react/-/eslint-plugin-react-7.12.4.tgz#b1ecf26479d61aee650da612e425c53a99f48c8c"
  integrity sha1-sezyZHnWGu5lDaYS5CXFOpn0jIw=
  dependencies:
    array-includes "^3.0.3"
    doctrine "^2.1.0"
    has "^1.0.3"
    jsx-ast-utils "^2.0.1"
    object.fromentries "^2.0.0"
    prop-types "^15.6.2"
    resolve "^1.9.0"

eslint-scope@3.7.1:
  version "3.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-scope/-/eslint-scope-3.7.1.tgz#3d63c3edfda02e06e01a452ad88caacc7cdcb6e8"
  integrity sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.0:
  version "4.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-scope/-/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-scope/-/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1, eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-utils/-/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-utils/-/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint-visitor-keys/-/eslint-visitor-keys-2.0.0.tgz#21fdc8fbcd9c795cc0321f0563702095751511a8"
  integrity sha1-If3I+82ceVzAMh8FY3AglXUVEag=

eslint@^6.5.1:
  version "6.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eslint/-/eslint-6.8.0.tgz#62262d6729739f9275723824302fb227c8c93ffb"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2:
  version "6.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/espree/-/espree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/esquery/-/esquery-1.3.1.tgz#b78b5828aa8e214e29fb74c4d5b752e1c033da57"
  integrity sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/estraverse/-/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/estraverse/-/estraverse-5.2.0.tgz#307df42547e6cc7324d3cf03c155d5cdb8c53880"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-target-shim@^5.0.0, event-target-shim@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/event-target-shim/-/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@1.2.x:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eventemitter3/-/eventemitter3-1.2.0.tgz#1c86991d816ad1e504750e73874224ecf3bec508"
  integrity sha1-HIaZHYFq0eUEdQ5zh0Ik7PO+xQg=

eventemitter3@^3.0.0:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/eventemitter3/-/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

exec-sh@^0.3.2:
  version "0.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/exec-sh/-/exec-sh-0.3.4.tgz#3a018ceb526cc6f6df2bb504b2bfe8e3a4934ec5"
  integrity sha1-OgGM61JsxvbfK7UEsr/o46STTsU=

execa@^0.7.0:
  version "0.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/execa/-/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/execa/-/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/exit/-/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/expect/-/expect-24.9.0.tgz#b75165b4817074fa4a157794f46fe9f1ba15b6ca"
  integrity sha1-t1FltIFwdPpKFXeU9G/p8boVtso=
  dependencies:
    "@jest/types" "^24.9.0"
    ansi-styles "^3.2.0"
    jest-get-type "^24.9.0"
    jest-matcher-utils "^24.9.0"
    jest-message-util "^24.9.0"
    jest-regex-util "^24.9.0"

extend-shallow@^1.1.2:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extend-shallow/-/extend-shallow-1.1.4.tgz#19d6bf94dfc09d76ba711f39b872d21ff4dd9071"
  integrity sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=
  dependencies:
    kind-of "^1.1.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extend-shallow/-/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

extendible@0.1.x:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extendible/-/extendible-0.1.1.tgz#e2a37ed87129fb4f9533e8a8d7506230a539c905"
  integrity sha1-4qN+2HEp+0+VM+io11BiMKU5yQU=

external-editor@^2.0.4:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/external-editor/-/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
  integrity sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-github@0.0.x:
  version "0.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extract-github/-/extract-github-0.0.5.tgz#f542536db8c19b983a3bec9db96d2ef2a5ff1a86"
  integrity sha1-9UJTbbjBm5g6O+yduW0u8qX/GoY=

extract-zip@^1.6.7:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extract-zip/-/extract-zip-1.7.0.tgz#556cc3ae9df7f452c493a0cfb51cc30277940927"
  integrity sha1-VWzDrp339FLEk6DPtRzDAneUCSc=
  dependencies:
    concat-stream "^1.6.2"
    debug "^2.6.9"
    mkdirp "^0.5.4"
    yauzl "^2.10.0"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/extsprintf/-/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fancy-log@^1.3.2:
  version "1.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fancy-log/-/fancy-log-1.3.3.tgz#dbc19154f558690150a23953a0adbd035be45fc7"
  integrity sha1-28GRVPVYaQFQojlToK29A1vkX8c=
  dependencies:
    ansi-gray "^0.1.1"
    color-support "^1.1.3"
    parse-node-version "^1.0.0"
    time-stamp "^1.0.0"

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
  integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.1:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-diff/-/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@^3.1.1:
  version "3.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-glob/-/fast-glob-3.2.4.tgz#d20aefbf99579383e7f3cc66529158c9b98554d3"
  integrity sha1-0grvv5lXk4Pn88xmUpFYybmFVNM=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.0"
    merge2 "^1.3.0"
    micromatch "^4.0.2"
    picomatch "^2.2.1"

fast-json-stable-stringify@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
  integrity sha1-1RQsDK7msRifh9OnYREGT4bIu/I=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fastq/-/fastq-1.8.0.tgz#550e1f9f59bbc65fe185cb6a9b4d95357107f481"
  integrity sha1-VQ4fn1m7xl/hhctqm02VNXEH9IE=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fb-watchman/-/fb-watchman-2.0.1.tgz#fc84fb39d2709cf3ff6d743706157bb5708a8a85"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

fbjs-css-vars@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz#216551136ae02fe255932c3ec8775f18e2c078b8"
  integrity sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg=

fbjs-scripts@^1.1.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fbjs-scripts/-/fbjs-scripts-1.2.0.tgz#069a0c0634242d10031c6460ef1fccefcdae8b27"
  integrity sha1-BpoMBjQkLRADHGRg7x/M782uiyc=
  dependencies:
    "@babel/core" "^7.0.0"
    ansi-colors "^1.0.1"
    babel-preset-fbjs "^3.2.0"
    core-js "^2.4.1"
    cross-spawn "^5.1.0"
    fancy-log "^1.3.2"
    object-assign "^4.0.1"
    plugin-error "^0.1.2"
    semver "^5.1.0"
    through2 "^2.0.0"

fbjs@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fbjs/-/fbjs-1.0.0.tgz#52c215e0883a3c86af2a7a776ed51525ae8e0a5a"
  integrity sha1-UsIV4Ig6PIavKnp3btUVJa6OClo=
  dependencies:
    core-js "^2.4.1"
    fbjs-css-vars "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.18"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fd-slicer/-/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/figures/-/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/file-entry-cache/-/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/finalhandler/-/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find-babel-config/-/find-babel-config-1.2.0.tgz#a9b7b317eb5b9860cda9d54740a8c8337a2283a2"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find@^0.2.4:
  version "0.2.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/find/-/find-0.2.9.tgz#4b73f1ff9e56ad91b76e716407fe5ffe6554bb8c"
  integrity sha1-S3Px/55WrZG3bnFkB/5f/mVUu4w=
  dependencies:
    traverse-chain "~0.1.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/flat-cache/-/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/flatted/-/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/form-data/-/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forms-logic-compiler@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/forms-logic-compiler/-/forms-logic-compiler-1.0.2.tgz#4df895550fe3643cfe067527a3f6a8aff03d7a1f"
  integrity sha1-TfiVVQ/jZDz+BnUno/aor/A9eh8=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fs-extra/-/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
  integrity sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fs-extra/-/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.2, fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fsevents/-/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

fusing@0.2.x:
  version "0.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fusing/-/fusing-0.2.3.tgz#d0eefaf985d2bafded44af8b185316f6e429e1db"
  integrity sha1-0O76+YXSuv3tRK+LGFMW9uQp4ds=
  dependencies:
    predefine "0.1.x"

fusing@0.4.x:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fusing/-/fusing-0.4.0.tgz#c99068f54ca3e11dc0118902152abf367aba4a4d"
  integrity sha1-yZBo9Uyj4R3AEYkCFSq/Nnq6Sk0=
  dependencies:
    emits "1.0.x"
    predefine "0.1.x"

fusing@1.0.x:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/fusing/-/fusing-1.0.0.tgz#550c15d76af9265778aa051ece44d4000a098d45"
  integrity sha1-VQwV12r5Jld4qgUezkTUAAoJjUU=
  dependencies:
    emits "3.0.x"
    predefine "0.1.x"

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/gauge/-/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/gensync/-/gensync-1.0.0-beta.1.tgz#58f4361ff987e5ff6e1e7a210827aa371eaac269"
  integrity sha1-WPQ2H/mH5f9uHnohCCeqNx6qwmk=

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/get-caller-file/-/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"
  integrity sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/get-caller-file/-/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/get-stream/-/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

githulk@0.0.x:
  version "0.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/githulk/-/githulk-0.0.7.tgz#d96ca29f0ec43117c538e521d663566ea84b4eff"
  integrity sha1-2Wyinw7EMRfFOOUh1mNWbqhLTv8=
  dependencies:
    debug "0.7.x"
    extract-github "0.0.x"
    mana "0.1.x"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/glob-parent/-/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@^5.1.0:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/glob-parent/-/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob@^7.0.0, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
  version "7.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/glob/-/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/globals/-/globals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globby@^11.0.1:
  version "11.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/globby/-/globby-11.0.1.tgz#9a2bf107a068f3ffeabc49ad702c79ede8cfd357"
  integrity sha1-mivxB6Bo8//qvEmtcCx57ejP01c=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
  version "4.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/graceful-fs/-/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb"
  integrity sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=

growly@^1.3.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/growly/-/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/har-validator/-/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-symbols/-/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-values/-/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hermes-engine@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/hermes-engine/-/hermes-engine-0.2.1.tgz#25c0f1ff852512a92cb5c5cc47cf967e1e722ea2"
  integrity sha1-JcDx/4UlEqkstcXMR8+Wfh5yLqI=

history@^4.10.1, history@^4.9.0:
  version "4.10.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/history/-/history-4.10.1.tgz#33371a65e3a83b267434e2b3f3b1b4c58aad4cf3"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hoist-non-react-statics@^2.5.5:
  version "2.5.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.1.0:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hosted-git-info@^2.1.4, hosted-git-info@^2.7.1:
  version "2.8.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/hosted-git-info/-/hosted-git-info-2.8.8.tgz#7539bd4bc1e0e0a895815a2e0262420b12858488"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

html-encoding-sniffer@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz#e70d84b94da53aa375e11fe3a351be6642ca46f8"
  integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
  dependencies:
    whatwg-encoding "^1.0.1"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/html-escaper/-/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/http-errors/-/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

iconv-lite@0.4.24, iconv-lite@^0.4.17, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/iconv-lite/-/iconv-lite-0.6.2.tgz#ce13d1875b0c3a674bd6a04b7f76b01b1b6ded01"
  integrity sha1-zhPRh1sMOmdL1qBLf3awGxtt7QE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ignore/-/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.5, ignore@^5.1.4:
  version "5.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ignore/-/ignore-5.1.8.tgz#f150a8b50a34289b33e22f5889abd4d8016f0e57"
  integrity sha1-8VCotQo0KJsz4i9YiavU2AFvDlc=

image-size@^0.6.0:
  version "0.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/image-size/-/image-size-0.6.3.tgz#e7e5c65bb534bd7cdcedd6cb5166272a85f75fb2"
  integrity sha1-5+XGW7U0vXzc7dbLUWYnKoX3X7I=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/import-fresh/-/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/import-fresh/-/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
  integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/import-local/-/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inquirer@^3.0.6:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/inquirer/-/inquirer-3.3.0.tgz#9dd2f2ad765dcab1ff0443b491442a20ba227dc9"
  integrity sha1-ndLyrXZdyrH/BEO0kUQqILoifck=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

inquirer@^7.0.0:
  version "7.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/inquirer/-/inquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/interpret/-/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invariant@2.2.4, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/invert-kv/-/invert-kv-2.0.0.tgz#7393f5afa59ec9ff5f67a27620d11c226e3eec02"
  integrity sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.2:
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-callable/-/is-callable-1.2.2.tgz#c7c6715cd22d4ddb48d3e19970223aceabb080d9"
  integrity sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-ci/-/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-core-module/-/is-core-module-2.0.0.tgz#58531b70aed1db7c0e8d4eb1a0a2d1ddd64bd12d"
  integrity sha1-WFMbcK7R23wOjU6xoKLR3dZL0S0=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-date-object/-/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-descriptor/-/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-directory/-/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-extendable/-/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-generator-fn/-/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-glob/-/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-glob/-/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-negative-zero@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-negative-zero/-/is-negative-zero-2.0.0.tgz#9553b121b0fac28869da9ed459e20c7543788461"
  integrity sha1-lVOxIbD6wohp2p7UWeIMdUN4hGE=

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-regex/-/is-regex-1.1.1.tgz#c6f98aacc546f6cec5468a07b7b153ab564a57b9"
  integrity sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=
  dependencies:
    has-symbols "^1.0.1"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-string@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-string/-/is-string-1.0.5.tgz#40493ed198ef3ff477b8c7f92f644ec82a5cd3a6"
  integrity sha1-QEk+0ZjvP/R3uMf5L2ROyCpc06Y=

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-symbol/-/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is-wsl/-/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is_js@^0.9.0:
  version "0.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/is_js/-/is_js-0.9.0.tgz#0ab94540502ba7afa24c856aa985561669e9c52d"
  integrity sha1-CrlFQFArp6+iTIVqqYVWFmnpxS0=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^2.0.2, istanbul-lib-coverage@^2.0.5:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.5.tgz#675f0ab69503fad4b1d849f736baaca803344f49"
  integrity sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k=

istanbul-lib-instrument@^3.0.1, istanbul-lib-instrument@^3.3.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/istanbul-lib-instrument/-/istanbul-lib-instrument-3.3.0.tgz#a5f63d91f0bbc0c3e479ef4c5de027335ec6d630"
  integrity sha1-pfY9kfC7wMPkee9MXeAnM17G1jA=
  dependencies:
    "@babel/generator" "^7.4.0"
    "@babel/parser" "^7.4.3"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.3"
    "@babel/types" "^7.4.0"
    istanbul-lib-coverage "^2.0.5"
    semver "^6.0.0"

istanbul-lib-report@^2.0.4:
  version "2.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/istanbul-lib-report/-/istanbul-lib-report-2.0.8.tgz#5a8113cd746d43c4889eba36ab10e7d50c9b4f33"
  integrity sha1-WoETzXRtQ8SInro2qxDn1QybTzM=
  dependencies:
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    supports-color "^6.1.0"

istanbul-lib-source-maps@^3.0.1:
  version "3.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.6.tgz#284997c48211752ec486253da97e3879defba8c8"
  integrity sha1-KEmXxIIRdS7EhiU9qX44ed77qMg=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    rimraf "^2.6.3"
    source-map "^0.6.1"

istanbul-reports@^2.2.6:
  version "2.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/istanbul-reports/-/istanbul-reports-2.2.7.tgz#5d939f6237d7b48393cc0959eab40cd4fd056931"
  integrity sha1-XZOfYjfXtIOTzAlZ6rQM1P0FaTE=
  dependencies:
    html-escaper "^2.0.0"

jest-changed-files@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-changed-files/-/jest-changed-files-24.9.0.tgz#08d8c15eb79a7fa3fc98269bc14b451ee82f8039"
  integrity sha1-CNjBXreaf6P8mCabwUtFHugvgDk=
  dependencies:
    "@jest/types" "^24.9.0"
    execa "^1.0.0"
    throat "^4.0.0"

jest-cli@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-cli/-/jest-cli-24.9.0.tgz#ad2de62d07472d419c6abc301fc432b98b10d2af"
  integrity sha1-rS3mLQdHLUGcarwwH8QyuYsQ0q8=
  dependencies:
    "@jest/core" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    import-local "^2.0.0"
    is-ci "^2.0.0"
    jest-config "^24.9.0"
    jest-util "^24.9.0"
    jest-validate "^24.9.0"
    prompts "^2.0.1"
    realpath-native "^1.1.0"
    yargs "^13.3.0"

jest-config@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-config/-/jest-config-24.9.0.tgz#fb1bbc60c73a46af03590719efa4825e6e4dd1b5"
  integrity sha1-+xu8YMc6Rq8DWQcZ76SCXm5N0bU=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^24.9.0"
    "@jest/types" "^24.9.0"
    babel-jest "^24.9.0"
    chalk "^2.0.1"
    glob "^7.1.1"
    jest-environment-jsdom "^24.9.0"
    jest-environment-node "^24.9.0"
    jest-get-type "^24.9.0"
    jest-jasmine2 "^24.9.0"
    jest-regex-util "^24.3.0"
    jest-resolve "^24.9.0"
    jest-util "^24.9.0"
    jest-validate "^24.9.0"
    micromatch "^3.1.10"
    pretty-format "^24.9.0"
    realpath-native "^1.1.0"

jest-diff@^24.3.0, jest-diff@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-diff/-/jest-diff-24.9.0.tgz#931b7d0d5778a1baf7452cb816e325e3724055da"
  integrity sha1-kxt9DVd4obr3RSy4FuMl43JAVdo=
  dependencies:
    chalk "^2.0.1"
    diff-sequences "^24.9.0"
    jest-get-type "^24.9.0"
    pretty-format "^24.9.0"

jest-docblock@^21.0.0:
  version "21.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-docblock/-/jest-docblock-21.2.0.tgz#51529c3b30d5fd159da60c27ceedc195faf8d414"
  integrity sha1-UVKcOzDV/RWdpgwnzu3Blfr41BQ=

jest-docblock@^24.3.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-docblock/-/jest-docblock-24.9.0.tgz#7970201802ba560e1c4092cc25cbedf5af5a8ce2"
  integrity sha1-eXAgGAK6Vg4cQJLMJcvt9a9ajOI=
  dependencies:
    detect-newline "^2.1.0"

jest-each@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-each/-/jest-each-24.9.0.tgz#eb2da602e2a610898dbc5f1f6df3ba86b55f8b05"
  integrity sha1-6y2mAuKmEImNvF8fbfO6hrVfiwU=
  dependencies:
    "@jest/types" "^24.9.0"
    chalk "^2.0.1"
    jest-get-type "^24.9.0"
    jest-util "^24.9.0"
    pretty-format "^24.9.0"

jest-environment-jsdom@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-environment-jsdom/-/jest-environment-jsdom-24.9.0.tgz#4b0806c7fc94f95edb369a69cc2778eec2b7375b"
  integrity sha1-SwgGx/yU+V7bNpppzCd47sK3N1s=
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/types" "^24.9.0"
    jest-mock "^24.9.0"
    jest-util "^24.9.0"
    jsdom "^11.5.1"

jest-environment-node@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-environment-node/-/jest-environment-node-24.9.0.tgz#333d2d2796f9687f2aeebf0742b519f33c1cbfd3"
  integrity sha1-Mz0tJ5b5aH8q7r8HQrUZ8zwcv9M=
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/types" "^24.9.0"
    jest-mock "^24.9.0"
    jest-util "^24.9.0"

jest-get-type@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-get-type/-/jest-get-type-24.9.0.tgz#1684a0c8a50f2e4901b6644ae861f579eed2ef0e"
  integrity sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4=

jest-haste-map@^24.7.1, jest-haste-map@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-haste-map/-/jest-haste-map-24.9.0.tgz#b38a5d64274934e21fa417ae9a9fbeb77ceaac7d"
  integrity sha1-s4pdZCdJNOIfpBeump++t3zqrH0=
  dependencies:
    "@jest/types" "^24.9.0"
    anymatch "^2.0.0"
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.15"
    invariant "^2.2.4"
    jest-serializer "^24.9.0"
    jest-util "^24.9.0"
    jest-worker "^24.9.0"
    micromatch "^3.1.10"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^1.2.7"

jest-jasmine2@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-jasmine2/-/jest-jasmine2-24.9.0.tgz#1f7b1bd3242c1774e62acabb3646d96afc3be6a0"
  integrity sha1-H3sb0yQsF3TmKsq7NkbZavw75qA=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    chalk "^2.0.1"
    co "^4.6.0"
    expect "^24.9.0"
    is-generator-fn "^2.0.0"
    jest-each "^24.9.0"
    jest-matcher-utils "^24.9.0"
    jest-message-util "^24.9.0"
    jest-runtime "^24.9.0"
    jest-snapshot "^24.9.0"
    jest-util "^24.9.0"
    pretty-format "^24.9.0"
    throat "^4.0.0"

jest-leak-detector@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-leak-detector/-/jest-leak-detector-24.9.0.tgz#b665dea7c77100c5c4f7dfcb153b65cf07dcf96a"
  integrity sha1-tmXep8dxAMXE99/LFTtlzwfc+Wo=
  dependencies:
    jest-get-type "^24.9.0"
    pretty-format "^24.9.0"

jest-matcher-utils@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-matcher-utils/-/jest-matcher-utils-24.9.0.tgz#f5b3661d5e628dffe6dd65251dfdae0e87c3a073"
  integrity sha1-9bNmHV5ijf/m3WUlHf2uDofDoHM=
  dependencies:
    chalk "^2.0.1"
    jest-diff "^24.9.0"
    jest-get-type "^24.9.0"
    pretty-format "^24.9.0"

jest-message-util@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-message-util/-/jest-message-util-24.9.0.tgz#527f54a1e380f5e202a8d1149b0ec872f43119e3"
  integrity sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/stack-utils" "^1.0.1"
    chalk "^2.0.1"
    micromatch "^3.1.10"
    slash "^2.0.0"
    stack-utils "^1.0.1"

jest-mock@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-mock/-/jest-mock-24.9.0.tgz#c22835541ee379b908673ad51087a2185c13f1c6"
  integrity sha1-wig1VB7jebkIZzrVEIeiGFwT8cY=
  dependencies:
    "@jest/types" "^24.9.0"

jest-pnp-resolver@^1.2.1:
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz#b704ac0ae028a89108a4d040b3f919dfddc8e33c"
  integrity sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=

jest-regex-util@^24.3.0, jest-regex-util@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-regex-util/-/jest-regex-util-24.9.0.tgz#c13fb3380bde22bf6575432c493ea8fe37965636"
  integrity sha1-wT+zOAveIr9ldUMsST6o/jeWVjY=

jest-resolve-dependencies@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-resolve-dependencies/-/jest-resolve-dependencies-24.9.0.tgz#ad055198959c4cfba8a4f066c673a3f0786507ab"
  integrity sha1-rQVRmJWcTPuopPBmxnOj8HhlB6s=
  dependencies:
    "@jest/types" "^24.9.0"
    jest-regex-util "^24.3.0"
    jest-snapshot "^24.9.0"

jest-resolve@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-resolve/-/jest-resolve-24.9.0.tgz#dff04c7687af34c4dd7e524892d9cf77e5d17321"
  integrity sha1-3/BMdoevNMTdflJIktnPd+XRcyE=
  dependencies:
    "@jest/types" "^24.9.0"
    browser-resolve "^1.11.3"
    chalk "^2.0.1"
    jest-pnp-resolver "^1.2.1"
    realpath-native "^1.1.0"

jest-runner@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-runner/-/jest-runner-24.9.0.tgz#574fafdbd54455c2b34b4bdf4365a23857fcdf42"
  integrity sha1-V0+v29VEVcKzS0vfQ2WiOFf830I=
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    chalk "^2.4.2"
    exit "^0.1.2"
    graceful-fs "^4.1.15"
    jest-config "^24.9.0"
    jest-docblock "^24.3.0"
    jest-haste-map "^24.9.0"
    jest-jasmine2 "^24.9.0"
    jest-leak-detector "^24.9.0"
    jest-message-util "^24.9.0"
    jest-resolve "^24.9.0"
    jest-runtime "^24.9.0"
    jest-util "^24.9.0"
    jest-worker "^24.6.0"
    source-map-support "^0.5.6"
    throat "^4.0.0"

jest-runtime@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-runtime/-/jest-runtime-24.9.0.tgz#9f14583af6a4f7314a6a9d9f0226e1a781c8e4ac"
  integrity sha1-nxRYOvak9zFKap2fAibhp4HI5Kw=
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/environment" "^24.9.0"
    "@jest/source-map" "^24.3.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/yargs" "^13.0.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.1.15"
    jest-config "^24.9.0"
    jest-haste-map "^24.9.0"
    jest-message-util "^24.9.0"
    jest-mock "^24.9.0"
    jest-regex-util "^24.3.0"
    jest-resolve "^24.9.0"
    jest-snapshot "^24.9.0"
    jest-util "^24.9.0"
    jest-validate "^24.9.0"
    realpath-native "^1.1.0"
    slash "^2.0.0"
    strip-bom "^3.0.0"
    yargs "^13.3.0"

jest-serializer@^24.4.0, jest-serializer@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-serializer/-/jest-serializer-24.9.0.tgz#e6d7d7ef96d31e8b9079a714754c5d5c58288e73"
  integrity sha1-5tfX75bTHouQeacUdUxdXFgojnM=

jest-snapshot@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-snapshot/-/jest-snapshot-24.9.0.tgz#ec8e9ca4f2ec0c5c87ae8f925cf97497b0e951ba"
  integrity sha1-7I6cpPLsDFyHro+SXPl0l7DpUbo=
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^24.9.0"
    chalk "^2.0.1"
    expect "^24.9.0"
    jest-diff "^24.9.0"
    jest-get-type "^24.9.0"
    jest-matcher-utils "^24.9.0"
    jest-message-util "^24.9.0"
    jest-resolve "^24.9.0"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    pretty-format "^24.9.0"
    semver "^6.2.0"

jest-util@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-util/-/jest-util-24.9.0.tgz#7396814e48536d2e85a37de3e4c431d7cb140162"
  integrity sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/source-map" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    callsites "^3.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.15"
    is-ci "^2.0.0"
    mkdirp "^0.5.1"
    slash "^2.0.0"
    source-map "^0.6.0"

jest-validate@^24.7.0, jest-validate@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-validate/-/jest-validate-24.9.0.tgz#0775c55360d173cd854e40180756d4ff52def8ab"
  integrity sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks=
  dependencies:
    "@jest/types" "^24.9.0"
    camelcase "^5.3.1"
    chalk "^2.0.1"
    jest-get-type "^24.9.0"
    leven "^3.1.0"
    pretty-format "^24.9.0"

jest-watcher@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-watcher/-/jest-watcher-24.9.0.tgz#4b56e5d1ceff005f5b88e528dc9afc8dd4ed2b3b"
  integrity sha1-S1bl0c7/AF9biOUo3Jr8jdTtKzs=
  dependencies:
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/yargs" "^13.0.0"
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    jest-util "^24.9.0"
    string-length "^2.0.0"

jest-worker@^24.6.0, jest-worker@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest-worker/-/jest-worker-24.9.0.tgz#5dbfdb5b2d322e98567898238a9697bcce67b3e5"
  integrity sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^6.1.0"

jest@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jest/-/jest-24.9.0.tgz#987d290c05a08b52c56188c1002e368edb007171"
  integrity sha1-mH0pDAWgi1LFYYjBAC42jtsAcXE=
  dependencies:
    import-local "^2.0.0"
    jest-cli "^24.9.0"

jetifier@^1.6.2:
  version "1.6.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jetifier/-/jetifier-1.6.6.tgz#fec8bff76121444c12dc38d2dad6767c421dab68"
  integrity sha1-/si/92EhREwS3DjS2tZ2fEIdq2g=

js-sha256@^0.9.0:
  version "0.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/js-sha256/-/js-sha256-0.9.0.tgz#0b89ac166583e91ef9123644bd3c5334ce9d0966"
  integrity sha1-C4msFmWD6R75EjZEvTxTNM6dCWY=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/js-yaml/-/js-yaml-3.14.0.tgz#a7a34170f26a21bb162424d8adacb4113a69e482"
  integrity sha1-p6NBcPJqIbsWJCTYray0ETpp5II=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsc-android@^245459.0.0:
  version "245459.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsc-android/-/jsc-android-245459.0.0.tgz#e584258dd0b04c9159a27fb104cd5d491fd202c9"
  integrity sha1-5YQljdCwTJFZon+xBM1dSR/SAsk=

jsdom@^11.5.1:
  version "11.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsdom/-/jsdom-11.12.0.tgz#1a80d40ddd378a1de59656e9e6dc5a3ba8657bc8"
  integrity sha1-GoDUDd03ih3lllbp5txaO6hle8g=
  dependencies:
    abab "^2.0.0"
    acorn "^5.5.3"
    acorn-globals "^4.1.0"
    array-equal "^1.0.0"
    cssom ">= 0.3.2 < 0.4.0"
    cssstyle "^1.0.0"
    data-urls "^1.0.0"
    domexception "^1.0.1"
    escodegen "^1.9.1"
    html-encoding-sniffer "^1.0.2"
    left-pad "^1.3.0"
    nwsapi "^2.0.7"
    parse5 "4.0.0"
    pn "^1.1.0"
    request "^2.87.0"
    request-promise-native "^1.0.5"
    sax "^1.2.4"
    symbol-tree "^3.2.2"
    tough-cookie "^2.3.4"
    w3c-hr-time "^1.0.1"
    webidl-conversions "^4.0.2"
    whatwg-encoding "^1.0.3"
    whatwg-mimetype "^2.1.0"
    whatwg-url "^6.4.1"
    ws "^5.2.0"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  integrity sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.0, json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^0.5.1:
  version "0.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^2.1.2:
  version "2.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/json5/-/json5-2.1.3.tgz#c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43"
  integrity sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsonfile/-/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsonfile/-/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"
  integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsprim/-/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jsx-ast-utils@^2.0.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/jsx-ast-utils/-/jsx-ast-utils-2.4.1.tgz#1114a4c1209481db06c690c2b4f488cc665f657e"
  integrity sha1-ERSkwSCUgdsGxpDCtPSIzGZfZX4=
  dependencies:
    array-includes "^3.1.1"
    object.assign "^4.1.0"

kdbush@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kdbush/-/kdbush-3.0.0.tgz#f8484794d47004cc2d85ed3a79353dbe0abc2bf0"
  integrity sha1-+EhHlNRwBMwthe06eTU9vgq8K/A=

kind-of@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kind-of/-/kind-of-1.1.0.tgz#140a3d2d41a36d2efcfa9377b62c24f8495a5c44"
  integrity sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kind-of/-/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw@^1.0.0:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/klaw/-/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  integrity sha1-QIhDO0azsbolnXh4XY6W9zugJDk=
  optionalDependencies:
    graceful-fs "^4.1.9"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kleur/-/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

kuler@0.0.x:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/kuler/-/kuler-0.0.0.tgz#b66bb46b934e550f59d818848e0abba4f7f5553c"
  integrity sha1-tmu0a5NOVQ9Z2BiEjgq7pPf1VTw=
  dependencies:
    colornames "0.0.2"

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

lcid@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lcid/-/lcid-2.0.0.tgz#6ef5d2df60e52f82eb228a4c373e8d1f397253cf"
  integrity sha1-bvXS32DlL4LrIopMNz6NHzlyU88=
  dependencies:
    invert-kv "^2.0.0"

left-pad@^1.3.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/left-pad/-/left-pad-1.3.0.tgz#5b8a3a7765dfe001261dde915589e782f8c94d1e"
  integrity sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/leven/-/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

licenses@0.0.x:
  version "0.0.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/licenses/-/licenses-0.0.20.tgz#f18a57b26a78eaf28a873e2a378a33e81f59d136"
  integrity sha1-8YpXsmp46vKKhz4qN4oz6B9Z0TY=
  dependencies:
    async "0.6.x"
    debug "0.8.x"
    fusing "0.2.x"
    githulk "0.0.x"
    npm-registry "0.1.x"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/load-json-file/-/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/load-json-file/-/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lodash.sortby/-/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lodash.throttle/-/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.unescape@4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lodash.unescape/-/lodash.unescape-4.0.1.tgz#bf2249886ce514cda112fae9218cdc065211fc9c"
  integrity sha1-vyJJiGzlFM2hEvrpIYzcBlIR/Jw=

lodash@^4.16.6, lodash@^4.17.10, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.5, lodash@^4.3.0:
  version "4.17.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lodash/-/lodash-4.17.20.tgz#b44a9b6297bcb698f1c51a3545a2b3b368d59c52"
  integrity sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/log-symbols/-/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

logkitty@^0.6.0:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/logkitty/-/logkitty-0.6.1.tgz#fe29209669d261539cbd6bb998a136fc92a1a05c"
  integrity sha1-/ikglmnSYVOcvWu5mKE2/JKhoFw=
  dependencies:
    ansi-fragments "^0.2.1"
    dayjs "^1.8.15"
    yargs "^12.0.5"

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lru-cache/-/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@~2.2.1:
  version "2.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/lru-cache/-/lru-cache-2.2.4.tgz#6c658619becf14031d0d0b594b16042ce4dc063d"
  integrity sha1-bGWGGb7PFAMdDQtZSxYELOTcBj0=

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/make-dir/-/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

makeerror@1.0.x:
  version "1.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/makeerror/-/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

mana@0.1.x:
  version "0.1.41"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mana/-/mana-0.1.41.tgz#7cb13f73218668654229635c4fc5b17e26f93b7d"
  integrity sha1-fLE/cyGGaGVCKWNcT8Wxfib5O30=
  dependencies:
    assign ">=0.1.7"
    back "1.0.x"
    diagnostics "1.0.x"
    eventemitter3 "1.2.x"
    fusing "1.0.x"
    millisecond "0.1.x"
    request "2.x.x"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
  integrity sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=
  dependencies:
    p-defer "^1.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

mdn-data@2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mdn-data/-/mdn-data-2.0.6.tgz#852dc60fcaa5daa2e8cf6c9189c440ed3e042978"
  integrity sha1-hS3GD8ql2qLoz2yRicRA7T4EKXg=

mem@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mem/-/mem-1.1.0.tgz#5edd52b485ca1d900fe64895505399a0dfa45f76"
  integrity sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y=
  dependencies:
    mimic-fn "^1.0.0"

mem@^4.0.0, mem@^4.3.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mem/-/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
  integrity sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

merge-stream@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/merge-stream/-/merge-stream-1.0.1.tgz#4041202d508a342ba00174008df0c251b8c135e1"
  integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
  dependencies:
    readable-stream "^2.0.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

metro-babel-register@^0.56.0, metro-babel-register@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-babel-register/-/metro-babel-register-0.56.4.tgz#b0c627a1cfdd1bdd768f81af79481754e833a902"
  integrity sha1-sMYnoc/dG912j4GveUgXVOgzqQI=
  dependencies:
    "@babel/core" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/register" "^7.0.0"
    core-js "^2.2.2"
    escape-string-regexp "^1.0.5"

metro-babel-transformer@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-babel-transformer/-/metro-babel-transformer-0.56.4.tgz#fe1d0dc600fcf90201a5bea4d42caea10b801057"
  integrity sha1-/h0NxgD8+QIBpb6k1CyuoQuAEFc=
  dependencies:
    "@babel/core" "^7.0.0"
    metro-source-map "^0.56.4"

metro-cache@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-cache/-/metro-cache-0.56.4.tgz#542f9f8a35f8fb9d5576f46fd3ab4d4f42851a7e"
  integrity sha1-VC+fijX4+51VdvRv06tNT0KFGn4=
  dependencies:
    jest-serializer "^24.4.0"
    metro-core "^0.56.4"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"

metro-config@^0.56.0, metro-config@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-config/-/metro-config-0.56.4.tgz#338fd8165fba59424cec427c1a881757945e57e9"
  integrity sha1-M4/YFl+6WUJM7EJ8GogXV5ReV+k=
  dependencies:
    cosmiconfig "^5.0.5"
    jest-validate "^24.7.0"
    metro "^0.56.4"
    metro-cache "^0.56.4"
    metro-core "^0.56.4"
    pretty-format "^24.7.0"

metro-core@^0.56.0, metro-core@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-core/-/metro-core-0.56.4.tgz#67cc41b3c0bf66e9c2306f50239a1080b1e82312"
  integrity sha1-Z8xBs8C/ZunCMG9QI5oQgLHoIxI=
  dependencies:
    jest-haste-map "^24.7.1"
    lodash.throttle "^4.1.1"
    metro-resolver "^0.56.4"
    wordwrap "^1.0.0"

metro-inspector-proxy@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-inspector-proxy/-/metro-inspector-proxy-0.56.4.tgz#7343ff3c5908af4fd99e96b6d646e24e99816be4"
  integrity sha1-c0P/PFkIr0/Znpa21kbiTpmBa+Q=
  dependencies:
    connect "^3.6.5"
    debug "^2.2.0"
    rxjs "^5.4.3"
    ws "^1.1.5"
    yargs "^9.0.0"

metro-minify-uglify@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-minify-uglify/-/metro-minify-uglify-0.56.4.tgz#13589dfb1d43343608aacb7f78ddfcc052daa63c"
  integrity sha1-E1id+x1DNDYIqst/eN38wFLapjw=
  dependencies:
    uglify-es "^3.1.9"

metro-react-native-babel-preset@^0.56.0, metro-react-native-babel-preset@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.56.4.tgz#dcedc64b7ff5c0734839458e70eb0ebef6d063a8"
  integrity sha1-3O3GS3/1wHNIOUWOcOsOvvbQY6g=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.0.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    react-refresh "^0.4.0"

metro-react-native-babel-transformer@^0.56.0:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-react-native-babel-transformer/-/metro-react-native-babel-transformer-0.56.4.tgz#3c6e48b605c305362ee624e45ff338656e35fc1d"
  integrity sha1-PG5ItgXDBTYu5iTkX/M4ZW41/B0=
  dependencies:
    "@babel/core" "^7.0.0"
    babel-preset-fbjs "^3.1.2"
    metro-babel-transformer "^0.56.4"
    metro-react-native-babel-preset "^0.56.4"
    metro-source-map "^0.56.4"

metro-resolver@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-resolver/-/metro-resolver-0.56.4.tgz#9876f57bca37fd1bfcffd733541e2ee4a89fad7f"
  integrity sha1-mHb1e8o3/Rv8/9czVB4u5KifrX8=
  dependencies:
    absolute-path "^0.0.0"

metro-source-map@^0.56.0, metro-source-map@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-source-map/-/metro-source-map-0.56.4.tgz#868ccac3f3519fe14eca358bc186f63651b2b9bc"
  integrity sha1-hozKw/NRn+FOyjWLwYb2NlGyubw=
  dependencies:
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    invariant "^2.2.4"
    metro-symbolicate "^0.56.4"
    ob1 "^0.56.4"
    source-map "^0.5.6"
    vlq "^1.0.0"

metro-symbolicate@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro-symbolicate/-/metro-symbolicate-0.56.4.tgz#53e9d40beac9049fa75a3e620ddd47d4907ff015"
  integrity sha1-U+nUC+rJBJ+nWj5iDd1H1JB/8BU=
  dependencies:
    invariant "^2.2.4"
    metro-source-map "^0.56.4"
    source-map "^0.5.6"
    through2 "^2.0.1"
    vlq "^1.0.0"

metro@^0.56.0, metro@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/metro/-/metro-0.56.4.tgz#be7e1380ee6ac3552c25ead8098eab261029e4d7"
  integrity sha1-vn4TgO5qw1UsJerYCY6rJhAp5Nc=
  dependencies:
    "@babel/core" "^7.0.0"
    "@babel/generator" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/plugin-external-helpers" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    absolute-path "^0.0.0"
    async "^2.4.0"
    babel-preset-fbjs "^3.1.2"
    buffer-crc32 "^0.2.13"
    chalk "^2.4.1"
    concat-stream "^1.6.0"
    connect "^3.6.5"
    debug "^2.2.0"
    denodeify "^1.2.1"
    eventemitter3 "^3.0.0"
    fbjs "^1.0.0"
    fs-extra "^1.0.0"
    graceful-fs "^4.1.3"
    image-size "^0.6.0"
    invariant "^2.2.4"
    jest-haste-map "^24.7.1"
    jest-worker "^24.6.0"
    json-stable-stringify "^1.0.1"
    lodash.throttle "^4.1.1"
    merge-stream "^1.0.1"
    metro-babel-register "^0.56.4"
    metro-babel-transformer "^0.56.4"
    metro-cache "^0.56.4"
    metro-config "^0.56.4"
    metro-core "^0.56.4"
    metro-inspector-proxy "^0.56.4"
    metro-minify-uglify "^0.56.4"
    metro-react-native-babel-preset "^0.56.4"
    metro-resolver "^0.56.4"
    metro-source-map "^0.56.4"
    metro-symbolicate "^0.56.4"
    mime-types "2.1.11"
    mkdirp "^0.5.1"
    node-fetch "^2.2.0"
    nullthrows "^1.1.0"
    resolve "^1.5.0"
    rimraf "^2.5.4"
    serialize-error "^2.1.0"
    source-map "^0.5.6"
    temp "0.8.3"
    throat "^4.1.0"
    wordwrap "^1.0.0"
    write-file-atomic "^1.2.0"
    ws "^1.1.5"
    xpipe "^1.0.5"
    yargs "^9.0.0"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/micromatch/-/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

millisecond@0.1.x:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/millisecond/-/millisecond-0.1.2.tgz#6cc5ad386241cab8e78aff964f87028eec92dac5"
  integrity sha1-bMWtOGJByrjniv+WT4cCjuyS2sU=

mime-db@1.44.0:
  version "1.44.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime-db/-/mime-db-1.44.0.tgz#fa11c5eb0aca1334b4233cb4d52f10c5a6272f92"
  integrity sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=

"mime-db@>= 1.43.0 < 2":
  version "1.45.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime-db/-/mime-db-1.45.0.tgz#cceeda21ccd7c3a745eba2decd55d4b73e7879ea"
  integrity sha1-zO7aIczXw6dF66LezVXUtz54eeo=

mime-db@~1.23.0:
  version "1.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime-db/-/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"
  integrity sha1-oxtAcK2uon1zLqMzdApk0OyaZlk=

mime-types@2.1.11:
  version "2.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime-types/-/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
  integrity sha1-wlnEcb2oCKhdbNGTtDCl+uRHOzw=
  dependencies:
    mime-db "~1.23.0"

mime-types@^2.1.12, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.27"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime-types/-/mime-types-2.1.27.tgz#47949f98e279ea53119f5722e0f34e529bec009f"
  integrity sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=
  dependencies:
    mime-db "1.44.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.1:
  version "2.4.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mime/-/mime-2.4.6.tgz#e5b407c90db442f2beb5b162373d07b69affa4d1"
  integrity sha1-5bQHyQ20QvK+tbFiNz0Htpr/pNE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.0.0, mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-create-react-context@^0.4.0:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mini-create-react-context/-/mini-create-react-context-0.4.0.tgz#df60501c83151db69e28eac0ef08b4002efab040"
  integrity sha1-32BQHIMVHbaeKOrA7wi0AC76sEA=
  dependencies:
    "@babel/runtime" "^7.5.5"
    tiny-warning "^1.0.3"

minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mixin-deep/-/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.1, mkdirp@^0.5.4:
  version "0.5.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mkdirp/-/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

morgan@^1.9.0:
  version "1.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/morgan/-/morgan-1.10.0.tgz#091778abc1fc47cd3509824653dae1faab6b17d7"
  integrity sha1-CRd4q8H8R801CYJGU9rh+qtrF9c=
  dependencies:
    basic-auth "~2.0.1"
    debug "2.6.9"
    depd "~2.0.0"
    on-finished "~2.3.0"
    on-headers "~1.0.2"

ms@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ms/-/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/mute-stream/-/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

nan@^2.12.1:
  version "2.14.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nan/-/nan-2.14.2.tgz#f5376400695168f4cc694ac9393d0c9585eeea19"
  integrity sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nanomatch/-/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/negotiator/-/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

ngx-take-until-destroy@^5.4.0:
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ngx-take-until-destroy/-/ngx-take-until-destroy-5.4.0.tgz#6c8606ee956584dc947e2eb609c48b8f98b09900"
  integrity sha1-bIYG7pVlhNyUfi62CcSLj5iwmQA=
  dependencies:
    tslib "^1.9.0"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nice-try/-/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/node-fetch/-/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-fetch@^2.2.0, node-fetch@^2.5.0:
  version "2.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/node-fetch/-/node-fetch-2.6.1.tgz#045bd323631f76ed2e2b55573394416b639a0052"
  integrity sha1-BFvTI2Mfdu0uK1VXM5RBa2OaAFI=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/node-modules-regexp/-/node-modules-regexp-1.0.0.tgz#8d9dbe28964a4ac5712e9131642107c71e90ec40"
  integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=

node-notifier@^5.2.1, node-notifier@^5.4.2:
  version "5.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/node-notifier/-/node-notifier-5.4.3.tgz#cb72daf94c93904098e28b9c590fd866e464bd50"
  integrity sha1-y3La+UyTkECY4oucWQ/YZuRkvVA=
  dependencies:
    growly "^1.3.0"
    is-wsl "^1.1.0"
    semver "^5.5.0"
    shellwords "^0.1.1"
    which "^1.3.0"

normalize-package-data@^2.3.2, "normalize-package-data@~1.0.1 || ^2.0.0":
  version "2.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

"npm-package-arg@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0":
  version "6.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/npm-package-arg/-/npm-package-arg-6.1.1.tgz#02168cb0a49a2b75bf988a28698de7b529df5cb7"
  integrity sha1-AhaMsKSaK3W/mIooaY3ntSnfXLc=
  dependencies:
    hosted-git-info "^2.7.1"
    osenv "^0.1.5"
    semver "^5.6.0"
    validate-npm-package-name "^3.0.0"

npm-registry-client@^8.5.1:
  version "8.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/npm-registry-client/-/npm-registry-client-8.6.0.tgz#7f1529f91450732e89f8518e0f21459deea3e4c4"
  integrity sha1-fxUp+RRQcy6J+FGODyFFne6j5MQ=
  dependencies:
    concat-stream "^1.5.2"
    graceful-fs "^4.1.6"
    normalize-package-data "~1.0.1 || ^2.0.0"
    npm-package-arg "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"
    once "^1.3.3"
    request "^2.74.0"
    retry "^0.10.0"
    safe-buffer "^5.1.1"
    semver "2 >=2.2.1 || 3.x || 4 || 5"
    slide "^1.1.3"
    ssri "^5.2.4"
  optionalDependencies:
    npmlog "2 || ^3.1.0 || ^4.0.0"

npm-registry@0.1.x, npm-registry@^0.1.13:
  version "0.1.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/npm-registry/-/npm-registry-0.1.13.tgz#9e5d8b2fdfc1ab5990d47f7debbe231d79a9e822"
  integrity sha1-nl2LL9/Bq1mQ1H99674jHXmp6CI=
  dependencies:
    debug "0.8.x"
    extract-github "0.0.x"
    licenses "0.0.x"
    mana "0.1.x"
    semver "2.2.x"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

"npmlog@2 || ^3.1.0 || ^4.0.0":
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/npmlog/-/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nth-check/-/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nullthrows@^1.1.0:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nullthrows/-/nullthrows-1.1.1.tgz#7818258843856ae971eae4208ad7d7eb19a431b1"
  integrity sha1-eBgliEOFaulx6uQgitfX6xmkMbE=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nwsapi@^2.0.7:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/nwsapi/-/nwsapi-2.2.0.tgz#204879a9e3d068ff2a55139c2c772780681a38b7"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/oauth-sign/-/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

ob1@^0.56.4:
  version "0.56.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ob1/-/ob1-0.56.4.tgz#c4acb3baa42f4993a44b35b2da7c8ef443dcccec"
  integrity sha1-xKyzuqQvSZOkSzWy2nyO9EPczOw=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.8.0:
  version "1.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-inspect/-/object-inspect-1.8.0.tgz#df807e5ecf53a609cc6bfe93eac3cc7be5b3a9d0"
  integrity sha1-34B+Xs9TpgnMa/6T6sPMe+WzqdA=

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-unfreeze@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-unfreeze/-/object-unfreeze-1.1.0.tgz#69628bea1f3c9d29f4eb0ba63b38002d70ea3ce9"
  integrity sha1-aWKL6h88nSn06wumOzgALXDqPOk=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object-visit/-/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0, object.assign@^4.1.1:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object.assign/-/object.assign-4.1.1.tgz#303867a666cdd41936ecdedfb1f8f3e32a478cdd"
  integrity sha1-MDhnpmbN1Bk27N7fsfjz4ypHjN0=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.0"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.fromentries@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object.fromentries/-/object.fromentries-2.0.2.tgz#4a09c9b9bb3843dd0f89acdb517a794d4f355ac9"
  integrity sha1-SgnJubs4Q90PiazbUXp5TU81Wsk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

object.getownpropertydescriptors@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.0.tgz#369bf1f9592d8ab89d712dced5cb81c7c5352649"
  integrity sha1-Npvx+VktiridcS3O1cuBx8U1Jkk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.3.3, once@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.2.0:
  version "6.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/open/-/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.1, optionator@^0.8.3:
  version "0.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/optionator/-/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

options@>=0.0.5:
  version "0.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/options/-/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"
  integrity sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=

ora@^3.4.0:
  version "3.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ora/-/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/os-locale/-/os-locale-2.1.0.tgz#42bc2900a6b5b8bd17376c8e882b65afccf24bf2"
  integrity sha1-QrwpAKa1uL0XN2yOiCtlr8zyS/I=
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-locale@^3.0.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/os-locale/-/os-locale-3.1.0.tgz#a802a6ee17f24c10483ab9935719cef4ed16bf1a"
  integrity sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@^0.1.5:
  version "0.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/osenv/-/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-defer/-/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"
  integrity sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=

p-each-series@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-each-series/-/p-each-series-1.0.0.tgz#930f3d12dd1f50e7434457a22cd6f04ac6ad7f71"
  integrity sha1-kw89Et0fUOdDRFeiLNbwSsatf3E=
  dependencies:
    p-reduce "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-is-promise/-/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"
  integrity sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-limit/-/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-reduce@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-reduce/-/p-reduce-1.0.0.tgz#18c2b0dd936a4690a529f8231f58a0fdb6a47dfa"
  integrity sha1-GMKw3ZNqRpClKfgjH1ig/bakffo=

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parse-json/-/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-node-version@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parse-node-version/-/parse-node-version-1.0.1.tgz#e2b5dbede00e7fa9bc363607f53327e8b073189b"
  integrity sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs=

parse5@4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parse5/-/parse5-4.0.0.tgz#6d78656e3da8d78b4ec0b906f7c08ef1dfe3f608"
  integrity sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=

parse5@^5.0.0:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parse5/-/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-dirname/-/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-parse/-/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@^1.7.0:
  version "1.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-to-regexp/-/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a"
  integrity sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=
  dependencies:
    isarray "0.0.1"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-type/-/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-type/-/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pend@~1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pend/-/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.5, picomatch@^2.2.1:
  version "2.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/picomatch/-/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pify/-/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pirates@^4.0.0, pirates@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pirates/-/pirates-4.0.1.tgz#643a92caf894566f91b2b986d2c66950a8e2fb87"
  integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pkg-dir/-/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pkg-up/-/pkg-up-3.1.0.tgz#100ec235cc150e4fd42519412596a28512a0def5"
  integrity sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=
  dependencies:
    find-up "^3.0.0"

plist@^3.0.0, plist@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/plist/-/plist-3.0.1.tgz#a9b931d17c304e8912ef0ba3bdd6182baf2e1f8c"
  integrity sha1-qbkx0XwwTokS7wujvdYYK68uH4w=
  dependencies:
    base64-js "^1.2.3"
    xmlbuilder "^9.0.7"
    xmldom "0.1.x"

plugin-error@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/plugin-error/-/plugin-error-0.1.2.tgz#3b9bb3335ccf00f425e07437e19276967da47ace"
  integrity sha1-O5uzM1zPAPQl4HQ34ZJ2ln2kes4=
  dependencies:
    ansi-cyan "^0.1.1"
    ansi-red "^0.1.1"
    arr-diff "^1.0.1"
    arr-union "^2.0.1"
    extend-shallow "^1.1.2"

pn@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pn/-/pn-1.1.0.tgz#e2f4cef0e219f463c179ab37463e4e1ecdccbafb"
  integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/posix-character-classes/-/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

predefine@0.1.x:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/predefine/-/predefine-0.1.2.tgz#2aa92b4496bc1f8554e43a45f76bfbe50d33d37f"
  integrity sha1-KqkrRJa8H4VU5DpF92v75Q0z038=
  dependencies:
    extendible "0.1.x"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prettier@1.16.4:
  version "1.16.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/prettier/-/prettier-1.16.4.tgz#73e37e73e018ad2db9c76742e2647e21790c9717"
  integrity sha1-c+N+c+AYrS25x2dC4mR+IXkMlxc=

pretty-format@^24.7.0, pretty-format@^24.9.0:
  version "24.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pretty-format/-/pretty-format-24.9.0.tgz#12fac31b37019a4eea3c11aa9a959eb7628aa7c9"
  integrity sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k=
  dependencies:
    "@jest/types" "^24.9.0"
    ansi-regex "^4.0.0"
    ansi-styles "^3.2.0"
    react-is "^16.8.4"

pretty-format@^25.1.0:
  version "25.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pretty-format/-/pretty-format-25.5.0.tgz#7873c1d774f682c34b8d48b6743a2bf2ac55791a"
  integrity sha1-eHPB13T2gsNLjUi2dDor8qxVeRo=
  dependencies:
    "@jest/types" "^25.5.0"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^16.12.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/progress/-/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prompts@^2.0.1, prompts@^2.3.0:
  version "2.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/prompts/-/prompts-2.3.2.tgz#480572d89ecf39566d2bd3fe2c9fccb7c4c0b068"
  integrity sha1-SAVy2J7POVZtK9P+LJ/Mt8TAsGg=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.4"

prop-types@^15.5.10, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2:
  version "15.7.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/prop-types/-/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
  integrity sha1-UsQedbjIfnK52TYOAga5ncv/psU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/psl/-/psl-1.8.0.tgz#9326f8bcfb013adcc005fdff056acce020e51c24"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

pump@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/punycode/-/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@~6.5.2:
  version "6.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/qs/-/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

react-css-modules@^4.7.11:
  version "4.7.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-css-modules/-/react-css-modules-4.7.11.tgz#e9bc7ac6e3dd7e71c8e46e9d22c1d0abb2110682"
  integrity sha1-6bx6xuPdfnHI5G6dIsHQq7IRBoI=
  dependencies:
    hoist-non-react-statics "^2.5.5"
    lodash "^4.16.6"
    object-unfreeze "^1.1.0"

react-devtools-core@^3.6.3:
  version "3.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-devtools-core/-/react-devtools-core-3.6.3.tgz#977d95b684c6ad28205f0c62e1e12c5f16675814"
  integrity sha1-l32VtoTGrSggXwxi4eEsXxZnWBQ=
  dependencies:
    shell-quote "^1.6.1"
    ws "^3.3.1"

react-is@^16.12.0, react-is@^16.6.0, react-is@^16.7.0, react-is@^16.8.1, react-is@^16.8.4, react-is@^16.9.0:
  version "16.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-native-canvas@^0.1.37:
  version "0.1.37"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-canvas/-/react-native-canvas-0.1.37.tgz#62381f685effcd3263d10f13b4174a8c5b533dcf"
  integrity sha1-YjgfaF7/zTJj0Q8TtBdKjFtTPc8=
  dependencies:
    ctx-polyfill "^1.1.4"

react-native-config@^0.12.0:
  version "0.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-config/-/react-native-config-0.12.0.tgz#910e52226b60e0c9f2b648772eea2a93f8701751"
  integrity sha1-kQ5SImtg4Mnytkh3Luoqk/hwF1E=

react-native-exit-app@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-exit-app/-/react-native-exit-app-1.1.0.tgz#5ec082152974240e2d6a93f1608efb49a4385f21"
  integrity sha1-XsCCFSl0JA4tapPxYI77SaQ4XyE=

react-native-fs@^2.16.6:
  version "2.16.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-fs/-/react-native-fs-2.16.6.tgz#2901789a43210a35a0ef0a098019bbef3af395fd"
  integrity sha1-KQF4mkMhCjWg7woJgBm77zrzlf0=
  dependencies:
    base-64 "^0.1.0"
    utf8 "^3.0.0"

react-native-get-location@^1.4.2:
  version "1.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-get-location/-/react-native-get-location-1.4.3.tgz#d3f161e634f849495fdcd21cab6289583f86f12e"
  integrity sha1-0/Fh5jT4SUlf3NIcq2KJWD+G8S4=

react-native-image-crop-picker@^0.37.3:
  version "0.37.3"
  resolved "https://registry.npmjs.org/react-native-image-crop-picker/-/react-native-image-crop-picker-0.37.3.tgz#f260e40b6a6ba8e98f4db3dde25a8f09e0936385"
  integrity sha512-ih+0pWWRUNEFQyaHwGbH9rqJNOb7EBYMwKJhTY0VmsKIA9E+usfwMmQXAFIfOnee7fTn0A2vOXkBCPQZwyvnQw==

react-native-image-picker@^4.8.4:
  version "4.8.4"
  resolved "https://registry.npmjs.org/react-native-image-picker/-/react-native-image-picker-4.8.4.tgz#ac52f1e265458e944664adf1dcd0a9ee408fe083"
  integrity sha512-Mjh2j/sddyolb16EpmprWzbtyeFvW8Xgzr/8WNi9d6bR2FC/kL78cY/a+7Yzujg5ZDtT1MWys+eWw/qtfwgGiw==

react-native-iphone-x-helper@^1.0.3:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-iphone-x-helper/-/react-native-iphone-x-helper-1.3.0.tgz#84fd13e6b89cc3aa4daa80ec514bf15cb724d86d"
  integrity sha1-hP0T5ricw6pNqoDsUUvxXLck2G0=

react-native-keyboard-aware-scroll-view@^0.9.3:
  version "0.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-keyboard-aware-scroll-view/-/react-native-keyboard-aware-scroll-view-0.9.3.tgz#65ab4cab1a987b486d97924602756fa88b7fbfcc"
  integrity sha1-ZatMqxqYe0htl5JGAnVvqIt/v8w=
  dependencies:
    prop-types "^15.6.2"
    react-native-iphone-x-helper "^1.0.3"

react-native-map-clustering@^3.1.2:
  version "3.3.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-map-clustering/-/react-native-map-clustering-3.3.9.tgz#f1b7ad8a60dd9da2e794a1ff401f84f88896e80b"
  integrity sha1-8betimDdnaLnlKH/QB+E+IiW6As=
  dependencies:
    "@mapbox/geo-viewport" "^0.4.0"
    supercluster "^7.0.0"

react-native-maps@0.25:
  version "0.25.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-maps/-/react-native-maps-0.25.0.tgz#81bc51eb50e33811a9e1c345cc48869413ead67d"
  integrity sha1-gbxR61DjOBGp4cNFzEiGlBPq1n0=

react-native-root-siblings@^4.0.0:
  version "4.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-root-siblings/-/react-native-root-siblings-4.0.6.tgz#6dd7eedb725faacd7ba19c159dd279cf2e6d8476"
  integrity sha1-bdfu23Jfqs17oZwVndJ5zy5thHY=
  dependencies:
    static-container "^1.5.1"

react-native-root-toast@^3.2.0:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-root-toast/-/react-native-root-toast-3.2.1.tgz#f456d029f5c5b76e4b686334f25371465478a3be"
  integrity sha1-9FbQKfXFt25LaGM08lNxRlR4o74=
  dependencies:
    prop-types "^15.5.10"
    react-native-root-siblings "^4.0.0"

react-native-sqlite-storage@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-sqlite-storage/-/react-native-sqlite-storage-4.1.0.tgz#641780eef06bc90b853d3dcd628c0fe54b656d7b"
  integrity sha1-ZBeA7vBryQuFPT3NYowP5UtlbXs=

react-native-svg@^11.0.1:
  version "11.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-svg/-/react-native-svg-11.0.1.tgz#e92e7e9f9cb2604333fd5014fd4edcf62fea6496"
  integrity sha1-6S5+n5yyYEMz/VAU/U7c9i/qZJY=
  dependencies:
    css-select "^2.1.0"
    css-tree "^1.0.0-alpha.39"

react-native-webview@^8.1.2:
  version "8.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-webview/-/react-native-webview-8.2.1.tgz#23f9e156a3361fee316d54b60e64da1a27f9f73b"
  integrity sha1-I/nhVqM2H+4xbVS2DmTaGif59zs=
  dependencies:
    escape-string-regexp "2.0.0"
    invariant "2.2.4"
    rnpm-plugin-windows "^0.5.1-0"

react-native-windows@^0.62.0-0:
  version "0.62.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native-windows/-/react-native-windows-0.62.13.tgz#0908186aa77c6cd64808540124c26c802770c42a"
  integrity sha1-CQgYaqd8bNZICFQBJMJsgCdwxCo=
  dependencies:
    "@babel/runtime" "^7.8.4"
    cli-spinners "^2.2.0"
    create-react-class "^15.6.3"
    envinfo "^7.5.0"
    fbjs "^1.0.0"
    glob "^7.1.1"
    ora "^3.4.0"
    prop-types "^15.7.2"
    regenerator-runtime "^0.13.2"
    shelljs "^0.7.8"
    username "^5.1.0"
    uuid "^3.3.2"
    xml-parser "^1.2.1"

react-native@0.61.5:
  version "0.61.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-native/-/react-native-0.61.5.tgz#6e21acb56cbd75a3baeb1f70201a66f42600bba8"
  integrity sha1-biGstWy9daO66x9wIBpm9CYAu6g=
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@react-native-community/cli" "^3.0.0"
    "@react-native-community/cli-platform-android" "^3.0.0"
    "@react-native-community/cli-platform-ios" "^3.0.0"
    abort-controller "^3.0.0"
    art "^0.10.0"
    base64-js "^1.1.2"
    connect "^3.6.5"
    create-react-class "^15.6.3"
    escape-string-regexp "^1.0.5"
    event-target-shim "^5.0.1"
    fbjs "^1.0.0"
    fbjs-scripts "^1.1.0"
    hermes-engine "^0.2.1"
    invariant "^2.2.4"
    jsc-android "^245459.0.0"
    metro-babel-register "^0.56.0"
    metro-react-native-babel-transformer "^0.56.0"
    metro-source-map "^0.56.0"
    nullthrows "^1.1.0"
    pretty-format "^24.7.0"
    promise "^7.1.1"
    prop-types "^15.7.2"
    react-devtools-core "^3.6.3"
    react-refresh "^0.4.0"
    regenerator-runtime "^0.13.2"
    scheduler "0.15.0"
    stacktrace-parser "^0.1.3"
    whatwg-fetch "^3.0.0"

react-refresh@^0.4.0:
  version "0.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-refresh/-/react-refresh-0.4.3.tgz#966f1750c191672e76e16c2efa569150cc73ab53"
  integrity sha1-lm8XUMGRZy524Wwu+laRUMxzq1M=

react-router-native@^5.1.2:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-router-native/-/react-router-native-5.2.0.tgz#40c40856a8d1e3b189c7ad5c1be9b1ead4dbacfd"
  integrity sha1-QMQIVqjR47GJx61cG+mx6tTbrP0=
  dependencies:
    prop-types "^15.6.1"
    react-router "5.2.0"

react-router@5.2.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-router/-/react-router-5.2.0.tgz#424e75641ca8747fbf76e5ecca69781aa37ea293"
  integrity sha1-Qk51ZByodH+/duXsyml4GqN+opM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    history "^4.9.0"
    hoist-non-react-statics "^3.1.0"
    loose-envify "^1.3.1"
    mini-create-react-context "^0.4.0"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.2"
    react-is "^16.6.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"

react-test-renderer@16.9.0:
  version "16.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react-test-renderer/-/react-test-renderer-16.9.0.tgz#7ed657a374af47af88f66f33a3ef99c9610c8ae9"
  integrity sha1-ftZXo3SvR6+I9m8zo++ZyWEMiuk=
  dependencies:
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    react-is "^16.9.0"
    scheduler "^0.15.0"

react@16.9.0:
  version "16.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/react/-/react-16.9.0.tgz#40ba2f9af13bc1a38d75dbf2f4359a5185c4f7aa"
  integrity sha1-QLovmvE7waONddvy9DWaUYXE96o=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/read-pkg-up/-/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg-up@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/read-pkg-up/-/read-pkg-up-4.0.0.tgz#1b221c6088ba7799601c808f91161c66e58f8978"
  integrity sha1-GyIcYIi6d5lgHICPkRYcZuWPiXg=
  dependencies:
    find-up "^3.0.0"
    read-pkg "^3.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/read-pkg/-/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/read-pkg/-/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.2.2, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/readable-stream/-/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@^2.0.0:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/readdirp/-/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

realpath-native@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/realpath-native/-/realpath-native-1.1.0.tgz#2003294fea23fb0672f2476ebe22fcf498a2d65c"
  integrity sha1-IAMpT+oj+wZy8kduviL89Jii1lw=
  dependencies:
    util.promisify "^1.0.0"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rechoir/-/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redi-component-utils@^1.0.19:
  version "1.0.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/redi-component-utils/-/redi-component-utils-1.0.21.tgz#ce13b8a2c0bb49bec17076a1aeb5ed2fcc27a55f"
  integrity sha1-zhO4osC7Sb7BcHahrrXtL8wnpV8=

redi-form@^1.6.6:
  version "1.6.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/redi-form/-/redi-form-1.6.8.tgz#fa5fd03cf8a03ab335d611fde635f91874311c12"
  integrity sha1-+l/QPPigOrM11hH95jX5GHQxHBI=

redi-http@^2.2.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/redi-http/-/redi-http-2.2.1.tgz#e56d7737c500d83b3618eae5317efe9d1b5a37d0"
  integrity sha1-5W13N8UA2Ds2GOrlMX7+nRtaN9A=

redi-ui-utils@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/redi-ui-utils/-/redi-ui-utils-2.0.2.tgz#e97ff4c3e8f205669fcdfd89164b5739d35e58d7"
  integrity sha1-6X/0w+jyBWafzf2JFktXOdNeWNc=

regenerate-unicode-properties@^8.2.0:
  version "8.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz#e5de7111d655e7ba60c057dbe9ff37c87e65cdec"
  integrity sha1-5d5xEdZV57pgwFfb6f83yH5lzew=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.4.0:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regenerate/-/regenerate-1.4.1.tgz#cad92ad8e6b591773485fbe05a485caf4f457e6f"
  integrity sha1-ytkq2Oa1kXc0hfvgWkhcr09Ffm8=

regenerator-runtime@^0.13.2, regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regenerator-runtime/-/regenerator-runtime-0.13.7.tgz#cac2dacc8a1ea675feaabaeb8ae833898ae46f55"
  integrity sha1-ysLazIoepnX+qrrriugziYrkb1U=

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regenerator-transform/-/regenerator-transform-0.14.5.tgz#c98da154683671c9c4dcb16ece736517e1b7feb4"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regexpp/-/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpp@^3.0.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regexpp/-/regexpp-3.1.0.tgz#206d0ad0a5648cffbdb8ae46438f3dc51c9f78e2"
  integrity sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=

regexpu-core@^4.7.1:
  version "4.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regexpu-core/-/regexpu-core-4.7.1.tgz#2dea5a9a07233298fbf0db91fa9abc4c6e0f8ad6"
  integrity sha1-LepamgcjMpj78NuR+pq8TG4PitY=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regjsgen@^0.5.1:
  version "0.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regjsgen/-/regjsgen-0.5.2.tgz#92ff295fb1deecbf6ecdab2543d207e91aa33733"
  integrity sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=

regjsparser@^0.6.4:
  version "0.6.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/regjsparser/-/regjsparser-0.6.4.tgz#a769f8684308401a66e9b529d2436ff4d0666272"
  integrity sha1-p2n4aEMIQBpm6bUp0kNv9NBmYnI=
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/repeat-element/-/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request-ip@~2.0.1:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/request-ip/-/request-ip-2.0.2.tgz#deeae6d4af21768497db8cd05fa37143f8f1257e"
  integrity sha1-3urm1K8hdoSX24zQX6NxQ/jxJX4=
  dependencies:
    is_js "^0.9.0"

request-promise-core@1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/request-promise-core/-/request-promise-core-1.1.4.tgz#3eedd4223208d419867b78ce815167d10593a22f"
  integrity sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=
  dependencies:
    lodash "^4.17.19"

request-promise-native@^1.0.5:
  version "1.0.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/request-promise-native/-/request-promise-native-1.0.9.tgz#e407120526a5efdc9a39b28a5679bf47b9d9dc28"
  integrity sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=
  dependencies:
    request-promise-core "1.1.4"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@2.x.x, request@^2.74.0, request@^2.87.0, request@^2.88.0:
  version "2.88.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/request/-/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"
  integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/require-main-filename/-/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

reselect@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/reselect/-/reselect-4.0.0.tgz#f2529830e5d3d0e021408b246a206ef4ea4437f7"
  integrity sha1-8lKYMOXT0OAhQIskaiBu9OpEN/c=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve-cwd/-/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve-pathname/-/resolve-pathname-3.0.0.tgz#99d02224d3cf263689becbb393bc560313025dcd"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@1.1.7:
  version "1.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.13.1, resolve@^1.3.2, resolve@^1.5.0, resolve@^1.8.1, resolve@^1.9.0:
  version "1.18.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/resolve/-/resolve-1.18.1.tgz#018fcb2c5b207d2a6424aee361c5a266da8f4130"
  integrity sha1-AY/LLFsgfSpkJK7jYcWiZtqPQTA=
  dependencies:
    is-core-module "^2.0.0"
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/restore-cursor/-/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.10.0:
  version "0.10.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/retry/-/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"
  integrity sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/reusify/-/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rimraf/-/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^2.5.4, rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@~2.2.6:
  version "2.2.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rimraf/-/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"
  integrity sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=

rnpm-plugin-windows@^0.5.1-0:
  version "0.5.1-0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rnpm-plugin-windows/-/rnpm-plugin-windows-0.5.1-0.tgz#9ffdd38653c6024c538a98a1046a37625d56eddb"
  integrity sha1-n/3ThlPGAkxTipihBGo3Yl1W7ds=
  dependencies:
    chalk "^1.1.3"
    extract-zip "^1.6.7"
    fs-extra "^7.0.1"
    npm-registry "^0.1.13"
    prompts "^2.3.0"
    request "^2.88.0"
    semver "^6.1.1"
    valid-url "^1.0.9"

rollbar-react-native@^0.8.1:
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rollbar-react-native/-/rollbar-react-native-0.8.1.tgz#f41a777f16ea800eae0e6fbb84bb286b4407e889"
  integrity sha1-9Bp3fxbqgA6uDm+7hLsoa0QH6Ik=
  dependencies:
    buffer "^4.9.1 || ^5.0.7"
    rollbar "^2.16.1"
    url "^0.11.0"

rollbar@^2.16.1:
  version "2.19.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rollbar/-/rollbar-2.19.4.tgz#eda4145cb459203bd3e9ccd885bfb5c5c3f6f2a2"
  integrity sha1-7aQUXLRZIDvT6czYhb+1xcP28qI=
  dependencies:
    async "~1.2.1"
    console-polyfill "0.3.0"
    error-stack-parser "^2.0.4"
    json-stringify-safe "~5.0.0"
    lru-cache "~2.2.1"
    request-ip "~2.0.1"
    source-map "^0.5.7"
    uuid "3.0.x"
  optionalDependencies:
    decache "^3.0.5"

rsvp@^4.8.4:
  version "4.8.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rsvp/-/rsvp-4.8.5.tgz#c8f155311d167f68f21e168df71ec5b083113734"
  integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=

run-async@^2.2.0, run-async@^2.4.0:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/run-async/-/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.1.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/run-parallel/-/run-parallel-1.1.9.tgz#c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679"
  integrity sha1-yd06fPn0ssS2JE4XOm7YZuYd1nk=

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
  integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rx-lite/-/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"
  integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=

rxjs@6.3.3:
  version "6.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rxjs/-/rxjs-6.3.3.tgz#3c6a7fa420e844a81390fb1158a9ec614f4bad55"
  integrity sha1-PGp/pCDoRKgTkPsRWKnsYU9LrVU=
  dependencies:
    tslib "^1.9.0"

rxjs@^5.4.3:
  version "5.5.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rxjs/-/rxjs-5.5.12.tgz#6fa61b8a77c3d793dbaf270bee2f43f652d741cc"
  integrity sha1-b6YbinfD15PbrycL7i9D9lLXQcw=
  dependencies:
    symbol-observable "1.0.1"

rxjs@^6.4.0, rxjs@^6.6.0:
  version "6.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/rxjs/-/rxjs-6.6.3.tgz#8ca84635c4daa900c0d3967a6ee7ac60271ee552"
  integrity sha1-jKhGNcTaqQDA05Z6buesYCce5VI=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@^5.0.1, safe-buffer@^5.1.1, safe-buffer@^5.1.2:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sane/-/sane-4.1.0.tgz#ed881fd922733a6c461bc189dc2b6c006f3ffded"
  integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sax@^1.2.1, sax@^1.2.4:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

scheduler@0.15.0, scheduler@^0.15.0:
  version "0.15.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/scheduler/-/scheduler-0.15.0.tgz#6bfcf80ff850b280fed4aeecc6513bc0b4f17f8e"
  integrity sha1-a/z4D/hQsoD+1K7sxlE7wLTxf44=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schematics-utilities@^1.1.1:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/schematics-utilities/-/schematics-utilities-1.1.3.tgz#3f78f62d31630afeafbdf0ca6ea5f41897efa236"
  integrity sha1-P3j2LTFjCv6vvfDKbqX0GJfvojY=
  dependencies:
    "@angular-devkit/core" "^7.3.6"
    "@angular-devkit/schematics" "^7.3.6"
    npm-registry-client "^8.5.1"
    parse5 "^5.0.0"
    rxjs "^6.4.0"
    typescript "^3.3.3333"

"semver@2 >=2.2.1 || 3.x || 4 || 5", "semver@2 || 3 || 4 || 5", semver@^5.1.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/semver/-/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@2.2.x:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/semver/-/semver-2.2.1.tgz#7941182b3ffcc580bff1c17942acdf7951c0d213"
  integrity sha1-eUEYKz/8xYC/8cF5QqzfeVHA0hM=

semver@5.5.0:
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/semver/-/semver-5.5.0.tgz#dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab"
  integrity sha1-3Eu8emyp2Rbe5dQ1FvAJK1j3uKs=

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.2.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/semver/-/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.3.2:
  version "7.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/semver/-/semver-7.3.2.tgz#604962b052b81ed0786aae84389ffba70ffd3938"
  integrity sha1-YElisFK4HtB4aq6EOJ/7pw/9OTg=

send@0.17.1:
  version "0.17.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/send/-/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-error@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/serialize-error/-/serialize-error-2.1.0.tgz#50b679d5635cdf84667bdc8e59af4e5b81d5f60a"
  integrity sha1-ULZ51WNc34Rme9yOWa9OW4HV9go=

serve-static@^1.13.1:
  version "1.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/serve-static/-/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/setprototypeof/-/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shell-quote@1.6.1:
  version "1.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shell-quote/-/shell-quote-1.6.1.tgz#f4781949cce402697127430ea3b3c5476f481767"
  integrity sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=
  dependencies:
    array-filter "~0.0.0"
    array-map "~0.0.0"
    array-reduce "~0.0.0"
    jsonify "~0.0.0"

shell-quote@^1.6.1:
  version "1.7.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shell-quote/-/shell-quote-1.7.2.tgz#67a7d02c76c9da24f99d20808fcaded0e0e04be2"
  integrity sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I=

shelljs@^0.7.8:
  version "0.7.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shelljs/-/shelljs-0.7.8.tgz#decbcf874b0d1e5fb72e14b164a9683048e9acb3"
  integrity sha1-3svPh0sNHl+3LhSxZKloMEjprLM=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/shellwords/-/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/signal-exit/-/signal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

simple-plist@^1.0.0:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/simple-plist/-/simple-plist-1.1.1.tgz#54367ca28bc5996a982c325c1c4a4c1a05f4047c"
  integrity sha1-VDZ8oovFmWqYLDJcHEpMGgX0BHw=
  dependencies:
    bplist-creator "0.0.8"
    bplist-parser "0.2.0"
    plist "^3.0.1"

sisteransi@^1.0.4:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sisteransi/-/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/slash/-/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^2.0.0, slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/slice-ansi/-/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slide@^1.1.3, slide@^1.1.5:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/slide/-/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"
  integrity sha1-VusCfWW00tzmyy4tMsTUr8nh1wc=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map-resolve/-/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.16, source-map-support@^0.5.6:
  version "0.5.19"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map-support/-/source-map-support-0.5.19.tgz#a98b62f86dcaf4f67399648c085291ab9e8fed61"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map-url/-/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@0.7.3:
  version "0.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map/-/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

source-map@^0.5.0, source-map@^0.5.6, source-map@^0.5.7:
  version "0.5.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/spdx-correct/-/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/spdx-license-ids/-/spdx-license-ids-3.0.6.tgz#c80757383c28abf7296744998cbc106ae8b854ce"
  integrity sha1-yAdXODwoq/cpZ0SZjLwQaui4VM4=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sshpk/-/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^5.2.4:
  version "5.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ssri/-/ssri-5.3.0.tgz#ba3872c9c6d33a0704a7d71ff045e5ec48999d06"
  integrity sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=
  dependencies:
    safe-buffer "^5.1.1"

stack-utils@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/stack-utils/-/stack-utils-1.0.2.tgz#33eba3897788558bebfc2db059dc158ec36cebb8"
  integrity sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g=

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/stackframe/-/stackframe-1.2.0.tgz#52429492d63c62eb989804c11552e3d22e779303"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

stacktrace-parser@^0.1.3:
  version "0.1.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/stacktrace-parser/-/stacktrace-parser-0.1.10.tgz#29fb0cae4e0d0b85155879402857a1639eb6051a"
  integrity sha1-KfsMrk4NC4UVWHlAKFehY562BRo=
  dependencies:
    type-fest "^0.7.1"

static-container@^1.5.1:
  version "1.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/static-container/-/static-container-1.5.1.tgz#9d7a94e04dea864539a7b6a1304843ada740dc19"
  integrity sha1-nXqU4E3qhkU5p7ahMEhDradA3Bk=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/stealthy-require/-/stealthy-require-1.1.1.tgz#35b09875b4ff49f26a777e509b3090a3226bf24b"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-buffers@~2.2.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/stream-buffers/-/stream-buffers-2.2.0.tgz#91d5f5130d1cef96dcfa7f726945188741d09ee4"
  integrity sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ=

string-length@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string-length/-/string-length-2.0.0.tgz#d40dbb686a3ace960c1cffca562bf2c45f8363ed"
  integrity sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=
  dependencies:
    astral-regex "^1.0.0"
    strip-ansi "^4.0.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string-width/-/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string-width/-/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.trimend@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.2.tgz#6ddd9a8796bc714b489a3ae22246a208f37bfa46"
  integrity sha1-bd2ah5a8cUtImjriIkaiCPN7+kY=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"

string.prototype.trimstart@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.2.tgz#22d45da81015309cd0cdd79787e8919fc5c613e7"
  integrity sha1-ItRdqBAVMJzQzdeXh+iRn8XGE+c=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-ansi/-/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-ansi/-/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

sudo-prompt@^9.0.0:
  version "9.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/sudo-prompt/-/sudo-prompt-9.2.1.tgz#77efb84309c9ca489527a4e749f287e6bdd52afd"
  integrity sha1-d++4QwnJykiVJ6TnSfKH5r3VKv0=

supercluster@^7.0.0:
  version "7.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/supercluster/-/supercluster-7.1.0.tgz#f0a457426ec0ab95d69c5f03b51e049774b94479"
  integrity sha1-8KRXQm7Aq5XWnF8DtR4El3S5RHk=
  dependencies:
    kdbush "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/supports-color/-/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

symbol-observable@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/symbol-observable/-/symbol-observable-1.0.1.tgz#8340fc4702c3122df5d22288f88283f513d3fdd4"
  integrity sha1-g0D8RwLDEi310iKI+IKD9RPT/dQ=

symbol-tree@^3.2.2:
  version "3.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/symbol-tree/-/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

table@^5.2.3:
  version "5.4.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/table/-/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

temp@0.8.3:
  version "0.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/temp/-/temp-0.8.3.tgz#e0c6bc4d26b903124410e4fed81103014dfc1f59"
  integrity sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k=
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

test-exclude@^5.2.3:
  version "5.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/test-exclude/-/test-exclude-5.2.3.tgz#c3d3e1e311eb7ee405e092dac10aefd09091eac0"
  integrity sha1-w9Ph4xHrfuQF4JLawQrv0JCR6sA=
  dependencies:
    glob "^7.1.3"
    minimatch "^3.0.4"
    read-pkg-up "^4.0.0"
    require-main-filename "^2.0.0"

text-hex@0.0.x:
  version "0.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/text-hex/-/text-hex-0.0.0.tgz#578fbc85a6a92636e42dd17b41d0218cce9eb2b3"
  integrity sha1-V4+8haapJjbkLdF7QdAhjM6esrM=

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^4.0.0, throat@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/throat/-/throat-4.1.0.tgz#89037cbc92c56ab18926e6ba4cbb200e15672a6a"
  integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=

through2@^2.0.0, through2@^2.0.1:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

time-stamp@^1.0.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/time-stamp/-/time-stamp-1.1.0.tgz#764a5a11af50561921b133f3b44e618687e0f5c3"
  integrity sha1-dkpaEa9QVhkhsTPztE5hhofg9cM=

tiny-invariant@^1.0.2:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tiny-invariant/-/tiny-invariant-1.1.0.tgz#634c5f8efdc27714b7f386c35e6760991d230875"
  integrity sha1-Y0xfjv3CdxS384bDXmdgmR0jCHU=

tiny-warning@^1.0.0, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tiny-warning/-/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tmpl/-/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/toidentifier/-/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

tough-cookie@^2.3.3, tough-cookie@^2.3.4, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tough-cookie/-/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tr46/-/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

traverse-chain@~0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/traverse-chain/-/traverse-chain-0.1.0.tgz#61dbc2d53b69ff6091a12a168fd7d433107e40f1"
  integrity sha1-YdvC1Ttp/2CRoSoWj9fUMxB+QPE=

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tsutils@^3.17.1, tsutils@^3.7.0:
  version "3.17.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tsutils/-/tsutils-3.17.1.tgz#ed719917f11ca0dee586272b2ac49e015a2dd759"
  integrity sha1-7XGZF/EcoN7lhicrKsSeAVot11k=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.11.0:
  version "0.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/type-fest/-/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.7.1:
  version "0.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/type-fest/-/type-fest-0.7.1.tgz#8dda65feaf03ed78f0a3f9678f1869147f7c5c48"
  integrity sha1-jdpl/q8D7Xjwo/lnjxhpFH98XEg=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/type-fest/-/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@^3.3.3333, typescript@^3.7.3:
  version "3.9.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/typescript/-/typescript-3.9.7.tgz#98d600a5ebdc38f40cb277522f12dc800e9e25fa"
  integrity sha1-mNYApevcOPQMsndSLxLcgA6eJfo=

ua-parser-js@^0.7.18:
  version "0.7.22"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ua-parser-js/-/ua-parser-js-0.7.22.tgz#960df60a5f911ea8f1c818f3747b99c6e177eae3"
  integrity sha1-lg32Cl+RHqjxyBjzdHuZxuF36uM=

uglify-es@^3.1.9:
  version "3.3.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/uglify-es/-/uglify-es-3.3.9.tgz#0c1c4f0700bed8dbc124cdb304d2592ca203e677"
  integrity sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc=
  dependencies:
    commander "~2.13.0"
    source-map "~0.6.1"

ultron@1.0.x:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ultron/-/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"
  integrity sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po=

ultron@~1.1.0:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ultron/-/ultron-1.1.1.tgz#9fe1536a10a664a65266a1e3ccf85fd36302bc9c"
  integrity sha1-n+FTahCmZKZSZqHjzPhf02MCvJw=

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.2.0.tgz#0d91f600eeeb3096aa962b1d6fc88876e64ea531"
  integrity sha1-DZH2AO7rMJaqlisdb8iIduZOpTE=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz#dd57a99f6207bedff4628abefb94c50db941c8f4"
  integrity sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/union-value/-/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/universalify/-/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.0.5:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/upath/-/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

uri-js@^4.2.2:
  version "4.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/uri-js/-/uri-js-4.4.0.tgz#aa714261de793e8a82347a7bcc9ce74e86f28602"
  integrity sha1-qnFCYd55PoqCNHp7zJznTobyhgI=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url@^0.11.0:
  version "0.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/use/-/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

username@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/username/-/username-5.1.0.tgz#a7f9325adce2d0166448cdd55d4985b1360f2508"
  integrity sha1-p/kyWtzi0BZkSM3VXUmFsTYPJQg=
  dependencies:
    execa "^1.0.0"
    mem "^4.3.0"

utf8@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/utf8/-/utf8-3.0.0.tgz#f052eed1364d696e769ef058b183df88c87f69d1"
  integrity sha1-8FLu0TZNaW52nvBYsYPfiMh/adE=

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/util.promisify/-/util.promisify-1.0.1.tgz#6baf7774b80eeb0f7520d8b81d07982a59abbaee"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@3.0.x:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/uuid/-/uuid-3.0.1.tgz#6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1"
  integrity sha1-ZUS7ot/ajBzxfmKaOjBeK7H+5sE=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache@^2.0.3:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/v8-compile-cache/-/v8-compile-cache-2.1.1.tgz#54bc3cdd43317bca91e35dcaf305b1a7237de745"
  integrity sha1-VLw83UMxe8qR413K8wWxpyN950U=

valid-url@^1.0.9:
  version "1.0.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/valid-url/-/valid-url-1.0.9.tgz#1c14479b40f1397a75782f115e4086447433a200"
  integrity sha1-HBRHm0DxOXp1eC8RXkCGRHQzogA=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz#5fa912d81eb7d0c74afc140de7317f0ca7df437e"
  integrity sha1-X6kS2B630MdK/BQN5zF/DKffQ34=
  dependencies:
    builtins "^1.0.3"

value-equal@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/value-equal/-/value-equal-1.0.1.tgz#1e0b794c734c5c0cade179c437d356d931a34d6c"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

vary@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vlq@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/vlq/-/vlq-1.0.1.tgz#c003f6e7c0b4c1edd623fd6ee50bbc0d6a1de468"
  integrity sha1-wAP258C0we3WI/1u5Qu8DWod5Gg=

w3c-hr-time@^1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz#0a89cdf5cc15822df9c360543676963e0cc308cd"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/walker/-/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wcwidth/-/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/webidl-conversions/-/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.3:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@>=0.10.0, whatwg-fetch@^3.0.0:
  version "3.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/whatwg-fetch/-/whatwg-fetch-3.4.1.tgz#e5f871572d6879663fa5674c8f833f15a8425ab3"
  integrity sha1-5fhxVy1oeWY/pWdMj4M/FahCWrM=

whatwg-mimetype@^2.1.0, whatwg-mimetype@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^6.4.1:
  version "6.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/whatwg-url/-/whatwg-url-6.5.0.tgz#f2df02bff176fd65070df74ad5ccbb5a199965a8"
  integrity sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/whatwg-url/-/whatwg-url-7.1.0.tgz#c2c492f1eca612988efd3d2266be1b9fc6170d06"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/which-module/-/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9, which@^1.3.0:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wide-align/-/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
  dependencies:
    string-width "^1.0.2 || 2"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/word-wrap/-/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wrap-ansi/-/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@2.4.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/write-file-atomic/-/write-file-atomic-2.4.1.tgz#d0b05463c188ae804396fd5ab2a370062af87529"
  integrity sha1-0LBUY8GIroBDlv1asqNwBir4dSk=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^1.2.0:
  version "1.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/write-file-atomic/-/write-file-atomic-1.3.4.tgz#f807a4f0b1d9e913ae7a48112e6cc3af1991b45f"
  integrity sha1-+Aek8LHZ6ROuekgRLmzDrxmRtF8=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    slide "^1.1.5"

write@1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/write/-/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^1.1.0, ws@^1.1.5:
  version "1.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ws/-/ws-1.1.5.tgz#cbd9e6e75e09fc5d2c90015f21f0c40875e0dd51"
  integrity sha1-y9nm514J/F0skAFfIfDECHXg3VE=
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

ws@^3.3.1:
  version "3.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ws/-/ws-3.3.3.tgz#f1cf84fe2d5e901ebce94efaece785f187a228f2"
  integrity sha1-8c+E/i1ekB686U767OeF8YeiKPI=
  dependencies:
    async-limiter "~1.0.0"
    safe-buffer "~5.1.0"
    ultron "~1.1.0"

ws@^5.2.0:
  version "5.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/ws/-/ws-5.2.2.tgz#dffef14866b8e8dc9133582514d1befaf96e980f"
  integrity sha1-3/7xSGa46NyRM1glFNG++vlumA8=
  dependencies:
    async-limiter "~1.0.0"

xcode@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xcode/-/xcode-2.1.0.tgz#bab64a7e954bb50ca8d19da7e09531c65a43ecfe"
  integrity sha1-urZKfpVLtQyo0Z2n4JUxxlpD7P4=
  dependencies:
    simple-plist "^1.0.0"
    uuid "^3.3.2"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xml-name-validator/-/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml-parser@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xml-parser/-/xml-parser-1.2.1.tgz#c31f4c34f2975db82ad013222120592736156fcd"
  integrity sha1-wx9MNPKXXbgq0BMiISBZJzYVb80=
  dependencies:
    debug "^2.2.0"

xmlbuilder@^9.0.7:
  version "9.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xmlbuilder/-/xmlbuilder-9.0.7.tgz#132ee63d2ec5565c557e20f4c22df9aca686b10d"
  integrity sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0=

xmldoc@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xmldoc/-/xmldoc-1.1.2.tgz#6666e029fe25470d599cd30e23ff0d1ed50466d7"
  integrity sha1-ZmbgKf4lRw1ZnNMOI/8NHtUEZtc=
  dependencies:
    sax "^1.2.1"

xmldom@0.1.x:
  version "0.1.31"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xmldom/-/xmldom-0.1.31.tgz#b76c9a1bd9f0a9737e5a72dc37231cf38375e2ff"
  integrity sha1-t2yaG9nwqXN+WnLcNyMc84N14v8=

xpipe@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xpipe/-/xpipe-1.0.5.tgz#8dd8bf45fc3f7f55f0e054b878f43a62614dafdf"
  integrity sha1-jdi/Rfw/f1Xw4FS4ePQ6YmFNr98=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"
  integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=

"y18n@^3.2.1 || ^4.0.0", y18n@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/y18n/-/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yargs-parser@^11.1.1:
  version "11.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs-parser/-/yargs-parser-11.1.1.tgz#879a0865973bca9f6bab5cbdf3b1c67ec7d3bcf4"
  integrity sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs-parser/-/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^7.0.0:
  version "7.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs-parser/-/yargs-parser-7.0.0.tgz#8d0ac42f16ea55debd332caf4c4038b3e3f5dfd9"
  integrity sha1-jQrELxbqVd69MyyvTEA4s+P139k=
  dependencies:
    camelcase "^4.1.0"

yargs@^12.0.5:
  version "12.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs/-/yargs-12.0.5.tgz#05f5997b609647b64f66b81e3b4b10a368e7ad13"
  integrity sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM=
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^1.0.1"
    os-locale "^3.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1 || ^4.0.0"
    yargs-parser "^11.1.1"

yargs@^13.3.0:
  version "13.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs/-/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^9.0.0:
  version "9.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yargs/-/yargs-9.0.1.tgz#52acc23feecac34042078ee78c0c007f5085db4c"
  integrity sha1-UqzCP+7Kw0BCB47njAwAf1CF20w=
  dependencies:
    camelcase "^4.1.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    read-pkg-up "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^7.0.0"

yauzl@^2.10.0:
  version "2.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/e9969204-1895-439f-b99e-995965d8088b/npm/yauzl/-/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"
