import React from 'react';
import Toast from 'react-native-root-toast';
import { View } from 'react-native';
import styles from './styles';
import theme from 'config/styles/theme';
import { HttpError } from 'redi-http';

let _instance: ToastProvider;

export default class ToastProvider extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		_instance = this;
		this.state = {
			visible: false,
			backgroundColour: theme.PRIMARY_MEDIUM_BLUE,
			message: ''
		};
	}

	componentDidMount() {
		this._mounted = true;
	}
	componentWillUnmount() {
		this._mounted = false;
	}
	show(type: ToastType, message: string) {
		if (this._mounted) {
			let backgroundColour = '';
			switch (type) {
				case 'error':
					backgroundColour = 'red';
					break;
				case 'success':
					backgroundColour = 'green';
					break;
				case 'warning':
					backgroundColour = 'orange';
					break;
				case 'info':
				default:
					backgroundColour = theme.PRIMARY_MEDIUM_BLUE;
					break;
			}

			this.setState({ visible: true, message, backgroundColour });
			setTimeout(() => {
				if (this._mounted) {
					this.setState({ visible: false });
				}
			}, Toast.durations.LONG);
		}
	}

	render() {
		return (
			<View style={styles.root}>
				<Toast
					visible={this.state.visible}
					position={Toast.positions.BOTTOM}
					containerStyle={{
						...styles.content,
						backgroundColor: this.state.backgroundColour
					}}
					textStyle={{ color: 'white' }}
					duration={Toast.durations.LONG}
					shadow={false}
					animation={false}
					hideOnPress={true}>
					{this.state.message}
				</Toast>
			</View>
		);
	}

	private _mounted = false;
}

interface Props {

}

interface State {
	visible: boolean;
	message: string;
	backgroundColour: string;
}

export function showToast(type: ToastType, message: Error | string | HttpError | any): void {
	if (message instanceof HttpError) {
		try {
			let err;
			if (typeof message.response === 'string') {
				err = JSON.parse(message.response);
			} else {
				err = message.response;
			}
			if (err.modelState && err.modelState[''][0]) {
				message = err.modelState[''][0];
			} else if (err.exceptionMessage) {
				message = err.exceptionMessage;
			} else if (err.message) {
				message = err.message;
			} else {
				message = 'An error has occurred';
			}
		} catch (e) {
			if (message.response) {
				message = message.response;
			}
			if (message.message) {
				message = message.message;
			} else {
				message = 'An error has occurred';
			}
		}
	}
	else if (message instanceof Error) {
		try {
			const err = JSON.parse(message.message);
			if (err.modelState && err.modelState[''][0]) {
				message = err.modelState[''][0];
			} else if (err.exceptionMessage) {
				message = err.exceptionMessage;
			} else if (message.message) {
				message = message.message;
			} else {
				message = 'An error has occurred';
			}
		} catch (e) {
			if (message.message) {
				message = message.message;
			} else {
				message = 'An error has occurred';
			}
		}
	}
	else if (typeof message !== 'string') {
		message = JSON.stringify(message);
	}
	_instance.show(type, message);
}

type ToastType = 'info' | 'success' | 'warning' | 'error';
