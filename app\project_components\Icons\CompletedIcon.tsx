import React from 'react';
import Svg, { G, Circle, Polyline } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class CompletedIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE,
		size: 35
	};

	render() {
		return (
			<Svg width={scaleToDesign(this.props.size) + 'px'} height={scaleToDesign(this.props.size) + 'px'} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Circle stroke={this.props.colour} strokeWidth="2" cx="17.5" cy="17.5" r="16.5" />
					<Polyline stroke={this.props.colour} strokeWidth="2" points="11.7609231 18.1419404 16.1189827 22.5 23.2390769 12.5" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
	size: number;
}
