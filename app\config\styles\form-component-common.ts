import { StyleSheet, ViewStyle, TextStyle, ImageStyle, View } from "react-native";

export interface FormClasses {
    [key: string]: ViewStyle | ImageStyle | TextStyle;
}

const FORM_STYLES = {
    WRAPPER_MARGIN_TOP: 15,
    WRAPPER_MARGIN_BOTTOM: 15,

    BORDER_WIDTH: StyleSheet.hairlineWidth,
    BORDER_COLOR: '#979797',
    BORDER_RADIUS: 10,

    LABEL_FONT_SIZE: 20,
    LABEL_FONT_COLOR: '#323232',
    LABEL_FONT_FAMILY: 'Montserrat-Medium',
    LABEL_LETTER_SPACING: 1,

    PLACEHOLDER_FONT_SIZE: 20,
    PLACEHOLDER_FONT_COLOR: '#777777',
    PLACEHOLDER_FONT_FAMILY: 'Montserrat-Medium',
    PLACEHOLDER_LETTER_SPACING: 1,
    PLACEHOLDER_BACKGROUND_COLOR: 'white',

    INPUT_HEIGHT: 60,
    INPUT_PADDING_LEFT: 15,

    BUTTON_FONT_SIZE: 22,
    BUTTON_FONT_FAMILY: 'Montserrat-SemiBold',
    BUTTON_LETTER_SPACING: 1,
    BUTTON_FONT_COLOR: '#FFFFFF',
    BUTTON_HEIGHT: 60
};

const FORM_CLASSES: FormClasses = {
    WRAPPER: {
        marginTop: FORM_STYLES.WRAPPER_MARGIN_TOP,
        marginBottom: FORM_STYLES.WRAPPER_MARGIN_BOTTOM,
        alignItems: 'center',
    } as ViewStyle,
    LABEL_TEXT: {
        fontSize: FORM_STYLES.LABEL_FONT_SIZE,
        color: FORM_STYLES.LABEL_FONT_COLOR,
        fontFamily: FORM_STYLES.LABEL_FONT_FAMILY,
        letterSpacing: FORM_STYLES.LABEL_LETTER_SPACING,
    } as TextStyle,
    PLACEHOLDER_TEXT: {
        fontSize: FORM_STYLES.PLACEHOLDER_FONT_SIZE,
        color: FORM_STYLES.PLACEHOLDER_FONT_COLOR,
        fontFamily: FORM_STYLES.PLACEHOLDER_FONT_FAMILY,
        letterSpacing: FORM_STYLES.PLACEHOLDER_LETTER_SPACING,
        backgroundColor: FORM_STYLES.PLACEHOLDER_BACKGROUND_COLOR
    } as TextStyle,
    INPUT_CONTAINER: {
        borderWidth: FORM_STYLES.BORDER_WIDTH,
        borderColor: FORM_STYLES.BORDER_COLOR,
        borderRadius: FORM_STYLES.BORDER_RADIUS,
    } as ViewStyle,
    BUTTON: {
        fontSize: FORM_STYLES.BUTTON_FONT_SIZE,
        fontFamily: FORM_STYLES.BUTTON_FONT_FAMILY,
        letterSpacing: FORM_STYLES.BUTTON_LETTER_SPACING,
        color: FORM_STYLES.BUTTON_FONT_COLOR,
        height: FORM_STYLES.BUTTON_HEIGHT,
        alignItems: 'center',
        justifyContent: 'center'
    } as ViewStyle
};

const FORM_ERROR_STYLES = {
    errorWrapper: {
        display: 'flex',
        paddingLeft: 0,
        paddingRight: 10,
        marginBottom: 15,
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: '100%',
    } as ViewStyle,
    errorText: {
        color: 'red'
    } as TextStyle
}

export { FORM_STYLES, FORM_CLASSES, FORM_ERROR_STYLES };
