declare module 'redi-types' {
    export interface CompiledAction {

        type: CompileActionTypes;
        subActions?: CompiledAction[];
        
        arguments?: CompileActionValue[];
        value?: CompileActionValue;

        next?: CompiledAction;
        /** go here instead if value is truthy */
        conditionalNext?: CompiledAction;
        typeOptions?: CompiledTypeOptions;

        functionParams?: string[];
        funcToCall?: CompiledAction;
    }

    export type CompileActionTypes =
        //
        | 'var_assign'
        // any word that start with a $
        | 'var_ref'
        // a dot 'var.member' or index 'var[2]' accessor
        | 'var_accessor'
        // the function to call will be a subaction pointed to by the value object. any arguments to pass in will be in the values array
        | 'function'
        // same as parentheses. get the result of all the executing subactions
        | 'action_group'
        | 'if'
        | 'foreach'
        | 'exit'
        | 'operator'
        //  begin new scope -> '{'
        | 'begin_scope'
        //  end last scope -> '}'
        | 'end_scope';

    export interface CompileActionValue {
        /** if value === 'number' and type === "subaction" then the value should be the result of the subaction at value index number */
        value: any;
        type: 'string' | 'number' | 'bool' | 'other' | 'subaction' | 'accessor_name';
    }

    export interface CompiledTypeOptions {
        operator?: Operator;
    }

    export type Operator = '+' | '-' | '/' | '*' | '&&' | '||' | '==' | '!=' | '!' | '>' | '<' | '>=' | '<=' | '%';

    export interface Token {
        type: TokenType;
        value: string;
        lineNumber: number;
        charNumber: number;
    }

    export type TokenType =
        | 'var_member_func'
        | 'operator'
        // {
        | 'open_brace'
        // }
        | 'close_brace'
        // '.' inbetween 2 var_member_func
        | 'member_access_dot'
        // '[' inbetween 2 var_member_func
        | 'member_access_open_bracket'
        // ']' at end of var_member_func. dot may follow, same as javascript
        | 'member_access_close_bracket'
        // (
        | 'open_parentheses'
        // )
        | 'close_parentheses'
        | 'keyword'
        // =
        | 'assign'
        | 'raw_string'
        | 'raw_number'
        // true, false, null
        | 'raw_other_val'
        // {an id value}
        | 'id_lookup'
        // ,
        | 'comma'
        // ;
        | 'semicolon';
}
