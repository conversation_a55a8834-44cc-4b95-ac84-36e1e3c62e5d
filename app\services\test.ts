class testService {
	test(): void {
		const damage = new StrengthCalcWriter.Damage();
		damage.Depth = 1;
		damage.Orientation = 1;
		damage.Width = 1;

		const pkt = new StrengthCalcWriter.EnclosedPocket();
		pkt.Depth = 10;
		pkt.Orientation = 1;
		pkt.Shell = 10;
		pkt.Width = 10;

		const l1 = new (System.Collections.Generic.List$1<StrengthCalcWriter.Damage>(StrengthCalcWriter.Damage))();
		console.log(l1);
		l1.add(damage);
		const l2 = new (System.Collections.Generic.List$1<StrengthCalcWriter.Damage>(StrengthCalcWriter.Damage))();
		l2.add(damage);

		const l3 = new (System.Collections.Generic.List$1<StrengthCalcWriter.EnclosedPocket>(StrengthCalcWriter.EnclosedPocket))();
		//l3.add(pkt);

		const calc = new StrengthCalcWriter.StrengthCalc.$ctor1(
			100,
			100,
			l1,
			l2,
			l3,
			100
		);
		calc.CalculateStrength();
		console.log(calc);
	}
}
const TestService = new testService();
export default TestService;