import { FormSubmissionDto, FormSubmissionField } from "redi-types";

export interface FormVisibility {
	isHidden?: boolean;
	visibilityConditions?: VisibilityCondition[];
	shouldAllVisibilityConditionsBeMet?: boolean;
}

interface VisibilityCondition {
	textId: string; // field or section id
	operator: string;
	value: any;
	value2: any;
}

export function ShowWhen(textId: string, value: any): VisibilityCondition {
	return {
		textId,
		value,
		operator: 'equal'
	};
}
export function ShowWhenGreaterThan(textId: string, value: any): VisibilityCondition {
	return {
		textId,
		value,
		operator: 'greaterThan'
	};
}

export function setVisibility(form: FormSubmissionDto): void {
	if (form.data?.pages?.length < 1) {
		return;
	}

	for (const page of form.data.pages) {
		for (const section of page.sections) {
			if (section.visibilityConditions) {
				let wasHidden = section.isHidden;
				section.isHidden = section.shouldAllVisibilityConditionsBeMet ?
					!section.visibilityConditions.every(x => shouldShow(form, x)) :
					!section.visibilityConditions.some(x => shouldShow(form, x));
				if (section.isHidden && !wasHidden) {
					section.fields.forEach(field => {
						if (!field.dontClearOnHide) {
							//field.value = undefined;
						}
					});
				}
			}

			if (!section.isHidden) {
				for (const field of section.fields) {
					if (field.visibilityConditions) {
						field.isHidden = field.shouldAllVisibilityConditionsBeMet ?
							!field.visibilityConditions.every(x => shouldShow(form, x)) :
							!field.visibilityConditions.some(x => shouldShow(form, x));

						if (field.isHidden && !field.dontClearOnHide) {
							//field.value = undefined;
						}
					}
				}
			}
		}
	}
}

function shouldShow(form: FormSubmissionDto, condition: VisibilityCondition): boolean {
	for (const page of form.data.pages) {
		for (const section of page.sections) {
			if (section.textId === condition.textId) {
				if (section.isComplete === undefined) {
					return false;
				}

				return isOperationTrue(section.isComplete, condition.operator, condition.value, condition.value2);
			}

			for (const field of section.fields) {
				if (field.textId === condition.textId) {
					if (field.value === undefined) {
						return false;
					}
					return isOperationTrue(field.value, condition.operator, condition.value, condition.value2);
				}
			}
		}
	}
}

function isOperationTrue(value: any, operator: VisibilityCondition['operator'], targetValue: any, targetValue2: any): boolean {
	if (operator === 'equal') {
		return value === targetValue;
	}
	else if (operator === 'greaterThan') {
		return value > targetValue;
	}
	else if (operator === 'greaterThanEqual') {
		return value >= targetValue;
	}
	else if (operator === 'lessThan') {
		return value < targetValue;
	}
	else if (operator === 'lessThanEqual') {
		return value <= targetValue;
	}
	else if (operator === 'between') {
		return value > targetValue && value <= targetValue2;
	}
}