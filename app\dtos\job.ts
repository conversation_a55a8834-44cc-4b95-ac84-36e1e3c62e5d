import { number, string } from "redi-ui-utils";

export interface JobDto {
	// Job Schedule
	teamId: number;
	createdOn: Date;
	deleted: boolean;
	// Job
	workOrder?: string;
	statusCode: JobStatusCode;
	workflowCode: string;
	// Asset
	assetId: number;
	assetName: string;
	maintenanceZone: string;
	suburb: string;
	latitude: number;
	longitude: number;
	esaInfo?: string;
	pipCustomer_Info?: string;
	pipComments?: string;
	fireRiskZoneClass?: string;
	// not yet available
	decInfo?: string;

	modifiedOnUtc?: Date;

	formData?: string
	latestChange?: Date;
	hasUploaded: boolean;
	isUploading: boolean;
	externalRecId: string;
	commentsJson: string;

	isLocked: boolean;
}

export interface JobListDto {
	externalRecId: string;
	createdOn: Date;
	statusCode: JobStatusCode;
	assetName: string;
	suburb: string;
	hasEsa: boolean;
	hasPip: boolean;
	hasDec: boolean;
	fireRiskZoneClass?: string;
	completedDate?: Date;
}

export interface JobSummaryDto {
	networkInspections: NetworkInspDto[];
	other: OtherWorkflowDto[];
}

export interface NetworkInspDto {
	workOrder: string;
	total: number;
	planned: number;
	inProgress: number;
	completed: number;
	maintenanceZone: string;
}

export interface OtherWorkflowDto {
	total: number;
	planned: number;
	inProgress: number;
	completed: number;
	workflowCode: string;
	workOrder: string;
	workflowDescription:string;
}

export enum JobStatusCode {
	JPlanned = 'JPlanned',
	JInProgress = 'JInProgress',
	JComplete = 'JComplete'
}

export interface MapJobDto extends JobListDto {
	latitude: number;
	longitude: number;
	outerColor: string;
	innerColor: string;
}

export interface ActivityCommentDto {
	activityCommentId?: number;
	activityId?: number;
	comment: string;
	createdOn: Date;
	createdByName: string;
	modifiedOn?: Date;
	modifiedByName?: string;
	deleted: boolean;
	mobileAppRefId: string;
}

export type HistoryDto = Pick<JobDto, 'assetName' | 'statusCode' | 'modifiedOnUtc' | 'isUploading' | 'externalRecId'>;