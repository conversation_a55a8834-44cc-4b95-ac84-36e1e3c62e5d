import React from 'react';
import { View, TouchableOpacity, Image, Modal } from 'react-native';
import CloseIcon from 'project_components/Icons/CloseIcon';
import AppButton from 'components/AppButton/AppButton';
import styles from './styles';
import { confirmDialog } from 'components/ConfirmModal/ConfirmModal';
import { scaleToDesign } from 'utils/design-scale';
import { PhotoValueDto } from './Photo';

export default class PhotoModal extends React.PureComponent<Props, never> {
	render() {
		return (
			<Modal
				visible={!!this.props.photo}
				onRequestClose={this.props.onClose}>
				<View style={{ flex: 1, backgroundColor: 'black' }}>
					<Image
						style={{ flex: 1, resizeMode: 'contain' }}
						source={{ uri: this.props.photo?.contentPath }} />
					<View style={styles.modalBar}>
						<AppButton
							theme="red"
							content="Delete"
							width={scaleToDesign(110)}
							onPress={() => this.onDelete()} />
						<TouchableOpacity onPress={this.props.onClose}>
							<CloseIcon />
						</TouchableOpacity>
					</View>
				</View>
			</Modal>
		);
	}
	private onDelete(): void {
		confirmDialog({
			title: 'Confirm Delete Photo',
			bodyText: 'Are you sure you want to delete this photo?',
			confirmText: 'Delete'
		}).then(() => this.props.onDelete()).catch(() => { });
	}
}

interface Props {
	photo: PhotoValueDto;
	onClose(): void;
	onDelete(): void;
}