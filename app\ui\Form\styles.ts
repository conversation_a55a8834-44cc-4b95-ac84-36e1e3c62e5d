import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	header: {
		height: 100,
		backgroundColor: theme.PRIMARY_DARK_BLUE,
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		paddingHorizontal: 60
	},
	page: {
		backgroundColor: theme.WHITE,
		height: 30,
		width: 30,
		borderRadius: 15,
		color: theme.PRIMARY_DARK_BLUE,
		fontSize: 15,
		textAlignVertical: 'center',
		textAlign: 'center'
	},
	section: {
		alignItems: 'flex-end'
	},
	progressSection: {
		flex: 1,
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 40
	},
	title: {
		fontSize: 15,
		color: theme.WHITE,
		textAlign: 'center',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1.5,
		marginBottom: 22
	},
	submitButton:{}
});

export default styles;
