import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import styles from './styles';

export default class SelectItem extends React.PureComponent<SelectItemProps, never> {
	render() {
		return (
			<TouchableOpacity
				style={styles.item}
				onPress={() => this.props.onPress && this.props.onPress()}>
				<Text style={{
					...styles.text,
					opacity: this.props.selected ? 1 : 0.69
				}}>{this.props.display}</Text>
			</TouchableOpacity>
		);
	}
}

export interface SelectItemProps {
	selected?: boolean;
	value?: any;
	onPress?(): void;
	display: string;
}