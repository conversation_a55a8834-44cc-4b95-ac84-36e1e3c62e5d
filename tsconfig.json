{"compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["es2017"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": false, "noImplicitAny": true, "noImplicitThis": true, "alwaysStrict": true, "moduleResolution": "node", "baseUrl": "./", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strictNullChecks": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noErrorTruncation": true, "sourceMap": true, "paths": {"components/*": ["./app/components/*"], "config/*": ["./app/config/*"], "dtos/*": ["./app/dtos/*"], "project_components/*": ["./app/project_components/*"], "services/*": ["./app/services/*"], "stores/*": ["./app/stores/*"], "ui/*": ["./app/ui/*"], "utils/*": ["./app/utils/*"], "app/*": ["./app/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}