import React from 'react';
import { View, FlatList, TouchableOpacity, Text } from 'react-native';
import ProgressBar from 'components/ProgressBar/ProgressBar';
import tableStyles from 'project_components/Table/style';
import theme from 'config/styles/theme';
import CompletedIcon from 'project_components/Icons/CompletedIcon';
import styles from './styles';
import textStyles from 'config/styles/text-styles';
import { SyncingDto, SyncingData } from 'stores/syncing-data';
import SyncIcon from 'project_components/Icons/SyncIcon';
import { StoreComponent } from 'utils/store-component';
import { SyncStore } from 'app/stores/sync-store';
import navigator from 'services/navigator';
import { AppStore } from 'stores/app-store';
import { map } from 'rxjs/operators';
import { ConnectionStatus } from 'services/heartbeat';
import { scaleToDesign } from 'utils/design-scale';

@StoreComponent({
	isSyncing: SyncStore.isSyncing$,
	data: SyncingData.data$,
	disconnected: AppStore.connectionStatus$.pipe(map(x => x === ConnectionStatus.Disconnected))
})
export default class Syncing extends React.PureComponent<Props, never> {
	componentDidMount() {
		SyncingData.load();
	}

	render() {
		return (
			<View style={{ padding: scaleToDesign(40) }}>
				{this.props.disconnected &&
					<View style={styles.syncing}>
						<SyncIcon colour="#E9190F" />
						<Text style={styles.text}>Offline</Text>
					</View>
				}
				{!this.props.disconnected && this.props.isSyncing &&
					<View style={styles.syncing}>
						<SyncIcon />
						<Text style={styles.text}>syncing...</Text>
					</View>
				}
				{this.props.data.length === 0 &&
					<View style={styles.syncing}>
						<Text style={styles.text}>sync completed</Text>
					</View>
				}
				<FlatList
					data={this.props.data}
					keyExtractor={(item, index) => index.toString()}
					renderItem={({ item }) =>
						<TouchableOpacity
							style={{ ...tableStyles.row, height: scaleToDesign(90) }}
							onPress={() => navigator.go(`/jobDetail/${item.externalRecId}`)}>
							<View style={{ ...styles.asset, flex: 3 }}>
								<Text style={textStyles.large_2}>{item.assetName}</Text>
								{/* <Text style={textStyles.mediumGray}>{DateTime.format(item.completedOn, 'H:mm a')}</Text> */}
							</View>
							<View style={{
								...styles.progress,
								marginRight: scaleToDesign(10),
								flex: 1
							}}>
								<ProgressBar
									color="#3BB273"
									backgroundColour="#E6E6E6"
									percent={item.dataUploaded ? 100 : 0} />
								<View style={styles.progressText}>
									{item.dataUploaded && <CompletedIcon colour={theme.PRIMARY_GREEN} size={18} />}
									<Text style={textStyles.mediumGray}>Data</Text>
								</View>
							</View>
							<View style={{ ...styles.progress, flex: 5 }}>
								<ProgressBar
									color={theme.PRIMARY_LIGHT_BLUE}
									backgroundColour="#E6E6E6"
									percent={item.uploaded / item.total * 100} />
								<View style={styles.progressText}>
									<Text style={textStyles.mediumGray}>Files</Text>
									<Text style={textStyles.mediumGray}>
										{item.uploaded}/{item.total} items complete
									</Text>
								</View>
							</View>
						</TouchableOpacity>
					} />
			</View >
		);
	}
}

interface Props {
	data: SyncingDto[];
	isSyncing: boolean;
	disconnected: boolean;
}


