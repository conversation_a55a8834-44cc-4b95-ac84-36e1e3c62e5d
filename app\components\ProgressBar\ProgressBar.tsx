import React from 'react';
import { View } from 'react-native';
import styles from './styles';
import theme from 'config/styles/theme';

export default class ProgressBar extends React.PureComponent<Props, never> {
	static defaultProps = {
		backgroundColour: theme.PRIMARY_LIGHT_BLUE,
		color: theme.WHITE
	};

	render() {
		return (
			<View style={{ ...styles.root, backgroundColor: this.props.backgroundColour }}>
				<View style={{
					...styles.inner,
					width: this.props.percent + '%',
					backgroundColor: this.props.color
				}} />
			</View>
		);
	}
}

interface Props {
	percent: number;
	backgroundColour: string;
	color: string;
}
