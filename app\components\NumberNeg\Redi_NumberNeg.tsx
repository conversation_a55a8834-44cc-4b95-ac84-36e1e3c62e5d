import React from 'react';
import { View, Text, TextInput, ViewStyle } from 'react-native';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import styles from 'components/Text/styles';
import { string } from 'redi-ui-utils';

export default class Redi_NumberNeg extends React.Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			focused: false
		};

		this.onFocus = this.onFocus.bind(this);
		this.onBlur = this.onBlur.bind(this);
		this.onChange = this.onChange.bind(this);
	}

	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content, this.props.overrideStyles?.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					<View style={[styles.textInputContainer]}>
						{this.props.placeholder && !this.props.disableFloatingPlaceholder &&
							<Text style={[styles.placeholder, this.props.overrideStyles?.placeholder, (this.state.focused || this.props.value) ? styles.focusedLabel : {}]}>
								{this.props.placeholder}
							</Text>
						}
						{this.props.placeholder && this.props.disableFloatingPlaceholder && (!this.props.value || this.props.value === '') &&
							<Text style={[styles.placeholder, this.props.overrideStyles?.placeholder]}>{this.props.placeholder}</Text>
						}
						<TextInput
							style={[styles.input, this.props.overrideStyles?.input]}
							autoCompleteType="off"
							value={this.props.value}
							autoCorrect={false}
							keyboardType="numeric"
							secureTextEntry={this.props.secureTextEntry}
							onChangeText={this.onChange}
							onBlur={this.onBlur}
							onFocus={this.onFocus}
						/>
					</View>
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private onFocus() {
		this.setState({ focused: true, oldValue: this.props.value });
		this.props.onFocus?.(null);
	}
	private onBlur() {
		this.setState({ focused: false });
		if (this.props.value != this.state.oldValue)
			this.props.onBlur?.(null);
	}
	private onChange(value: string) {
		if (this.props.integerOnly) {
			let newVal: string = value.replace(/[^0-9]/g, '');
			this.props.onChange(newVal);
		} else {
			let decimalFound: boolean = false;
			let newVal: string = "";
			let numbers = '0123456789';
			for (var ii = 0; ii < value.length; ii++) {
				let wkChar: string = value[ii];
				if (wkChar === "." && !decimalFound) {
					decimalFound = true;
					newVal += wkChar;
				// Allow negatives here
				} else if (numbers.includes(wkChar) || wkChar == '-') {
					newVal += wkChar;
				}
			}
			this.props.onChange(newVal);
		}
	}
}

interface Props extends CommonFormComponentProps<string> {
	secureTextEntry?: boolean;
	/**Optional, require the number be an integer */
	integerOnly?: boolean;
    /** Optional. If true, this component will always keep the space for the label, even when the label is a string or null/undefined.
     * Useful to stop components moving around if you are changing/removing labels dynamically. */
	overrideStyles?: {
		//Styles that go on the very outer View
		wrapper?: ViewStyle;
		//styles that go on the View that wraps the label
		labelWrapper?: ViewStyle;
		//styles tyhat go on the label Text element
		labelText?: ViewStyle;
		//styles that go on the view that wraps the placeholder Text element and the TextInput element
		content?: ViewStyle;
		//styles that go on the placeholder Text element
		placeholder?: ViewStyle;
		//styles that go on the TextInput element
		input?: ViewStyle;
	}
	disableFloatingPlaceholder?: boolean;
}

interface State {
	focused: boolean;
	oldValue: string;
}
