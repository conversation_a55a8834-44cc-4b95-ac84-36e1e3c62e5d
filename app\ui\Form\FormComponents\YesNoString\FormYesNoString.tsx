import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import YesNo, { YesNoProps } from 'components/YesNo/YesNo';

export default class FormYesNoString extends React.Component<WrappedFieldInterface<string, YesNoProps>> {
	render() {
		const props = this.props.componentProps();
		return (
			<YesNo
				{...props}
				onChange={(value) => {
					if (value) {
						this.props.fieldProps.onChange("Yes");
					} else {
						this.props.fieldProps.onChange("No");
					}
				}}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value === 'Yes'}
				error={this.props.form.field.error} />
		);
	}
}
