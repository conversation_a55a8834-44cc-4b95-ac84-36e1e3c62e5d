import config from 'config/config';
import { showToast } from 'components/Toast/ToastProvider';
import { AppStore } from 'app/stores/app-store';
import { sha256 } from 'js-sha256';
import { decode } from 'base-64';
import { TokenDto, ResetPasswordDto } from 'dtos/security';
import Database from './database';
import { DbUserDto } from 'dtos/dbUser';
import { http, HttpError, HttpResult } from 'redi-http';
import { ConnectionStatus } from './heartbeat';
import ClientService from './client';
import TeamService from './team';

const route = `${config.apiUrl}Security`;

class securityService {
	login(username: string, password: string): Promise<boolean> {
		username = username.toLowerCase();
		return AppStore.getValue().connectionStatus === ConnectionStatus.Connected ?
			this.handleOnlineLogin(username, password) :
			this.handleOfflineLogin(username, password);
	}
	async logout(): Promise<void> {
		await Database.execute('UPDATE user SET isActive = 0');
		AppStore.reset();
	}
	async loginActiveUser(): Promise<void> {
		const rows = await Database.select<DbUserDto>('SELECT * FROM user WHERE isActive = 1 LIMIT 1');
		if (rows[0]) {
			const time = Math.floor((new Date().getTime() / 1000));
			const user = this.parseJwt(rows[0].jwt);
			if(AppStore.getValue().connectionStatus === ConnectionStatus.Connected && time > user.exp) {
				this.logout();
			} else {
				AppStore.update({
					isLoggedIn: true,
					user,
					jwt: rows[0].jwt,
					lastModified: rows[0].lastModified ? new Date(rows[0].lastModified) : null,
					teamId: rows[0].teamId,
					team: rows[0].teamName
				});
			}
		}
	}
	changePassword(data: ResetPasswordDto): Promise<HttpResult> {
		const url = `${route}/ChangePassword`;
		return http({ url, method: "POST", data })
			.then(x => {
				this.updatePasswordHash(data.newPassword);
				showToast('success', 'Password change success');
				return x;
			})
			.catch(error => {
				showToast("error", error.error);
				return error;
			});
	}

	private async updatePasswordHash(newPassword: string): Promise<void> {
		const { UserId, Username } = AppStore.getValue().user;
		const hash = sha256(Username.toLowerCase() + newPassword);
		await Database.execute(`
			UPDATE user
			SET hash = ?
			WHERE userId = ?
		`, [hash, UserId]);
	}
	private async handleOnlineLogin(username: string, password: string): Promise<boolean> {
		const url = `${route}/Login`;
		try {
			const response = await http<TokenDto>({
				method: 'GET',
				url,
				username,
				password
			});
			if (response.error) {
				showToast('error', response.error);
				console.warn(response.error);
			}
			else {
				const token = response.data;
				const user = this.parseJwt(token.access_token);
				const hash = sha256(username.toLowerCase() + password);

				await Database.transaction(tx => {
					tx.executeSql('UPDATE user SET isActive = 0');
					tx.executeSql(`
						INSERT OR REPLACE INTO user
						(username, hash, jwt, isActive, userId)
						VALUES(?,?,?,?,?);
					`, [username, hash, token.access_token, true, user.UserId]);
				});
				AppStore.update({
					user,
					jwt: token.access_token
				});
				await ClientService.saveClientsFromServer();
				await TeamService.saveTeamsFromServer();
				return true;
			}
		}
		catch (e) {
			if(!(e.error instanceof HttpError)) {
				showToast('error', e);
			} else {
				if(e.response && e.response.message) {
					showToast('error', e.response.message);
				} else {
					showToast('error', 'Login failed');
				}
			}
			console.warn(e);
			return e;
		}
	}
	private async handleOfflineLogin(username: string, password: string): Promise<boolean> {
		const rows = await Database.select<DbUserDto>('SELECT * FROM user WHERE username = ? LIMIT 1', [username]);
		if (rows.length === 1) {
			const hash = sha256(username + password);
			const matches = hash === rows[0].hash;
			if (matches) {
				const user = this.parseJwt(rows[0].jwt);
				AppStore.update({
					isLoggedIn: true,
					user,
					jwt: rows[0].jwt,
					teamId: rows[0].teamId,
					team: rows[0].teamName
				});
				await Database.execute(`
					UPDATE user SET isActive = 1
					WHERE username = ?
				`, [username]);
			}
			else {
				showToast('error', 'Login Failed');
			}
		}
		else {
			showToast('error', 'Login Failed');
		}
		return false;
	}
	private parseJwt(token: string) {
		var base64Url = token.split('.')[1];
		var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		return JSON.parse(decode(base64));
	}
}
const SecurityService = new securityService();
export default SecurityService;

