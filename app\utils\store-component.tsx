import React from 'react';
import { PartialObserver, Observable, Subscription } from "rxjs";

export type Prop<PERSON>ey<T> = {
	[key in keyof T]: Observable<T[key]>
};

export function StoreComponent<Props>(mapToProps?: Partial<PropKey<Props>>) {
	return function <T extends React.ComponentType<Props>>(comp: T): T {
		return StoreComponentImpl(comp, mapToProps);
	};
}

function StoreComponentImpl<Props, T extends React.ComponentType<Props>>(Comp: T, mapToProps?: Partial<PropKey<Props>>): T {
	class StoreComponent extends React.PureComponent<Props, any> {
		protected readonly _subs = new Array<Subscription>();

		constructor(props: Props) {
			super(props);
			this.state = {};

			for (const key in mapToProps) {
				if (mapToProps.hasOwnProperty(key)) {
					const map = mapToProps[key];
					(this.state as any)[key] = undefined;
					this.subscribe(map, val => {
						if (this._isMounted) {
							this.setState({ [key]: val });
						}
						else {
							(this.state as any)[key] = val;
						}
					});
				}
			}
		}

		componentDidMount() {
			this._isMounted = true;
		}

		render() {
			const El = Comp as any;
			return <El {...this.props} {...this.state} subscribe={(a: any, b: any) => this.subscribe(a, b)} />;
		}

		componentWillUnmount() {
			this._subs.forEach(x => x.unsubscribe());
		}

		private _isMounted = false;

		private subscribe<T>(observable: Observable<T>, observer: PartialObserver<T>): void
		private subscribe<T>(observable: Observable<T>, observer: (arg0: T) => void): void
		private subscribe<T>(observable: Observable<T>, observer: any): void {
			this._subs.push(observable.subscribe(observer));
		}
	}

	return StoreComponent as any;
}

export interface StoreProps extends Partial<{
	subscribe<T>(observable: Observable<T>, observer: PartialObserver<T>): void
	subscribe<T>(observable: Observable<T>, observer: (arg0: T) => void): void
	subscribe<T>(observable: Observable<T>, observer: any): void
}> { }