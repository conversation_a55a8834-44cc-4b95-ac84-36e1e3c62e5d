import { DataServiceBase } from 'utils/data-service-base';
import Database from 'services/database';
import { AppStore } from './app-store';

class syncingData extends DataServiceBase<SyncingDto[], {}> {
	constructor() {
		super('syncing', { isVisible: true });
	}

	protected async _load(): Promise<void> {
		const currentCall = ++this._callCount;

		const sql = `
			SELECT
				j.assetName
				,j.hasUploaded AS dataUploaded
				,SUM(CASE WHEN f.hasUploaded = 1 THEN 1 ELSE 0 END) AS uploaded
				,COUNT(f.externalRecId) AS total
				,j.externalRecId
			FROM jobs j
			LEFT JOIN files f ON j.externalRecId = f.externalRecId
			WHERE j.teamId = ${AppStore.getValue().teamId}
			GROUP BY j.externalRecId
			HAVING uploaded != total
			OR (uploaded = total AND dataUploaded = 0 AND formData IS NOT NULL)
		`;
		const data = await Database.select<SyncingDto>(sql);

		for (const i of data) {
			i.dataUploaded = !!i.dataUploaded;
		}

		if (currentCall > this._latestCall) {
			this.store.update({ data });
		}
	}

	private _callCount = 0;
	private _latestCall = 0;
}
export const SyncingData = new syncingData();

export interface SyncingDto {
	assetName: string;
	dataUploaded: boolean;
	uploaded: number;
	total: number;
	externalRecId: string;
	isUploading: boolean;
}