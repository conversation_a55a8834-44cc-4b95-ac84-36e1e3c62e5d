import { StyleSheet } from 'react-native';
import { FORM_STYLES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        flexDirection: 'column',
        alignItems: 'center',
        marginTop: FORM_STYLES.WRAPPER_MARGIN_TOP,
        marginBottom: FORM_STYLES.WRAPPER_MARGIN_BOTTOM
    },
    content: {
		width: '80%',
		flexDirection: 'column'
    },
    label: {
        fontSize: 22
    },
    buttonWrapper:{
        flexDirection: 'row'
    },
    baseButton: {
        width: 130,
        height: 60,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 20
    },
    baseButtonText: {
        color: 'white',
        fontSize: 22,
    },
    yesButton:{
        backgroundColor: 'rgba(59, 178, 115, 0.5)'
    },
    yesButtonText:{
    },
    yesButtonSelected:{
        backgroundColor: 'rgb(59,178,115)'
    },
    noButton:{
        backgroundColor: 'rgba(0, 44, 85, 0.5)'
    },
    noButtonText:{

    },
    noButtonSelected:{
        backgroundColor: 'rgb(0,44,85)'
    },
    naButton:{
        backgroundColor: 'rgba(27,152,224, 0.5)'
    },
    naButtonText:{

    },
    naButtonSelected:{
        backgroundColor: 'rgb(27,152,224)'
    }
});

export default styles;
