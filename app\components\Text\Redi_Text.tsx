import React from 'react';
import { View, Text, TextInput, ViewStyle } from 'react-native';
import styles from './styles';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';
import FormComponentLabel from 'components/Form/FormComponentLabel';

export default class Redi_Text extends React.Component<RediTextProps, State> {
	constructor(props: RediTextProps) {
		super(props);
		this.state = {
			focused: false
		};

		this.onFocus = this.onFocus.bind(this);
		this.onBlur = this.onBlur.bind(this);
	}

	render() {
		const required = this.props.required ? ' *' : '';
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content, this.props.overrideStyles?.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					<View style={[styles.textInputContainer]}>
						{this.props.placeholder && !this.props.disableFloatingPlaceholder &&
							<Text style={[
								styles.placeholder,
								this.props.overrideStyles?.placeholder,
								(this.state.focused || this.props.value) ? styles.focusedLabel : {}
							]}>
								{this.props.placeholder + required}
							</Text>
						}
						{this.props.placeholder && this.props.disableFloatingPlaceholder && (!this.props.value || this.props.value === '') &&
							<Text style={[styles.placeholder, this.props?.overrideStyles?.placeholder]}>{this.props.placeholder + required}</Text>
						}
						<TextInput
							style={[styles.input, this.props?.overrideStyles?.input]}
							autoCompleteType="off"
							autoCorrect={false}
							value={this.props.value}
							secureTextEntry={this.props.secureTextEntry}
							onChangeText={value => this.props.onChange(value)}
							onBlur={(e) => this.onBlur(e)}
							onFocus={(e) => this.onFocus(e)} />
					</View>
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private onFocus(e: Event) {
		this.setState({ focused: true, oldValue: this.props.value });
		this.props.onFocus?.(e);
	}
	private onBlur(e: Event) {
		this.setState({ focused: false });
		if (this.props.value != this.state.oldValue)
			this.props.onBlur?.(e);
	}
}

export interface RediTextProps extends CommonFormComponentProps<string> {
    /** Optional. If true, the text input obscures the text entered so that sensitive text like passwords stay secure.
     * The default value is false. */
	secureTextEntry?: boolean;
	overrideStyles?: {
		//Styles that go on the very outer View
		wrapper?: ViewStyle;
		//styles that go on the View that wraps the label
		labelWrapper?: ViewStyle;
		//styles tyhat go on the label Text element
		labelText?: ViewStyle;
		//styles that go on the view that wraps the placeholder Text element and the TextInput element
		content?: ViewStyle;
		//styles that go on the placeholder Text element
		placeholder?: ViewStyle;
		//styles that go on the TextInput element
		input?: ViewStyle;
	}
	disableFloatingPlaceholder?: boolean;
}

interface State {
	focused: boolean;
	oldValue: string;
}
