import { MapJobDto, JobStatusCode } from 'dtos/job';
import { DataServiceBase } from 'utils/data-service-base';
import Database from 'services/database';
import { teamIdFilter } from 'utils/filter';
import theme from 'config/styles/theme';

class mapData extends DataServiceBase<MapJobDto[], {}> {
	constructor() {
		super('map');
	}

	protected async _load(): Promise<void> {
		const currentCall = ++this._callCount;

		const sql = `
			SELECT
				externalRecId
				,assetName
				,statusCode
				,latitude
				,longitude
				,CASE WHEN esaInfo IS NULL THEN 0 ELSE 1 END as hasEsa
				,CASE WHEN (pipCustomer_Info IS NULL AND pipComments IS NULL) THEN 0 ELSE 1 END as hasPip
				,CASE WHEN decInfo IS NULL THEN 0 ELSE 1 END as hasDec
			FROM jobs
			${teamIdFilter()}
		`;

		const data = await Database.select<MapJobDto>(sql);
		for (const item of data) {
			if (item.statusCode === JobStatusCode.JComplete) {
				item.outerColor = 'rgba(59, 178, 115, 0.30000001192092896)';
				item.innerColor = theme.PRIMARY_GREEN;
			}
			else {
				item.outerColor = 'rgba(254, 127, 45, 0.30000001192092896)';
				item.innerColor = '#FE7F2D';
			}

			let search: MapJobDto;
			do {
				search = data
					.filter(x => x !== item)
					.find(x => x.latitude === item.latitude && x.longitude === item.longitude);
				if (search) {
					// if both lng and lat are the same mapview won't render the
					// custom markers (will render the default) so adjust here slightly
					item.latitude += 0.00004;
				}
			} while (search);
		}

		if (currentCall > this._latestCall) {
			this.store.update({ data });
		}
	}

	private _callCount = 0;
	private _latestCall = 0;
}
export const MapData = new mapData();