import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Modal } from 'react-native';
import { RouteComponentProps } from 'react-router-native';
import HeaderBar from 'components/HeaderBar/HeaderBar';
import styles from './styles';
import navigator from 'services/navigator';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import { JobDto, JobStatusCode, ActivityCommentDto } from 'dtos/job';
import PIPIcon from '../JobIcons/PIPIcon';
import ESAIcon from '../JobIcons/ESAIcon';
import BFMIcon from '../JobIcons/BFMIcon';
import DECIcon from '../JobIcons/DECIcon';
import { getStatusIcon } from '../JobIcons/status-icon';
import JobDetailSection from './JobDetailSection';
import AddIcon from 'project_components/Icons/AddIcon';
import JobService from 'services/job';
import AppButton from 'components/AppButton/AppButton';
import BackButton from 'project_components/BackButton/BackButton';
import DBYD from './DBYD';
import { scaleToDesign } from 'utils/design-scale';
import modalStyles from 'config/styles/modal-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import Redi_Text from 'components/Text/Redi_Text';
import { AppStore } from 'stores/app-store';
import uuid4 from 'uuid/v4';

export default class JobDetail extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			data: null,
			hasDBYD: true,
			commentModalIsOpen: false,
			inProgressComment: ""
		};
	}
	componentDidMount() {
		JobService
			.getJob(this.props.match.params.id)
			.then(data => {
				this.setState({ data });
			});
	}

	convertComments() {

	}

	render() {
		if (this.state.data === null) {
			return null;
		}
		const { assetName, maintenanceZone, statusCode, suburb, pipComments, commentsJson } = this.state.data;
		const { pipCustomer_Info, decInfo, esaInfo, fireRiskZoneClass } = this.state.data;

		let comments:ActivityCommentDto[] = [];
		if(commentsJson) {
			comments = JSON.parse(commentsJson) as ActivityCommentDto[];
			if (!Array.isArray(comments)) { comments = []; }
		}

		return (
			<View>
				<HeaderBar>
					<View style={styles.root}>
						<BackButton />
						<View style={styles.title}>
							<ScheduledFormIcon colour={theme.WHITE} />
							<Text style={{ ...textStyles.large, marginLeft: 16 }}>
								{maintenanceZone} - {assetName}
							</Text>
						</View>
						<View />
					</View>
				</HeaderBar>
				<JobDetailSection style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
					<View style={{ flexDirection: 'row' }}>
						<View style={{ marginRight: 15, marginTop: 7 }}>
							{getStatusIcon(statusCode)}
						</View>
						<View>
							<Text style={textStyles.large_2}>{assetName}</Text>
							<Text style={textStyles.mediumGray}>{suburb}</Text>
						</View>
					</View>
					<View style={{ flexDirection: 'row' }}>
						{decInfo && <DECIcon style={{ marginRight: 10 }} />}
						{(pipComments || pipCustomer_Info) &&
							<PIPIcon style={{ marginRight: 10 }} />
						}
						{esaInfo && <ESAIcon style={{ marginRight: 10 }} />}
						<BFMIcon rating={fireRiskZoneClass} />
					</View>
				</JobDetailSection>
				{decInfo &&
					<JobDetailSection>
						<DECIcon style={{ marginBottom: 15 }} />
						<Text style={styles.textContent}>{decInfo}</Text>
					</JobDetailSection>
				}
				{(pipComments || pipCustomer_Info) &&
					<JobDetailSection>
						<PIPIcon style={{ marginBottom: 15 }} />
						<Text style={styles.textContent}>{pipCustomer_Info}</Text>
						<Text style={styles.textContent}>{pipComments}</Text>
					</JobDetailSection>
				}
				{esaInfo &&
					<JobDetailSection>
						<ESAIcon style={{ marginBottom: 15 }} />
						<Text style={textStyles.mediumGray}>REQUIREMENTS</Text>
						<Text style={styles.textContent}>{esaInfo}</Text>
					</JobDetailSection>
				}

				<JobDetailSection>
					<AppButton
						theme="green"
						disabled={this.state.data.isLocked}
						height={scaleToDesign(57)}
						width={scaleToDesign(305)}
						onPress={() => this.enterJob()}>
						<Text style={textStyles.large}>
							{statusCode === JobStatusCode.JPlanned && 'Start '}
							{statusCode === JobStatusCode.JInProgress && 'Continue '}
							{statusCode === JobStatusCode.JComplete && 'Edit '}
							Job
						</Text>
					</AppButton>
				</JobDetailSection>

				{this.state.hasDBYD &&
					<DBYD />
				}
				<JobDetailSection hideLine={true}>
					<View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
						<Text style={textStyles.mediumGray}>COMMENTS</Text>
						<TouchableOpacity
							onPress={() => this.setState({commentModalIsOpen: true})}
							style={{ flexDirection: 'row', alignItems: 'center' }}>
							<AddIcon />
							<Text style={styles.addComment}>Add Comment</Text>
						</TouchableOpacity>
					</View>
					{
						comments && comments.length > 0 &&
						<FlatList 
							keyExtractor={(item, index) => item.mobileAppRefId + index.toString()}
							data={comments}
							renderItem={({item}) =>
								<View style={{flexDirection:'row'}}>
									<Text style={{flex:1}}>{item.comment}</Text>
									<TouchableOpacity onPress={() => this.removeComment(item.mobileAppRefId)}>
										<CloseIcon />
									</TouchableOpacity>
								</View>
							} />
					}
					<Modal
						transparent
						visible={this.state.commentModalIsOpen}
						onRequestClose={() => this.setState({commentModalIsOpen: false})}>
						<View style={{ alignItems: 'center' }}>
							<View style={modalStyles.content}>
								<View style={modalStyles.header}>
									<Text style={textStyles.large_2}>New Comment</Text>
									<TouchableOpacity onPress={() => this.setState({commentModalIsOpen: false})}>
										<CloseIcon />
									</TouchableOpacity>
								</View>
								<Redi_Text
									placeholder="Comment Text"
									onChange={e => this.setState({inProgressComment: e})}
									value={this.state.inProgressComment} />
								<View style={{ alignItems: 'flex-end' }} >
									<AppButton
										theme="green"
										marginVertical={scaleToDesign(10)}
										width={scaleToDesign(130)}
										onPress={() => this.saveCurrentComment()}
										content="Save" />
								</View>
							</View>
						</View>
					</Modal>
				</JobDetailSection>
			</View >
		);
	}
	private removeComment(mobileAppRefId:string): void {
		const { commentsJson } = this.state.data;

		let comments:ActivityCommentDto[] = [];
		if(commentsJson) {
			comments = JSON.parse(commentsJson) as ActivityCommentDto[];
			if (!Array.isArray(comments)) { comments = []; }
		}

		for(var ii = 0; ii < comments.length; ii++) {
			if(comments[ii].mobileAppRefId == mobileAppRefId) {
				comments[ii].deleted = true;
				break;
			}
		}

		const newCommentsJson = JSON.stringify(comments);

		this.setState({data: {...this.state.data, commentsJson: newCommentsJson}, inProgressComment: "", commentModalIsOpen: false});

		JobService.updateComments(this.state.data.externalRecId, newCommentsJson);
	}
	private saveCurrentComment(): void {
		const { commentsJson } = this.state.data;

		let comments:ActivityCommentDto[] = [];
		if(commentsJson) {
			comments = JSON.parse(commentsJson) as ActivityCommentDto[];
			if (!Array.isArray(comments)) { comments = []; }
		}
		comments.push({
			mobileAppRefId: uuid4(),
			comment: this.state.inProgressComment,
			createdOn: new Date(),
			createdByName: AppStore.getValue().user?.Username,
			deleted: false
		});

		const newCommentsJson = JSON.stringify(comments);

		this.setState({data: {...this.state.data, commentsJson: newCommentsJson}, inProgressComment: "", commentModalIsOpen: false});

		JobService.updateComments(this.state.data.externalRecId, newCommentsJson);
	}
	private enterJob(): void {
		if (this.state.data.statusCode === JobStatusCode.JPlanned) {
			JobService.updateJobStatus(this.state.data.externalRecId, JobStatusCode.JInProgress);
		}
		if (this.state.data.statusCode === JobStatusCode.JComplete) {
			JobService.updateJobStatus(this.state.data.externalRecId, JobStatusCode.JInProgress);
		}
		navigator.go(`/form/${this.props.match.params.id}`);
	}
}

interface Props extends RouteComponentProps<{ id: string }> {

}

interface State {
	data: JobDto;
	hasDBYD: boolean;
	inProgressComment: string;
	commentModalIsOpen: boolean;
}