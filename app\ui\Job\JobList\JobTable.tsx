import React from 'react';
import { View, Text } from 'react-native';
import styles from './styles';
import navigator from 'services/navigator';
import Table, { TableColumn } from 'project_components/Table/Table';
import { Sort } from 'project_components/Table/HeaderCell/HeaderCell';
import { JobListDto, JobStatusCode } from 'dtos/job';
import { JobListData } from 'app/stores/job-list-data';
import DECIcon from '../JobIcons/DECIcon';
import PIPIcon from '../JobIcons/PIPIcon';
import ESAIcon from '../JobIcons/ESAIcon';
import BFMIcon from '../JobIcons/BFMIcon';
import { getStatusIcon } from '../JobIcons/status-icon';
import { StoreComponent } from 'utils/store-component';

@StoreComponent({
	data: JobListData.data$
})
class JobTable extends React.PureComponent<Props, never> {
	componentDidMount() {
		JobListData.setVisible();
		const { workflowCode, workOrder, maintenanceZone } = this.props;
		const __maintenanceZone = decodeURIComponent(maintenanceZone);
		JobListData.setArgs({
			workflowCode,
			workOrder,
			maintenanceZone: maintenanceZone ? __maintenanceZone : null,
			status: 'AllJobs'
		});
	}
	componentWillUnmount() {
		JobListData.reset();
	}

	render() {
		return (
			<Table
				onSort={(e, s) => this.onSort(e, s)}
				renderHeaderCell={x => <Text>{x.display}</Text>}
				renderRow={x => [
					<View key="" style={styles.assetCell}>
						{getStatusIcon(x.statusCode)}
						<Text style={{
							...styles.assetName,
							color: x.statusCode === JobStatusCode.JComplete ?
								'#8B8B8B' : styles.assetName.color
						}}>{x.assetName}</Text>
					</View>,
					<View key="">
						{x.hasDec && <DECIcon />}
					</View>,
					<View key="">
						{x.hasPip && <PIPIcon />}
					</View>,
					<View key="">
						{x.hasEsa && <ESAIcon />}
					</View>,
					<View key="">
						<BFMIcon rating={x.fireRiskZoneClass} />
					</View>,
					<Text key="" style={styles.suburb}>{x.suburb}</Text>
				]}
				columns={tableColumns}
				data={this.props.data}
				onRowPress={e => navigator.go(`/jobDetail/${e.externalRecId}`)} />
		);
	}

	private onSort(column: keyof JobListDto, sort: Sort): void {
		JobListData.setPartialArgs({ column, sort });
	}
}
export default JobTable as React.ComponentType<PublicProps>;

interface Props extends PublicProps {
	data: JobListDto[];
}

interface PublicProps {
	workflowCode: string;
	workOrder: string;
	maintenanceZone?: string;
}

const tableColumns: TableColumn<JobListDto>[] = [
	{ columnName: 'assetName', display: 'Pick ID', flex: 2, isSortable: true },
	{ columnName: 'hasDec', display: 'DEC', flex: 1 },
	{ columnName: 'hasPip', display: 'PIP', flex: 1 },
	{ columnName: 'hasEsa', display: 'ESA', flex: 1 },
	{ columnName: 'fireRiskZoneClass', display: 'Fire', flex: 1 },
	{ columnName: 'suburb', display: 'Suburb/Town', flex: 3, isSortable: true, headerAlign: 'flex-end' }
];
