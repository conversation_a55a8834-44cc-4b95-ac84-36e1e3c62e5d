import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import YesNo, { YesNoProps } from 'components/YesNo/YesNo';

export default class FormYesNo extends React.Component<WrappedFieldInterface<boolean, YesNoProps>> {
	render() {
		const props = this.props.componentProps();
		return (
			<YesNo
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error} />
		);
	}
}
