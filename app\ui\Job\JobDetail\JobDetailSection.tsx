import React from 'react';
import { View, ViewStyle } from 'react-native';
import styles from './styles';

export default class JobDetailSection extends React.PureComponent<Props, never> {
	render() {
		return (
			<View style={{
				...styles.sectionRoot,
				...this.props.style,
				borderBottomWidth: this.props.hideLine ? 0 : 1
			}}>
				{this.props.children}
			</View>
		);
	}
}

interface Props {
	hideLine?: boolean;
	style?: ViewStyle;
}