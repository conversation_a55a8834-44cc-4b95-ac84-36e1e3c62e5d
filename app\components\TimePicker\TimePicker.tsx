import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Time } from 'redi-ui-utils';
import DateTimePicker from '@react-native-community/datetimepicker';
import styles from './styles';
import ClockIcon from 'project_components/Icons/ClockIcon';
import { scaleToDesign } from 'utils/design-scale';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import FormComponentError from 'components/Form/FormComponentError';

export default class TimePicker extends React.PureComponent<TimePickerProps, State> {
	constructor(props: TimePickerProps) {
		super(props);

		this.state = {
			showPicker: false
		};
	}

	componentDidMount() {
		if (!this.props.value) {
			this.onChange(new Date());
		}
	}

	render() {
		let value: Time = this.props.value ? this.props.value : new Time(new Date());
		if (typeof value === 'string') {
			value = this.stringToTime(value);
		}
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					<TouchableOpacity
						activeOpacity={0.95}
						style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}
						onPress={() => {
							this.props.onFocus?.(null);
							this.setState({ showPicker: true });
						}}>
						<View style={[styles.actionButton]}>
							<React.Fragment>
								<ClockIcon colour="#FFFFFF" />
								<Text style={[styles.buttonText]}>Select Time</Text>
							</React.Fragment>
						</View>
						<View style={[styles.dateDisplayWrapper, this.props.overrideStyles?.dateDisplayWrapper]}>
							<View style={{ width: scaleToDesign(20) }}></View>
							<Text style={[styles.dateDisplayText, this.props.overrideStyles?.dateDisplayText]}>
								{this.formatTime(value)}
							</Text>
						</View>
					</TouchableOpacity>
					<FormComponentError error={this.props.error} />
				</View>
				{this.state.showPicker &&
					<DateTimePicker
						mode="time"
						display="spinner"
						value={value?.toDate()}
						onChange={(e, date) => this.onChange(date)}
					/>
				}
			</View>
		);
	}

	private onChange(date: Date | undefined): void {
		this.setState({ showPicker: false });
		if (date) {
			this.props.onChange(new Time(date));
		}
		this.props.onBlur?.(null);
	}
	private formatTime(time: Time): string {
		if (time && time.format) {
			return time.format('hh:mm a');
		}
		return null;
	}
	private stringToTime(time: string): Time {
		const __time = time.split(':');
		return new Time(Number(__time[0]), Number(__time[1]), Number(__time[2]), 0);
	}
}

export interface TimePickerProps extends CommonFormComponentProps<Time> {
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper: object,
		//styles that go on the View that wraps the label
		labelWrapper?: object,
		//styles tyhat go on the label Text element
		labelText?: object,
		//Styles to go on the view that wraps the text which displays the fomratted date value
		dateDisplayWrapper?: object,
		//Styles to go on the Text 
		dateDisplayText?: object,
	}
}

interface State {
	showPicker: boolean;
}
