import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Draw from 'components/Draw/Draw';

// eslint-disable-next-line react/require-optimization
export default class FormDraw extends React.Component<WrappedFieldInterface<string, FormDrawProps>> {

    constructor(props: any) {
        super(props);
	}

	onChange(){}

	render() {
		const props = this.props.componentProps();
		return (
			<Draw
                onChange={this.onChange}
				{...props} />
		);
	}
}

export interface FormDrawProps{
}

