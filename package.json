{"name": "LogsysMobileApp", "version": "1.1.4", "private": true, "scripts": {"android": "react-native run-android", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@datorama/akita": "^4.18.1", "@react-native-community/datetimepicker": "^2.2.3", "base-64": "^0.1.0", "date-fns": "^2.9.0", "forms-logic-compiler": "^1.0.2", "history": "^4.10.1", "js-sha256": "^0.9.0", "ngx-take-until-destroy": "^5.4.0", "react": "16.9.0", "react-css-modules": "^4.7.11", "react-native": "0.61.5", "react-native-canvas": "^0.1.37", "react-native-config": "^0.12.0", "react-native-exit-app": "^1.1.0", "react-native-fs": "^2.16.6", "react-native-get-location": "^1.4.2", "react-native-image-crop-picker": "^0.37.3", "react-native-image-picker": "^4.8.4", "react-native-keyboard-aware-scroll-view": "^0.9.3", "react-native-map-clustering": "^3.1.2", "react-native-maps": "0.25", "react-native-root-toast": "^3.2.0", "react-native-sqlite-storage": "^4.1.0", "react-native-svg": "^11.0.1", "react-native-webview": "^8.1.2", "react-router-native": "^5.1.2", "redi-component-utils": "^1.0.19", "redi-form": "^1.6.6", "redi-http": "^2.2.1", "redi-ui-utils": "^2.0.0", "rollbar-react-native": "^0.8.1"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/runtime": "^7.6.2", "@react-native-community/eslint-config": "^0.0.5", "@types/history": "^4.7.4", "@types/jest": "^24.0.24", "@types/react-native": "^0.60.25", "@types/react-native-canvas": "^0.1.2", "@types/react-native-sqlite-storage": "^3.3.2", "@types/react-router-native": "^5.1.0", "@types/react-test-renderer": "16.9.1", "@types/uuid": "^7.0.2", "@typescript-eslint/eslint-plugin": "^2.12.0", "@typescript-eslint/parser": "^4.5.0", "babel-jest": "^24.9.0", "babel-plugin-module-resolver": "^4.0.0", "eslint": "^6.5.1", "jest": "^24.9.0", "metro-react-native-babel-preset": "^0.56.0", "react-test-renderer": "16.9.0", "typescript": "^3.7.3"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}