import { OmitName, ReactComponent } from "redi-types";
import { WrappedForm, FormProps } from "redi-form";

declare module "redi-types" {
	export type ReactComponent<P = any, S = any> = new (props: P, context?: any) => React.Component<P, S>;

	/** A type that defines any combination of react elements, nested any amount of levels deep */
	export type ElementRecursive<TProps> = React.ReactElement<TProps> | Array<React.ReactElement<TProps>> | Array<ElementRecursive<TProps>>;

	export interface CommonComponentProps {
		className?: string;
		styleName?: string;
		classes?: object;
		forwardedRef?: Writeable<React.RefObject<any>> | ((ref: any) => void);
	}
}

declare module "redi-form"{

export function formFactory<TVal extends object>(opts?: OmitName<FormProps<TVal>, "children">): (Comp:any) => any;

}