import React from 'react';
import { View, Text, Dimensions } from 'react-native';
import MapView from 'react-native-map-clustering';
import { Marker, Callout, Region } from 'react-native-maps';
import { MapJobDto } from 'dtos/job';
import { MapData } from 'stores/map-data';
import GetLocation from 'react-native-get-location';
import styles from './styles';
import MarkerBorder from './MarkerBorder';
import { StoreComponent } from 'utils/store-component';
import navigator from 'services/navigator';
import { AppStore } from 'stores/app-store';
import AppButton from 'components/AppButton/AppButton';

@StoreComponent({
	data: MapData.data$,
	savedRegion: AppStore.region$
})
export default class MapPage extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			initial: null
		};
	}

	async componentDidMount() {
		MapData.setVisible();
		MapData.load();
		if (this.props.savedRegion) {
			this.setState({ initial: this.props.savedRegion });
		}
		else {
			const region = await this.getLocation();
			this.setState({ initial: region });
		}
	}
	componentWillUnmount() {
		MapData.reset();
	}

	render() {
		if (this.state.initial === null) {
			return null;
		}
		return (
			<View style={{ flex: 1 }}>
				{
					this.props.data &&
					<MapView
						showsUserLocation={true}
						rotateEnabled={false}
						pitchEnabled={false}
						onRegionChangeComplete={(reg: Region) => AppStore.update({ mapRegion: reg })}
						style={{ flex: 1, width: Dimensions.get('window').width, height: Dimensions.get('window').height }}
						initialRegion={this.state.initial}>
						{this.props.data?.map(x => {
							if(x.latitude && x.longitude) {
								return (
									<Marker
										key={`${x.externalRecId}${x.hasDec}${x.hasEsa}${x.hasPip}`}
										coordinate={{
											latitude: x.latitude,
											longitude: x.longitude
										}}>
										<View style={styles.marker}>
											<View style={{ ...styles.outerCircle, backgroundColor: x.outerColor }}>
												<View style={{ ...styles.centerCircle, backgroundColor: x.innerColor }} />
											</View>
											<MarkerBorder {...x} />
										</View>
										<Callout onPress={() => navigator.go(`/jobDetail/${x.externalRecId}`)}>
											<View style={styles.callout}>
												<Text>{x.assetName}</Text>
												<AppButton
													theme="green"
													content="View" />
											</View>
										</Callout>
									</Marker>
								)
							} else {
								return null;
							}
						})}
					</MapView>
				}
			</View >
		);
	}

	private getLocation(): Promise<Region> {
		return GetLocation.getCurrentPosition({
			enableHighAccuracy: true,
			timeout: 15000,
		}).then((location: any) => ({
			latitude: location.latitude,
			longitude: location.longitude,
			latitudeDelta: 0.0922,
			longitudeDelta: 0.0421
		}));
	}
}

interface Props {
	data: MapJobDto[];
	savedRegion: Region;
}

interface State {
	initial: Region;
}