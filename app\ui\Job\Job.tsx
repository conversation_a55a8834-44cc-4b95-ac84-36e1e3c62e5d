import React from 'react';
import { View } from 'react-native';
import { RouteComponentProps } from 'react-router-native';
import HeaderBar from 'components/HeaderBar/HeaderBar';
import IconTextButton from 'project_components/IconTextButton/IconTextButton';
import navigator from 'services/navigator';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import InProgressFormIcon from 'project_components/Icons/InProgressFormIcon';
import CompletedIcon from 'project_components/Icons/CompletedIcon';
import JobSummary from './JobSummary/JobSummary';
import { StoreComponent, StoreProps } from 'utils/store-component';
import { AppStore } from 'app/stores/app-store';

@StoreComponent()
export default class Job extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			selected: undefined
		};
	}

	componentDidMount() {
		this.props.subscribe(AppStore.currentRoute$, x => {
			let selected: SelectedItem;
			if (x === '/job/scheduled') {
				selected = 'scheduled';
			} else if (x === '/job/inProgress') {
				selected = 'inProgress';
			} else if (x === '/job/completed') {
				selected = 'completed';
			}
			this.setState({ selected });
		});
	}

	render() {
		return (
			<View>
				<HeaderBar>
					<IconTextButton
						display="SCHEDULED"
						selected={this.state.selected === 'scheduled'}
						onClick={() => navigator.go('/job/scheduled')} >
						<ScheduledFormIcon />
					</IconTextButton>
					<IconTextButton
						display="IN PROGRESS"
						selected={this.state.selected === 'inProgress'}
						onClick={() => navigator.go('/job/inProgress')} >
						<InProgressFormIcon />
					</IconTextButton>
					<IconTextButton
						display="COMPLETED"
						selected={this.state.selected === 'completed'}
						onClick={() => navigator.go('/job/completed')}>
						<CompletedIcon />
					</IconTextButton>
				</HeaderBar>
				<JobSummary {...this.props.match.params} />
			</View>
		);
	}
}

interface Props extends StoreProps, RouteComponentProps<{ status: string }> { }

interface State {
	selected: SelectedItem;
}

type SelectedItem = 'scheduled' | 'inProgress' | 'completed' | undefined;