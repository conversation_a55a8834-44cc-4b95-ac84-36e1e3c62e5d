import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Photo, { PhotoProps, PhotoValueDto } from 'components/Photo/Photo';

export default class FormPhoto extends React.Component<WrappedFieldInterface<PhotoValueDto[], PhotoProps>> {
	constructor(props: any) {
		super(props);
		this.onChange = this.onChange.bind(this);
	}

	render() {
		const props = this.props.componentProps();
		const imagePaths = this.props.fieldProps.value || [];
		return (
			<Photo
				{...props}
				overrideStyles={{
					wrapper: {
						marginTop: 30,
					}
				}}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				error={this.props.form.field.error}
				photos={imagePaths} />
		);
	}

	private onChange(values: Array<PhotoValueDto>) {
		this.props.fieldProps.onChange(values);
	}
}
