import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import ChevronIcon from 'project_components/Icons/ChevronIcon';
import { SelectItemProps } from './SelectItem';
import { scaleToDesign } from 'utils/design-scale';

export default class Select extends React.PureComponent<Props, State> {
	state = {
		isOpen: false
	};

	render() {
		return (
			<View>
				<TouchableOpacity
					style={{
						...styles.root,
						borderBottomLeftRadius: this.state.isOpen ? 0 : scaleToDesign(30),
						borderBottomRightRadius: this.state.isOpen ? 0 : scaleToDesign(30)
					}}
					onPress={() => this.setState(prev => ({ isOpen: !prev.isOpen }))}>
					<Text style={styles.text}>
						{this.props.children.find(x => x.props.value === this.props.selected)?.props.display}
					</Text>
					<ChevronIcon position={this.state.isOpen ? 'up' : 'down'} />
				</TouchableOpacity>
				{this.state.isOpen &&
					<View style={styles.items}>
						{this.props.children.map(x =>
							React.cloneElement(x, {
								key: x.props.value,
								selected: this.props.selected === x.props.value,
								onPress: () => this.onClick(x.props.value)
							})
						)}
					</View>
				}
			</View>
		);
	}

	private onClick(value: any): void {
		this.setState({ isOpen: false });
		this.props.onSelected(value);
	}
}

interface Props {
	children: React.ReactElement<SelectItemProps>[];
	selected: any;
	onSelected(value: any): void;
}

interface State {
	isOpen: boolean;
}

