import React from 'react';
import { View, Text } from 'react-native';
import { FORM_STYLES } from 'config/styles/form-component-common';
import AppButton from 'components/AppButton/AppButton';
import JobService from 'services/job';
import { JobStatusCode } from 'dtos/job';
import navigator from 'services/navigator';
import { DateTime, string } from 'redi-ui-utils';
import { AppStore } from 'stores/app-store';
import FileService from 'services/file';
import { FormSubmissionDto } from 'redi-types';

export default class FormSubmit extends React.PureComponent<Props, never> {
	render() {
		return (
			<View style={{ justifyContent: 'center', alignItems: 'center' }}>
				<Text style={{
					fontSize: 24,
					fontFamily: 'Montserrat-SemiBold',
					letterSpacing: 2.3,
					color: FORM_STYLES.LABEL_FONT_COLOR,
					marginVertical: 20
				}}>You've reached the end!</Text>
				<Text style={{
					fontSize: 20,
					fontFamily: 'Montserrat-Medium',
					letterSpacing: 1.5,
					color: FORM_STYLES.LABEL_FONT_COLOR,
					marginBottom: 20,
					width: '80%',
					textAlign: 'center'
				}}>
					Scroll back or use the page index to check your answers before submitting
							</Text>
				<AppButton
					theme="blue"
					content="Submit"
					width={300}
					marginVertical={15}
					onPress={() => this.props.handleSubmit((data) => {
						JobService
							.updateJobStatus(this.props.externalRecId, JobStatusCode.JComplete)
							.then(() => navigator.goBack());
						data.completedDate = DateTime.toUTC(new Date());
						data.completedByName = AppStore.getValue().user.Username;
						FileService.saveFormData(this.props.externalRecId, data);
					}, err => console.warn(err))} />
			</View>
		);
	}
}

interface Props {
	handleSubmit<R = void>(
		onSuccess: (data: FormSubmissionDto) => R,
		onFail?: (errors: { [path: string]: string; }) => R,
		force?: boolean
	): R;
	externalRecId: string;
}