import React from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import styles from './style';
import HeaderCell, { Sort } from './HeaderCell/HeaderCell';

export default class Table<TData> extends React.PureComponent<Props<TData>, State<TData>> {
	constructor(props: Props<TData>) {
		super(props);
		this.state = {
			sortedColumn: null,
			sort: Sort.None
		};
	}
	render() {
		return (
			<View style={styles.root}>
				{this.props.renderHeaderCell &&
					<View style={styles.header}>
						{this.props.columns.map(x =>
							<HeaderCell
								key={x.columnName.toString()}
								sortable={x.isSortable}
								align={x.headerAlign}
								sort={this.state.sortedColumn === x.columnName ? this.state.sort : Sort.None}
								onSortChange={e => this.onSort(x.columnName, e)}
								style={{ ...styles.headerCell, flex: x.flex }}>
								{this.props.renderHeaderCell(x)}
							</HeaderCell>
						)}
					</View>
				}
				<FlatList
					data={this.props.data}
					keyExtractor={(item, index) => index.toString()}
					renderItem={({ item }) =>
						<TouchableOpacity
							style={styles.row}
							onPress={() => this.props.onRowPress && this.props.onRowPress(item)}>
							{this.props.renderRow(item).map((x, i) => React.cloneElement(x, {
								key: i,
								style: {
									...styles.rowCell,
									flex: this.props.columns[i].flex,
									...x.props.style
								}
							}))}
						</TouchableOpacity>
					}
				/>
			</View>
		);
	}

	private onSort(column: keyof TData, sort: Sort): void {
		this.props.onSort && this.props.onSort(column, sort);
		this.setState({ sortedColumn: column, sort });
	}
}

interface Props<TData> {
	renderHeaderCell?(data: TableColumn<TData>): React.ReactElement | null;
	renderRow(data: TData): React.ReactElement[] | null;
	columns: TableColumn<TData>[];
	data: TData[];
	onSort?(column: keyof TData, sort: Sort): void;
	onRowPress?(data: TData): void;
}

interface State<TData> {
	sortedColumn: keyof TData;
	sort: Sort;
}


export interface TableColumn<TData> {
	columnName: keyof TData;
	display?: string;
	flex: number;
	headerAlign?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
	isSortable?: boolean;
}
