import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';
import theme from 'config/styles/theme';

const styles = scaleAllToDesign({
	titleCell: {
		flexDirection: 'row',
		justifyContent: 'flex-start',
		alignItems: 'center'
	},
	titleName: {
		fontSize: 24,
		color: theme.TEXT_DARK,
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2.3,
		marginLeft: 18
	},
	detail: {
		fontSize: 16,
		color: '#A0A0A0',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1, 
		textAlign: 'right',
	},
	header: {
		fontSize: 16,
		color: '#A0A0A0',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1,
		textAlign: 'center',
		marginBottom: 20,
		marginTop: 40
	}
});
export default styles;
