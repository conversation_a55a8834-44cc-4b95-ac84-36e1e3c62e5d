import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { DateTime } from 'redi-ui-utils';
import DateTimePicker from '@react-native-community/datetimepicker';
import styles from './styles';
import CalendarIcon from 'project_components/Icons/CalendarIcon';
import { scaleToDesign } from 'utils/design-scale';
import FormComponentError from 'components/Form/FormComponentError';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import { CommonFormComponentProps } from 'components/Form/form-props';

export default class DatePicker extends React.PureComponent<DatePickerProps, State> {
	constructor(props: DatePickerProps) {
		super(props);

		this.state = {
			showPicker: false
		};
	}

	componentDidMount() {
		if (!this.props.value) {
			this.onChange(new Date());
		}
	}

	render() {
		let value: Date = this.props.value ? this.props.value : new Date();

		if (!(value instanceof Date)) {
			value = new Date(value);
		}
		
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					<TouchableOpacity
						activeOpacity={0.95}
						style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}
						onPress={() => {
							this.props.onFocus?.(null);
							this.setState({ showPicker: true });
						}}>
						<View style={[styles.actionButton]}>
							<React.Fragment>
								<CalendarIcon colour="#FFFFFF" />
								<Text style={[styles.buttonText]}>Select Date</Text>
							</React.Fragment>
						</View>
						<View style={[styles.dateDisplayWrapper, this.props.overrideStyles?.dateDisplayWrapper]}>
							<View style={{ width: scaleToDesign(20) }} />
							<Text style={[styles.dateDisplayText, this.props.overrideStyles?.dateDisplayText]}>
								{this.formatDate(value)}
							</Text>
						</View>
					</TouchableOpacity>
					<FormComponentError error={this.props.error} />
				</View>
				{this.state.showPicker &&
					<DateTimePicker
						mode="date"
						value={value}
						onChange={(e, date) => this.onChange(date)}
					/>
				}
			</View>
		);
	}

	private onChange(date: Date | undefined): void {
		this.setState({ showPicker: false });
		if (date) {
			this.props.onChange(date);
		}
		this.props.onBlur?.(null);
	}
	private formatDate(date: Date): string {
		if (date) {
			return DateTime.format(date, 'dd-MMM-yyyy');
		}
		return null;
	}
}

export interface DatePickerProps extends CommonFormComponentProps<Date> {
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper: object,
		//styles that go on the View that wraps the label
		labelWrapper?: object,
		//styles tyhat go on the label Text element
		labelText?: object,
		//Styles to go on the view that wraps the text which displays the fomratted date value
		dateDisplayWrapper?: object,
		//Styles to go on the Text 
		dateDisplayText?: object,
	}
}

interface State {
	showPicker: boolean;
}