import React from 'react';
import { View } from 'react-native';
import { RouteComponentProps, Route } from 'react-router-native';
import HeaderBar from 'components/HeaderBar/HeaderBar';
import IconTextButton from 'project_components/IconTextButton/IconTextButton';
import navigator from 'services/navigator';
import { AppStore } from 'app/stores/app-store';
import Syncing from './Syncing/Syncing';
import History from './History/History';
import HistoryIcon from 'project_components/Icons/HistoryIcon';
import SyncIcon from 'project_components/Icons/SyncIcon';
import { StoreComponent, StoreProps } from 'utils/store-component';

@StoreComponent()
export default class Sync extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			selected: undefined
		};
	}

	componentDidMount() {
		this.props.subscribe(AppStore.currentRoute$, x => {
			let selected: SelectedItem;
			if (x === '/sync/syncing') {
				selected = 'syncing';
			} else if (x.startsWith('/sync')) {
				selected = 'inProgress';
			}
			this.setState({ selected });
		});
	}

	render() {
		return (
			<View>
				<HeaderBar>
					<IconTextButton
						display="SYNCING"
						selected={this.state.selected === 'syncing'}
						onClick={() => navigator.go('/sync/syncing')} >
						<SyncIcon />
					</IconTextButton>
					<IconTextButton
						display="HISTORY"
						selected={this.state.selected === 'inProgress'}
						onClick={() => navigator.go('/sync/history/all')} >
						<HistoryIcon />
					</IconTextButton>
				</HeaderBar>
				<Route path={`${this.props.match.path}/syncing`} component={Syncing} exact={true} />
				<Route path={`${this.props.match.path}/history/:filter`} component={History} exact={true} />
			</View>
		);
	}
}

interface Props extends StoreProps, RouteComponentProps { }

interface State {
	selected: SelectedItem;
}

type SelectedItem = 'syncing' | 'inProgress' | undefined;


