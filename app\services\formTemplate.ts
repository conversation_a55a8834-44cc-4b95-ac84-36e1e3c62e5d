import { AppStore } from "stores/app-store";
import { of } from 'rxjs';
import { repeatWhen, flatMap, delay, takeWhile } from 'rxjs/operators';
import { ConnectionStatus } from "./heartbeat";
import config from "config/config";
import Database from "./database";
import { AppFormTemplateDto, UpdateFormTemplateDto } from "dtos/formTemplate";
import { http } from "redi-http";

class formTemplateService {
	private _started = false;

	async getFormTemplates() {
		const { connectionStatus, user } = AppStore.getValue();

		if (connectionStatus !== ConnectionStatus.Connected || user === null || user === undefined) {
			return;
		}
		const sql = `
		SELECT *
		FROM formTemplate
	`;
		let existing: Array<AppFormTemplateDto> = [];
		try {
			existing = await Database.select<AppFormTemplateDto>(sql);
		} catch (error) {
			console.error(error);
		}

		let currentFormVersions: Array<UpdateFormTemplateDto> = [];
		existing.forEach(s => {
			currentFormVersions.push({
				workflowCode: s.workflowCode,
				version: s.version
			});
		});

		const url = `${config.apiUrl}Form/GetFormTemplates`;
		try {
			const response = await http<AppFormTemplateDto[]>({ method: 'POST', url, data: currentFormVersions });
			if (response.data) {
				await Database.insertOrReplace<AppFormTemplateDto>(
					'formTemplate',
					['formTemplateId', 'workflowCode', 'workflowDescription', 'jsonTemplate', 'version'],
					response.data
				);
			}
		} catch (error) {
			console.error(error);
		}

	}

	start() {
		AppStore.isLoggedIn$.subscribe(isLoggedIn => {
			if (isLoggedIn && !this._started) {
				this._started = true;
				of({})
					.pipe(
						flatMap(() => this.getFormTemplates()),
						repeatWhen(delay(5 * 60 * 1000)),
						takeWhile(() => AppStore.getValue().isLoggedIn)
					).subscribe(
						() => { },
						() => { },
						() => {
							this._started = false;
						}
					);
			}
		});
	}

	stop() {
		this._started = false;
	}
}
const FormTemplateService = new formTemplateService();
export default FormTemplateService;