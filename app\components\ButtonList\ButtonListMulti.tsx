import React, { Component } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import { string } from 'redi-ui-utils';

export interface ButtonListColor {
    selectedColor: string;
    unselectedColor: string;
}


export default class ButtonListMulti<T> extends React.PureComponent<ButtonListMultiProps<T>, State> {
    private _buttonColors: Array<ButtonListColor> = [
        {
            selectedColor: "rgb(27,152,224)",
            unselectedColor: "rgba(27,152,224,0.5)"
        },
        {
            selectedColor: "rgb(0,84,162)",
            unselectedColor: "rgba(0,84,162,0.5)"
        },
        {
            selectedColor: "rgb(0,44,85)",
            unselectedColor: "rgba(0,44,85,0.5)"
        }
    ];

    constructor(props: ButtonListMultiProps<T>) {
        super(props);

        this.state = {
            modalOpen: false
        };

        this.onSelect = this.onSelect.bind(this);
    }

    onSelect(value: T){
        let newValues: Array<T> = [...this.props.selected];
        if(newValues.indexOf(value) === -1){
            newValues.push(value);
        }else{
            newValues = newValues.filter(s => s !== value);
        }
        this.props.onChange(newValues);
    }

    render() {
        //use the button colors passed in from props if available, else use default ones.
        let buttonColors: Array<ButtonListColor> = this.props.buttonColors && this.props.buttonColors.length ? this.props.buttonColors : this._buttonColors;

        return (
            <View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
                {!!this.props.label &&
                    <Text style={[styles.label, this.props.overrideStyles?.label]}>
                        {this.props.label}
                    </Text>
                }
                <View style={[styles.buttonWrapper, this.props.overrideStyles?.buttonWrapper]}>
                    {this.props.values.map((value: T, index: number) => {
                        let wkColor: ButtonListColor = buttonColors[index % buttonColors.length];
                        return (
                            <TouchableOpacity style={[
                                styles.baseButton,
                                { backgroundColor: this.props.selected.indexOf(value) !== -1 ? wkColor.selectedColor : wkColor.unselectedColor }
                            ]}
                                key={index}
                                onPress={() => this.onSelect(value)}>
                                <Text style={[styles.baseButtonText]}>
                                    {this.props.displayTransform ? this.props.displayTransform(value) : value}
                                </Text>
                            </TouchableOpacity>
                        );
                    }
                    )}
                </View>
            </View>
        );
    }
}

export interface ButtonListMultiProps<T> {
    //callback that gets called everytime a selecteion is made. The newly selected value is passed in as an argument.
    onChange: (valuea: Array<T>) => void;
    //list of the button values
    values: Array<T>;
    //optional function that will be called with each value T and should return a string to display
    displayTransform?: (value: T) => string;
    //the currently selected option or null if ntohing should be selected.
    selected: Array<T>;
    //optional label to display above the buttons
    label?: string;
    //optional. provides a list of ButtonListColor. ButtonListColor has two properties - "selectedColor" and "unselectedColor" which define the button colors when selected and unselected. Colors are repeated if there are more values than colors.
    buttonColors?: Array<ButtonListColor>;
    //optional. Ovveride the styles for this component
    overrideStyles?: {
        //styles that go on the entire component wrapper view
        wrapper?: object;
        //styles that go on the label text component
        label?: object;
        //styles that go on the view that wraps just teh buttons
        buttonWrapper?: object;
    }
}

interface State {
}