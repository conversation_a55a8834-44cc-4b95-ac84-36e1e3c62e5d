
declare module "redi-types" {
	export interface FormData {
		imageCompression: {
			enabled: boolean;
			maxHeightWidth?: number;
			qualityPercent: number;
		};
		pages: FormDataPage[];
		header: FormDataHeader;
	}

	export interface FormDataHeader {
		//logo?: MediaImage;
		color: string;
		backgroundColor: string;
	}

	export interface FormDataPage {
		pageName: string;
		/** User avaliable/defined page id */
		textId: string;
		pageId: string;
		canRepeat: boolean;
		hide?: boolean;
		sections: FormDataSection[];
		logic?: FormLogic[];
	}

	export interface FormDataSection {
		sectionId: string;
		sectionName: string;
		textId: string;
		canRepeat: boolean;
		fields: (FormDataField | FormDataRepeatingField)[];
		hide?: boolean;
		logic?: FormLogic[];
	}

	export interface FormDataRepeatingField {
		repeatingFieldId: string;
		textId: string;
		fieldType: FormFieldTypesRepeating;
		rows: FormDataFieldRow[];
		logic?: FormLogic[];

		options: {
			label: string;
			hide?: boolean;
			minRowsRequired?: number;
			allowAddRows?: boolean;
			groupByColumns?: boolean;
			/** used in dropdowns, checboxes radios etc */
			choices?: any[];
		};

		/** ui specific options not sent to the server or related to the form */
		_ui: {
			showEditField?: boolean;
		};
	}

	export interface FormDataFieldRow {
		rowId: string;
		label: string;
		textId: string;
		rowIndex: number;
		fields: (FormDataField | FormDataRepeatingField)[];
		hide?: boolean;
		logic?: FormLogic[];

		options: {
			countRow?: boolean;
			sumRow?: boolean;
		};
	}

	export interface FormDataField {
		fieldId: string;
		label: string;
		textId: string;
		fieldType: FormFieldTypes;
		isRequired: boolean;
		value?: any;
		logic?: FormLogic[];

		/** a dummy prop to store the values of preview */
		preview?: FormSubmissionField;

		options: {
			hide?: boolean;
			defaultValue?: string;
			/** used in labels etc */
			text?: string;
			/** used in dropdowns, checboxes radios etc */
			choices?: any[];
			btnListOnMobile?: boolean;
			/** min number value */
			minValue?: string;
			/** max number value */
			maxValue?: string;
			/** max chars of any text field */
			maxCharLength?: number;
			/** applies to single line text only */
			fieldMask?: string;
			/** all fields */
			readonly?: boolean;
			/** type number input field step */
			inputStep?: number;
			//backgroundImage?: MediaImage;

			textFieldType?: string;
		};

		submissionOptions?: {
			emailBody?: boolean;
			emailSubject?: boolean;
			pdfFileName?: boolean;
		};

		/** ui specific options not sent to the server or related to the form */
		_ui: {
			showEditField?: boolean;
		};
	}

	export interface FormLogic {
		/** Fields that trigger this logic */
		fieldIds: string[];

		logicBuilder: FormLogicBuilder;
		logic: string;
	}

	export interface FormLogicBuilder {
		action: "hide" | "show";
		disabled: boolean;
		conditions: FormLogicBuilderCondition[];
	}

	export interface FormLogicBuilderCondition {
		selectedPageTextId: string;
		selectedSectionTextId: string;
		selectedFieldTextId: string;

		joiner?: "and" | "or";
		check: FormLogicBuilderItemCheckOperator;

		value: any;
	}

	export type FormLogicBuilderItemCheckOperator = "==" | "!=" | ">" | "<" | ">=" | "<=";

	export type FormFieldTypesRepeating = "RepeatingGroup" | "Matrix";

	export type FormFieldTypes =
		| "Label"
		| "Text"
		| "TextMultiLine"
		| "TypeaheadText"
		| "Dropdown"
		| "DropdownMultiChoice"
		| "Photo"
		| "Radio"
		| "Checkbox"
		| "CheckboxLarge"
		| "CheckboxMultiChoice"
		| "Number"
		| "NumberNeg"
		| "GPS"
		| "Date"
		| "DateTime"
		| "Time"
		| "Draw"
		| "Barcode"
		| "ButtonList"
		| "WebLink"
		| "ButtonListMulti"
		| "YesNo"
		| "YesNoNA"
		| "YesNoString"
		| "MarkPicture"
		| "Signature";

	export type FormFieldTypesAll = FormFieldTypes | FormFieldTypesRepeating;
}
