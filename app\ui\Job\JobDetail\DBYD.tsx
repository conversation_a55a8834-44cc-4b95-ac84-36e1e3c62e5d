import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import JobDetailSection from './JobDetailSection';
import textStyles from 'config/styles/text-styles';
import ChevronIcon from 'project_components/Icons/ChevronIcon';
import styles from './styles';
import theme from 'config/styles/theme';

export default class DBYD extends React.PureComponent<Props, State> {
	state = {
		isOpen: false
	};

	render() {
		return (
			<JobDetailSection>
				<Text style={textStyles.mediumGray}>DIAL BEFORE YOU DIG</Text>
				<TouchableOpacity
					onPress={() => this.setState(s => ({ isOpen: !s.isOpen }))}
					style={styles.expand}>
					<View style={styles.chevron}>
						<ChevronIcon
							colour={theme.PRIMARY_DARK_BLUE}
							position={this.state.isOpen ? 'down' : 'right'} />
					</View>
					<Text style={{ ...textStyles.regular, paddingTop: 18 }}>
						{this.state.isOpen ? 'Collapse all files' : 'Expand to reveal files'}
					</Text>
				</TouchableOpacity>
				{this.state.isOpen &&
					<FlatList
						data={data}
						renderItem={({ item }) => (
							<TouchableOpacity
								onPress={() => this.onClick(item)}
								style={styles.fileItem}>
								<Text
									numberOfLines={1}
									style={{ ...textStyles.regular, color: theme.TEXT_DARK }}>{item}</Text>
							</TouchableOpacity>
						)}
					/>
				}
			</JobDetailSection>
		);
	}

	private onClick(item: string): void {

	}
}

interface Props {

}
interface State {
	isOpen: boolean;
}

// TODO: Implement when the DBYD code is implemented server-side
// Coming Soon was placed on 29/10/2020 as requested by Paul
const data = [
	'Coming Soon...'
]