import React, { Component } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';

export default class CheckboxLarge extends React.PureComponent<CheckboxProps, never> {
	render() {
		const required = this.props.required ? ' *' : '';
		return (
			<TouchableOpacity
				style={{ alignContent: 'center', alignItems: 'center' }}
				onPress={() => {
					this.props.onFocus?.(null);
					this.props.onChange(!this.props.selected);
					this.props.onBlur?.(null);
				}}>
				<View style={[styles.checkboxWrapper, this.props.overrideStyles?.checkboxWrapper]}>
					<View style={[styles.checkboxOuter, this.props.overrideStyles?.checkboxOuter]}>
						{this.props.selected &&
							<View style={[styles.checkboxInner, this.props.overrideStyles?.checkboxInner]}>
							</View>
						}
					</View>
					<Text style={[styles.checkboxLabel, this.props.overrideStyles?.checkboxLabel]}>
						{this.props.label + required}
					</Text>
				</View>
				<FormComponentError error={this.props.error} />
			</TouchableOpacity>
		);
	}
}

export interface CheckboxProps extends Omit<CommonFormComponentProps<boolean>, 'value'> {
	selected?: boolean;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		//Styles that go on the view that the checkbox - inside of the touchable highlight
		checkboxWrapper?: object,
		//styles that go on the Text that displays the checbox label
		checkboxLabel?: object,
		//styles to go on the actual box part of the checkbox
		checkboxOuter?: object,
		//styles to go on the "check" part of the checkbox - does not actually need a check mark
		checkboxInner?: object
	}
}