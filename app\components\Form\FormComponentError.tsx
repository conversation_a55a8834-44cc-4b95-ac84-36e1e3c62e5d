import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default class FormComponentError extends React.PureComponent<Props, never> {
	render() {
		if (!this.props.error) {
			return null;
		}
		return (
			<View style={[styles.errorWrapper]}>
				<Text style={[styles.errorText]}>{this.props.error}</Text>
			</View>
		);
	}
}

interface Props {
	error?: string;
}

const styles = StyleSheet.create({
	errorWrapper: {
		display: 'flex',
		paddingLeft: 0,
		paddingRight: 10,
		marginBottom: 15,
		justifyContent: 'flex-start',
		flexDirection: 'row',
		width: '100%',
	},
	errorText: {
		color: 'red'
	}
});