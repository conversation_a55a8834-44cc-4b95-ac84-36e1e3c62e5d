import { FORM_CLASSES, FORM_STYLES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	wrapper: {
		...FORM_CLASSES.WRAPPER
	},
	content: {
		alignItems: 'flex-start',
		justifyContent: 'center',
		width: '100%',
	},
	textInputContainer: {
		...FORM_CLASSES.INPUT_CONTAINER,
		height: FORM_STYLES.INPUT_HEIGHT,
		alignItems: 'flex-start',
		justifyContent: 'center',
		width: '100%',
	},
	placeholder: {
		...FORM_CLASSES.PLACEHOLDER_TEXT,
		position: 'absolute',
		left: 7,
		paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT - 7,
		paddingRight: FORM_STYLES.INPUT_PADDING_LEFT - 7
	},
	focusedLabel: {
		top: -10,
		fontSize: 12
	},
	input: {
		width: '100%',
		fontSize: FORM_STYLES.LABEL_FONT_SIZE,
		color: FORM_STYLES.LABEL_FONT_COLOR,
		paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT,
		maxHeight: 60
	}
});

export default styles;
