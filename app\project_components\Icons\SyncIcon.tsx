import React from 'react';
import Svg, { G, Path, Polyline } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class SyncIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE,
		size: 35
	};

	render() {
		return (
			<Svg width={scaleToDesign(this.props.size) + 'px'} height={scaleToDesign(this.props.size) + 'px'} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path
						d="M4.11702951,15.7145057 C4.03983607,16.2987138 4,16.8947048 4,17.5 C4,24.9558441 10.0441559,31 17.5,31 C21.2252419,31 24.5980691,29.4911329 27.040671,27.0512092 M30.913713,19.0348115 C30.9707169,18.5310886 31,18.018964 31,17.5 C31,10.0441559 24.9558441,4 17.5,4 C13.7697186,4 10.3928061,5.51295221 7.94942111,7.95869804"
						stroke={this.props.colour}
						strokeWidth="2" />
					<Polyline
						stroke={this.props.colour}
						strokeWidth="2"
						transform="translate(4.159090, 17.500000) rotate(-5.000000) translate(-4.159090, -17.500000) "
						points="8.15909028 19.5 4.15909028 15.5 0.159090278 19.5" />
					<Polyline
						stroke={this.props.colour}
						strokeWidth="2"
						transform="translate(30.840910, 17.500000) scale(1, -1) rotate(-5.000000) translate(-30.840910, -17.500000) "
						points="34.8409097 19.5 30.8409097 15.5 26.8409097 19.5" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
	size: number;
}
