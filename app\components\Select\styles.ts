import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	root: {
		borderColor: theme.PRIMARY_LIGHT_BLUE,
		borderWidth: 2,
		borderRadius: 30,
		width: 250,
		height: 60,
		paddingHorizontal: 10,
		justifyContent: 'space-between',
		alignItems: 'center',
		flexDirection: 'row'
	},
	text: {
		...textStyles.large,
		color: theme.PRIMARY_MEDIUM_BLUE,
		marginLeft: 15
	},
	items: {
		position: 'absolute',
		top: 59,
		backgroundColor: theme.WHITE,
		borderColor: theme.PRIMARY_LIGHT_BLUE,
		borderWidth: 2,
		borderBottomLeftRadius: 30,
		borderBottomRightRadius: 30,
		paddingHorizontal: 10,
		width: 250,
		zIndex: 1
	},
	item: {
		paddingVertical: 15
	}
});
export default styles;