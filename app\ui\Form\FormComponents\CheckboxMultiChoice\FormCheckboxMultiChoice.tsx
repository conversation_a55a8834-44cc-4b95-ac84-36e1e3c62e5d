import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import CheckboxGroup, { CheckboxGroupProps } from 'components/Checkbox/CheckboxGroup';
import { OmitName } from 'redi-types';

export default class FormCheckboxMultiChoice<T> extends React.Component<WrappedFieldInterface<string[], FormCheckboxMultiChoiceProps<string>>, never> {
	constructor(props: any) {
		super(props);
		this.onChange = this.onChange.bind(this);
	}

	render() {
		const props = this.props.componentProps();
		return (
			<CheckboxGroup
				{...props}
				selected={this.props.fieldProps.value}
				onChange={this.onChange} />
		);
	}

	private onChange(selected: boolean, value: string) {
		let newValues: Array<string> = [...this.props.fieldProps.value];
		if (selected) {
			newValues.push(value);
		} else {
			newValues = newValues.filter(s => s != value);
		}
		this.props.fieldProps.onChange(newValues);
	}
}

export interface FormCheckboxMultiChoiceProps<T> extends OmitName<CheckboxGroupProps<T>, "selected"> {
}