import { StoreBase } from 'utils/store-base';
import { UserDto } from 'dtos/security';
import { ConnectionStatus } from 'services/heartbeat';
import navigator from 'services/navigator';
import { Region } from 'react-native-maps';
import { string, number } from 'redi-ui-utils';

class appStore extends StoreBase<State> {
	constructor() {
		super('app', {
			currentRoute: '',
			isLoggedIn: false,
			connectionStatus: ConnectionStatus.Disconnected,
			lastModified: null,
			mapRegion: null
		});
	}

	readonly currentRoute$ = this.query.select(x => x.currentRoute);
	readonly isLoggedIn$ = this.query.select(x => x.isLoggedIn);
	readonly user$ = this.query.select(x => x.user);
	readonly jwt$ = this.query.select(x => x.jwt);
	readonly connectionStatus$ = this.query.select(x => x.connectionStatus);
	readonly lastModified$ = this.query.select(x => x.lastModified);
	readonly region$ = this.query.select(x => x.mapRegion);
	readonly team$ = this.query.select(x => x.team);
	readonly teamId$ = this.query.select(x => x.teamId);
}
export const AppStore = new appStore();

navigator.history.listen(x => AppStore.update({ currentRoute: x.pathname }));

interface State {
	currentRoute: string;
	isLoggedIn: boolean;
	user: UserDto;
	jwt: string;
	connectionStatus: ConnectionStatus;
	lastModified?: Date;
	team: string;
	teamId?: number;
	mapRegion?: Region;
}