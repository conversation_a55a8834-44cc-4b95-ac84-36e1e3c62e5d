import React from 'react';
import { View, Text, TouchableOpacity, Image, PermissionsAndroid } from 'react-native';
import {CameraOptions, launchCamera, launchImageLibrary} from 'react-native-image-picker';
import styles from './styles';
import CameraIcon from 'project_components/Icons/CameraIcon';
import PhotoModal from './PhotoModal';
import { scaleToDesign } from 'utils/design-scale';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import FormComponentError from 'components/Form/FormComponentError';
import uuid4 from 'uuid/v4';
import { number } from 'redi-ui-utils';
import SearchIcon from 'project_components/Icons/SearchIcon';
import ImagePicker, { ImageOrVideo } from 'react-native-image-crop-picker';

export default class Photo extends React.PureComponent<PhotoProps, State> {
	constructor(props: PhotoProps) {
		super(props);

		this.state = {
			modalPhoto: null,
			showSubButtons: false
		};

		this.showSubButtons = this.showSubButtons.bind(this);
		this.selectPhoto = this.selectPhoto.bind(this);
		this.selectGallery = this.selectGallery.bind(this);
		this.onPressImage = this.onPressImage.bind(this);
		this.onDelete = this.onDelete.bind(this);
	}

	showSubButtons() {
		this.setState({showSubButtons: true});
	}

	async selectPhoto() {
		this.props.onFocus(null);
		let cameraOptions = {
			mediaType: 'photo'
		} as CameraOptions;
		if (this.props.maxHeight) {
			cameraOptions.maxHeight = this.props.maxHeight;
		}
		if (this.props.maxWidth) {
			cameraOptions.maxWidth = this.props.maxWidth;
		}

		try {
			const granted = await PermissionsAndroid.request(
				PermissionsAndroid.PERMISSIONS.CAMERA,
				{
					title: 'App Camera Permission',
					message:'App needs access to your camera ',
					buttonNeutral: 'Ask Me Later',
					buttonNegative: 'Cancel',
					buttonPositive: 'OK'
				}
			);
			if (granted === PermissionsAndroid.RESULTS.GRANTED) {
				console.log('Camera permission given');
			} else {
				console.log('Camera permission denied');
			}
		} catch (err) {
			console.warn(err);
		}

		launchCamera(cameraOptions, image => {
			if (image.didCancel) {
				this.props.onBlur?.(null);
				this.setState({showSubButtons: false});
				return;
			}
			if (image && image.assets && image.assets.length > 0) {
				const value: PhotoValueDto = {
					fileName: uuid4(),
					filePath: image.assets[0].fileName,
					contentPath: image.assets[0].uri
				};
				const newValues: Array<PhotoValueDto> = [...this.props.photos, value];
				this.props.onChange(newValues);
				this.props.onAdd?.(value);
				this.props.onBlur?.(null);
				this.setState({showSubButtons: false});
			} else {
				this.props.onBlur?.(null);
				this.setState({showSubButtons: false});
			}
		});
	}

	selectGallery() {
		let cameraOptions = {
			mediaType: 'photo',
			multiple: false
		} as any;
		if (this.props.maxHeight) {
			cameraOptions.height = this.props.maxHeight;
		}
		if (this.props.maxWidth) {
			cameraOptions.width = this.props.maxWidth;
		}

		ImagePicker.openPicker(cameraOptions).then(imageOrVideo => {
			let image = imageOrVideo as ImageOrVideo;
			const value: PhotoValueDto = {
				fileName: uuid4(),
				filePath: image.path.substring(image.path.lastIndexOf("/") + 1),
				contentPath: image.path
			};

			const newValues: Array<PhotoValueDto> = [...this.props.photos, value];
			this.props.onChange(newValues);
			this.props.onAdd?.(value);
			this.props.onBlur?.(null);
			this.setState({showSubButtons: false});
		});
	}

	onPressImage(photo: PhotoValueDto) {
		this.props.onFocus(null);
		this.setState({ modalPhoto: photo });
	}

	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<PhotoModal
					photo={this.state.modalPhoto}
					onClose={() => this.setState({ modalPhoto: null })}
					onDelete={this.onDelete} />
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					{this.props.photos.length < this.props.maxPhotos && !this.state.showSubButtons &&
					<TouchableOpacity style={[styles.addPhotoButton]} onPress={this.showSubButtons}>
						<View style={[{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }]}>
							<CameraIcon colour="#FFFFFF" />
							<Text style={[styles.buttonText]}>Add Photo</Text>
						</View>
					</TouchableOpacity>}
					{this.props.photos.length < this.props.maxPhotos && this.state.showSubButtons &&
					<View style={[styles.subButtonContainer]}>
						<TouchableOpacity style={[styles.addPhotoButton, {marginRight: 4}]} onPress={this.selectPhoto}>
							<View style={[{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }]}>
								<CameraIcon colour="#FFFFFF" />
								<Text style={[styles.buttonText]}>Camera</Text>
							</View>
						</TouchableOpacity>
						<TouchableOpacity style={[styles.addPhotoButton, {marginLeft: 4}]} onPress={this.selectGallery}>
							<View style={[{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }]}>
								<SearchIcon colour="#FFFFFF" />
								<Text style={[styles.buttonText]}>Gallery</Text>
							</View>
						</TouchableOpacity>
					</View>}
					{!!this.props.photos &&
						<View style={[styles.imagesWrapper]}>
							{this.props.photos.map(x =>
								<TouchableOpacity key={x.fileName} onPress={() => this.onPressImage(x)}>
									<Image
										style={[styles.image]}
										source={{
											uri: x.contentPath,
											width: scaleToDesign(150),
											height: scaleToDesign(150)
										}}
										resizeMode={'cover'} />
								</TouchableOpacity>
							)}
						</View>
					}
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private onDelete(): void {
		let newValues = this.props.photos.filter(x => x.filePath !== this.state.modalPhoto.filePath);
		if (newValues.length === 0) {
			newValues = null;
		}
		this.props.onChange(newValues);
		this.props.onRemove(this.state.modalPhoto);
		this.setState({ modalPhoto: null });
		this.props.onBlur(null);
	}
}

export interface PhotoProps extends Omit<CommonFormComponentProps<PhotoValueDto[]>, 'value'> {
	onAdd?: (photo: PhotoValueDto) => void;
	onRemove?: (photo: PhotoValueDto) => void;
	photos?: Array<PhotoValueDto>;
	maxHeight?: number;
	maxWidth?: number;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper: object,
		//styles that go on the View that wraps the label
		labelWrapper?: object,
		//styles tyhat go on the label Text element
		labelText?: object,
	}
	maxPhotos: number;
}

export interface PhotoValueDto {
	fileName: string;
	filePath: string;
	contentPath: string;
}

interface State {
	modalPhoto: PhotoValueDto;
	showSubButtons: boolean;
}