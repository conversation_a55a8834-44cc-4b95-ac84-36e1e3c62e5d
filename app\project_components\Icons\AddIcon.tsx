import React from 'react';
import Svg, { G, Circle, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class AddIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(25) + "px";
		let height: string = scaleToDesign(25) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Circle stroke={this.props.colour} strokeWidth="2" cx="17.5" cy="17.5" r="16.5" />
					<Line x1="17.5" y1="11.5" x2="17.5" y2="23.5" stroke={this.props.colour} strokeWidth="2" />
					<Line x1="17.5" y1="11.5" x2="17.5" y2="23.5" stroke={this.props.colour} strokeWidth="2" transform="translate(17.500000, 17.500000) rotate(-90.000000) translate(-17.500000, -17.500000) " />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
