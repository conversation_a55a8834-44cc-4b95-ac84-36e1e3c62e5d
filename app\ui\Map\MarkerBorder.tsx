import React from 'react';
import { View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { scaleToDesign } from 'utils/design-scale';

const radius = 90;
const circumference = 2 * Math.PI * radius;
const viewBoxSize = 220;

export default class MarkerBorder extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		const colors = new Array<string>();
		if (props.hasEsa) {
			colors.push('#1B98E0');
		}
		if (props.hasDec) {
			colors.push('#801A86');
		}
		if (props.hasPip) {
			colors.push('#002C55');
		}
		this.state = { colors };
	}
	render() {
		return (
			<View style={{ position: 'absolute', top: 0, left: 0 }}>
				<Svg
					viewBox={`0 0 ${viewBoxSize} ${viewBoxSize}`}
					width={scaleToDesign(75)}
					height={scaleToDesign(75)}>
					{this.state.colors.map((x, i) =>
						<Circle
							key={x}
							cx={viewBoxSize / 2}
							cy={viewBoxSize / 2}
							r={radius}
							fill="none"
							stroke={x}
							strokeWidth={10}
							strokeLinecap="round"
							strokeDashoffset={this.getOffset(i, this.state.colors.length)}
							strokeDasharray={this.getDashArray(this.state.colors.length)}
						/>
					)}
				</Svg>
			</View>
		);
	}

	private getOffset(index: number, total: number): number {
		return (circumference * (index + 1) / total) + (circumference * 0.25);
	}
	private getDashArray(total: number): number[] {
		const percent = 1 / total;
		let dash = circumference * percent;
		if (total > 1) {
			dash *= 0.90;
		}
		return [dash, (circumference - dash)];
	}
}

interface Props {
	hasEsa: boolean;
	hasPip: boolean;
	hasDec: boolean;
}

interface State {
	colors: string[];
}