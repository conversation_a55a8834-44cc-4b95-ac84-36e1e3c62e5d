import React from 'react';
import { View, Text, Modal, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import styles from './styles';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import Dropdown from 'components/Dropdown/Dropdown';
import { ReactComponent } from 'redi-types';
import { StoreComponent } from 'utils/store-component';
import { AppStore } from 'stores/app-store';
import { map } from 'rxjs/operators';
import { ConnectionStatus } from 'services/heartbeat';
import ClientService from 'services/client';
import AssetService from 'services/asset';
import { ClientDto } from 'dtos/client';
import { AssetDto } from 'dtos/asset';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import PIPIcon from '../JobIcons/PIPIcon';
import ESAIcon from '../JobIcons/ESAIcon';
import BFMIcon from '../JobIcons/BFMIcon';
import modalStyles from 'config/styles/modal-styles';
import Redi_Text from 'components/Text/Redi_Text';
import AppButton from 'components/AppButton/AppButton';
import JobService from 'services/job';
import navigator from 'services/navigator';
import { scaleToDesign } from 'utils/design-scale';

@StoreComponent({
	isOffline: AppStore.connectionStatus$.pipe(map(x => x === ConnectionStatus.Disconnected))
})
class AddPole extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			client: '',
			clients: new Array<ClientDto>(),
			asset: '',
			foundAsset: null,
			notFound: false
		};

		this.add = this.add.bind(this);
		this.search = this.search.bind(this);
	}

	componentDidMount() {
		ClientService.saveClientsFromServer().then(() => {
			ClientService.getClients().then(x => this.setState({ clients: x }));
		});
	}

	selectClient(c:string) {
		console.log(c);
		this.setState({client: c});
	}

	render() {
		return (
			<Modal
				transparent
				visible
				onRequestClose={this.props.onClose}>
				<View style={{ alignItems: 'center'}}>
					<View style={{...modalStyles.content,paddingBottom:scaleToDesign(120)}}>
						<View style={modalStyles.header}>
							<Text style={textStyles.large_2}>Add Pole</Text>
							<TouchableOpacity onPress={this.props.onClose}>
								<CloseIcon />
							</TouchableOpacity>
						</View>
						<Dropdown
							placeholder="Client"
							value={this.state.client}
							onChange={e => this.selectClient(e)}
							options={this.state.clients.map(x => x.name)}
							overrideStyles={{
								wrapper: {
									paddingLeft: 0,
									paddingRight: 0
								},
								optionsWrapper: {
									maxHeight: 180
								}
							}} />
						<Redi_Text
							placeholder="Pick ID"
							value={this.state.asset}
							overrideStyles={{ wrapper: { marginBottom: 10 } }}
							onChange={e => this.setState({ asset: e })} />
						{this.props.isOffline &&
							<AppButton
								theme="lightblue"
								content="Add"
								onPress={() => this.add()} />
						}
						{!this.props.isOffline &&
							<AppButton
								theme="lightblue"
								content="Search"
								onPress={() => this.search()} />
						}
						{this.state.foundAsset &&
							<TouchableOpacity
								onPress={() => this.add()}>
								<View style={{...styles.foundAsset, flexDirection:'row'}}>
									<View style={{ ...styles.assetCell, flex: 3 }}>
										<ScheduledFormIcon />
										<Text style={styles.assetName}>{this.state.foundAsset.assetName}</Text>
									</View>
									<View style={{ flex: 1 }}>
										{(this.state.foundAsset.pipCustomer_Info !== null ||
											this.state.foundAsset.pipComments !== null) &&
											<PIPIcon />
										}
									</View>
									<View style={{ flex: 1 }}>
										{this.state.foundAsset.esaInfo !== null && <ESAIcon />}
									</View>
									<View style={{ flex: 1 }}>
										<BFMIcon rating={this.state.foundAsset.fireRiskZoneClass} />
									</View>
									<Text style={{ ...styles.suburb, flex: 1 }}>{this.state.foundAsset.suburb}</Text>
								</View>
							</TouchableOpacity>
						}
						{this.state.notFound &&
							<React.Fragment>
								<Text style={styles.message}>
									We could not find that Pick ID. Would you like to create a new pole?
								</Text>
								<AppButton
									theme="green"
									content="Yes"
									onPress={() => this.add()} />
							</React.Fragment>
						}
					</View>
				</View>
			</Modal>
		);
	}

	search(): void {
		const clientId = this.state.clients.find(x => x.name === this.state.client)?.clientId;
		AssetService.getAsset(clientId, this.state.asset).then(x => {
			if (x.data) {
				this.setState({ foundAsset: x.data, notFound: false });
			}
			else {
				this.setState({ notFound: true, foundAsset: null });
			}
		});
	}
	add() {
		JobService.createNewJob(
			this.state.foundAsset ?? this.state.asset,
			this.props.workflowCode,
			this.props.maintenanceZone,
			this.state.client,
			this.props.workOrder
		).then(x => {
			this.props.onClose();
			navigator.go(`/jobDetail/${x}`);
		});
	}
}
export default AddPole as ReactComponent<PublicProps>;

interface Props extends PublicProps {
	isOffline: boolean;
}

interface PublicProps {
	onClose(): void;
	maintenanceZone: string;
	workflowCode: string;
	workOrder: string;
}

interface State {
	client: string;
	clients: ClientDto[];
	asset: string;
	foundAsset: AssetDto;
	notFound: boolean;
}