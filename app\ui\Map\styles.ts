import { StyleSheet } from 'react-native';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	marker: {
		position: 'relative',
		height: 75,
		width: 75,
		justifyContent: 'center',
		alignItems: 'center'
	},
	outerCircle: {
		width: 50,
		height: 50,
		borderRadius: 25,
		justifyContent: 'center',
		alignItems: 'center'
	},
	centerCircle: {
		width: 20,
		height: 20,
		borderRadius: 10,
		borderColor: '#fff',
		borderWidth: 2
	},
	currentLocCircle: {
		width: 30,
		height: 30,
		borderRadius: 15,
		justifyContent: 'center',
		alignItems: 'center'
	},
	callout: {
		backgroundColor: '#fff',
		height: 100,
		width: 100
	}
});
export default styles;