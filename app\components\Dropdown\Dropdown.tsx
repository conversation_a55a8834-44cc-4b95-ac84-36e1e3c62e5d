import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, ViewStyle, FlatList } from 'react-native';
import styles from './styles';
import ChevronIcon from 'project_components/Icons/ChevronIcon';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import { number } from 'redi-ui-utils';

class DropdownCell extends React.PureComponent<DropdownCellProps> {
	constructor(props: DropdownCellProps) {
		super(props);
	}

	render() {
		return (
			<TouchableOpacity key={this.props.index} onPress={() => this.props.onSelect(this.props.item)}>
				<View style={[styles.optionRow]}>
					<Text style={[styles.optionText]}>
						{this.props.displayTransform ? this.props.displayTransform(this.props.item) : this.props.item}
					</Text>
				</View>
			</TouchableOpacity>
		)
	}
}

interface DropdownCellProps {
	item: any;
	index: number;
	onSelect: Function;
	displayTransform?: (value: any) => string;
}

export default class Dropdown<T> extends React.PureComponent<DropdownProps<T>, State> {
	constructor(props: DropdownProps<T>) {
		super(props);
		this.state = {
			focused: false,
		};

		this.onFocus = this.onFocus.bind(this);
		this.onSelect = this.onSelect.bind(this);
	}

	render() {
		const required = this.props.required ? ' *' : '';
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content, this.props.overrideStyles?.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required} />
					<TouchableOpacity onPress={this.onFocus} style={[styles.selectWrapper, this.state.focused ? { borderBottomRightRadius: 0, borderBottomLeftRadius: 0 } : {}, this.props.overrideStyles?.selectWrapper]}>
						<React.Fragment>
							{!!this.props.value &&
								<Text style={[styles.selectedText]}>{this.props.displayTransform ? this.props.displayTransform(this.props.value) : this.props.value}</Text>
							}
							{!!this.props.placeholder &&
								<Text style={[styles.placeholderText, this.props.value ? styles.focusedLabel : {}]}>{this.props.placeholder + required}</Text>
							}
							{/* just to push chevron to end */}
							<View style={{ flex: 1 }} />
							<ChevronIcon position="down" colour="#0054A2" />
						</React.Fragment>
					</TouchableOpacity>
					{
						!!this.state.focused &&
						<FlatList nestedScrollEnabled={true} data={this.props.options} style={[styles.optionsWrapper, this.props.overrideStyles?.optionsWrapper]}
							renderItem={({item, index}) => <DropdownCell item={item} index={index} displayTransform={this.props.displayTransform} onSelect={this.onSelect} />}
						/>
					}
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private onFocus() {
		if (this.state.focused) {
			this.props.onBlur?.(null);
		}
		else {
			this.props.onFocus?.(null);
		}
		this.setState({ focused: !this.state.focused });
	}
	private onSelect(value: T) {
		this.props.onChange(value);
		this.setState({ focused: false });
		this.props.onBlur?.(null);
	}
}

export interface DropdownProps<T> extends CommonFormComponentProps<T> {
	/** Optional. A function to transform the type of value to a string for display. You should consider this required if T is an object.*/
	displayTransform?: (value: T) => string;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper?: ViewStyle;
		content?: ViewStyle;
		optionsWrapper?: ViewStyle;
		selectWrapper?: ViewStyle;
	}
	options: T[];
}

interface State {
	focused: boolean;
}