import React from 'react';
import Svg, { <PERSON>, <PERSON>, <PERSON>yline, Circle } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class CameraIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(30) + "px";
		let height: string = scaleToDesign(30) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 30 30">
				<G stroke="none" strokeWidth="2" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
                    <Circle id="Oval" stroke="#FFFFFF" stroke-width="2" cx="15" cy="15.8946537" r="5.03391775"></Circle>
                    <Path id="Path-5" stroke-width="2" stroke={this.props.colour} d="M18.0968843,5.5 L11.9031157,5.5 C11.0420927,5.5 10.2777093,6.05106435 10.0056099,6.86796241 L9.69393108,7.80368572 C9.55788136,8.21213475 9.17568964,8.48766693 8.74517815,8.48766693 L5.5,8.48766693 C3.84314575,8.48766693 2.5,9.83081268 2.5,11.4876669 L2.5,21.5 C2.5,23.1568542 3.84314575,24.5 5.5,24.5 L24.5,24.5 C26.1568542,24.5 27.5,23.1568542 27.5,21.5 L27.5,11.4876669 C27.5,9.83081268 26.1568542,8.48766693 24.5,8.48766693 L21.2548218,8.48766693 C20.8243104,8.48766693 20.4421186,8.21213475 20.3060689,7.80368572 L19.9943901,6.86796241 C19.7222907,6.05106435 18.9579073,5.5 18.0968843,5.5 Z"></Path>
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}