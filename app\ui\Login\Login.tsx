import React from 'react';
import { View, Image, Keyboard } from 'react-native';
import styles from './styles';
import SecurityService from 'services/security';
import AppButton from 'components/AppButton/AppButton';
import Redi_Text from 'components/Text/Redi_Text';
import { scaleToDesign } from 'utils/design-scale';
import { FORM_STYLES } from 'config/styles/form-component-common';
import ChangeTeam from 'ui/Settings/ChangeTeam/ChangeTeam';
import Dropdown from 'components/Dropdown/Dropdown';
import { TeamDto } from 'dtos/team';
import TeamService from 'services/team';
import { AppStore } from 'stores/app-store';
import Database from 'services/database';

export default class Login extends React.PureComponent<{}, State> {
	state = {
		username: '',
		password: '',
		showTeam: false,
		team: '',
		teams: new Array<TeamDto>()
	};

	render() {
		return (
			<View style={styles.root}>
				<View style={styles.content}>
					<Image
						style={styles.image}
						source={require('app/assets/logo.png')} />
					<View style={{ width: '100%' }}>
						{!this.state.showTeam ?
							<React.Fragment>
								<Redi_Text
									placeholder="User"
									disableFloatingPlaceholder
									overrideStyles={{
										content: {
											backgroundColor: '#fff',
											borderWidth: FORM_STYLES.BORDER_WIDTH,
											borderColor: FORM_STYLES.BORDER_COLOR,
											borderRadius: FORM_STYLES.BORDER_RADIUS,
										}
									}}
									value={this.state.username}
									onChange={e => this.setState({ username: e })} />
								<Redi_Text
									placeholder="Password"
									secureTextEntry={true}
									value={this.state.password}
									disableFloatingPlaceholder
									overrideStyles={{
										content: {
											backgroundColor: '#fff',
											borderWidth: FORM_STYLES.BORDER_WIDTH,
											borderColor: FORM_STYLES.BORDER_COLOR,
											borderRadius: FORM_STYLES.BORDER_RADIUS,
										}
									}}
									onChange={e => this.setState({ password: e })} />
								<AppButton
									theme="green"
									marginVertical={scaleToDesign(10)}
									content="Login"
									onPress={() => this.onLogin()} />
							</React.Fragment>
							:
							<React.Fragment>
								<Dropdown
									placeholder="Select Team"
									value={this.state.team}
									onChange={e => this.setState({ team: e })}
									options={this.state.teams.map(x => x.name)}
									overrideStyles={{
										wrapper: {
											paddingLeft: 0,
											paddingRight: 0
										},
										selectWrapper: {
											backgroundColor: '#fff',
											borderWidth: FORM_STYLES.BORDER_WIDTH,
											borderColor: FORM_STYLES.BORDER_COLOR,
											borderRadius: FORM_STYLES.BORDER_RADIUS,
										}
									}} />
								<AppButton
									theme="lightblue"
									content="Confirm"
									onPress={() => this.selectTeam()} />
							</React.Fragment>
						}
					</View>
				</View>
			</View>
		);
	}

	private onLogin(): void {
		Keyboard.dismiss();
		SecurityService.login(this.state.username, this.state.password).then(async x => {
			if (x === true) {
				await TeamService.saveTeamsFromServer();
				TeamService.getTeams().then(x => this.setState({ teams: x.sort((a, b) => a.sortOrder - b.sortOrder) }));
				this.setState({ showTeam: true });
			}
		});
	}
	private async selectTeam(): Promise<void> {
		const teamId = this.state.teams.find(x => x.name === this.state.team).teamId;
		await Database.execute(`
			UPDATE user
			SET teamId = ?, teamName = ?
			WHERE userId = ?
		`, [teamId, this.state.team, AppStore.getValue().user.UserId]);
		AppStore.update({
			team: this.state.team,
			teamId,
			isLoggedIn: true
		});
	}
}

interface State {
	username: string;
	password: string;
	showTeam: boolean;
	teams: TeamDto[];
	team: string;
}
