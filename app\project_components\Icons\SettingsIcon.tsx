import React from 'react';
import Svg, { G, <PERSON> } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class SettingsIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path
						d="M17.4994951,23.2255409 C14.3375463,23.2255409 11.7744591,20.6614438 11.7744591,17.5005049 C11.7744591,14.3375463 14.3375463,11.7744591 17.4994951,11.7744591 C20.6614438,11.7744591 23.224531,14.3375463 23.224531,17.5005049 C23.224531,20.6614438 20.6614438,23.2255409 17.4994951,23.2255409 Z M31.4752884,14.9757934 L30.7724087,14.9757934 C29.579735,14.9757934 28.5062276,14.2567555 28.0527894,13.1539615 C28.04774,13.1428528 28.0416807,13.1297243 28.0376412,13.1186155 C27.5781436,12.0148116 27.8245555,10.7453867 28.6698289,9.90011323 L29.1666922,9.40224011 C30.1523396,8.41659271 30.1523396,6.81794534 29.1666922,5.83229795 C28.1810448,4.84665055 26.5823974,4.84665055 25.59675,5.83229795 L25.1140252,6.31603268 C24.266732,7.16332589 22.9942773,7.41074762 21.8904734,6.94620069 C21.8621967,6.93408208 21.8349298,6.92297335 21.8076629,6.91186461 C20.7058788,6.45842642 19.9908804,5.38390917 19.9908804,4.19426508 L19.9908804,3.52370169 C19.9908804,2.1300609 18.8608195,1 17.4671787,1 L17.4661689,1 C16.0715182,1 14.9414573,2.1300609 14.9414573,3.52370169 L14.9414573,4.19426508 C14.9414573,5.38390917 14.226459,6.45842642 13.1256847,6.91085473 C13.0984178,6.92297335 13.0701411,6.93408208 13.0418643,6.94620069 C11.9380604,7.41074762 10.6656058,7.16332589 9.81932246,6.31603268 L9.36891391,5.86663402 C8.38326652,4.87997674 6.78461915,4.87997674 5.79897175,5.86663402 C4.81332436,6.85228142 4.81332436,8.45092879 5.79897175,9.43657619 L6.26351868,9.90011323 C7.10778223,10.7453867 7.35419408,12.0148116 6.89570646,13.1186155 C6.88964715,13.1297243 6.88459773,13.1428528 6.87954831,13.1539615 C6.42611011,14.2567555 5.35260275,14.9757934 4.16093889,14.9757934 L3.52370169,14.9757934 C2.1300609,14.9757934 1,16.1058543 1,17.4994951 L1,17.5005049 C1,18.8941457 2.1300609,20.0252165 3.52370169,20.0252165 L4.16093889,20.0252165 C5.35058298,20.0252165 6.42510022,20.7402148 6.87752854,21.8399792 C6.88964715,21.868256 6.90075588,21.8965327 6.9128745,21.9237996 C7.37742143,23.0286134 7.12898981,24.3000581 6.28270649,25.1473513 L5.83229795,25.5977599 C4.84665055,26.5834073 4.84665055,28.1820547 5.83229795,29.1677021 C6.81794534,30.1533495 8.41659271,30.1533495 9.40224011,29.1677021 L9.86678704,28.7031551 C10.7120605,27.8578817 11.9804756,27.6114698 13.0842795,28.0709673 C13.0963981,28.0760168 13.1085167,28.0810662 13.1206353,28.0861156 C14.2234293,28.5405637 14.9414573,29.6140711 14.9414573,30.8057349 L14.9414573,31.4752884 C14.9414573,32.8699391 16.0715182,34 17.4661689,34 L17.4671787,34 C18.8608195,34 19.9908804,32.8699391 19.9908804,31.4752884 L19.9908804,30.8057349 C19.9908804,29.6130612 20.7099183,28.5405637 21.8127123,28.0861156 L21.8480583,28.0709673 C22.9508523,27.6114698 24.2202773,27.8578817 25.0655507,28.7031551 L25.5634238,29.2010282 C26.5490712,30.1866756 28.1477186,30.1866756 29.133366,29.2010282 C30.1190134,28.2153808 30.1190134,26.6167335 29.133366,25.6310861 L28.6506411,25.1473513 C27.8033479,24.3000581 27.5559262,23.0286134 28.0194632,21.9237996 C28.0315818,21.8965327 28.0426906,21.868256 28.0548092,21.8399792 C28.5072375,20.7402148 29.5817548,20.0252165 30.7724087,20.0252165 L31.4752884,20.0252165 C32.8699391,20.0252165 34,18.8941457 34,17.5005049 L34,17.4994951 C34,16.1058543 32.8699391,14.9757934 31.4752884,14.9757934 Z"
						stroke={this.props.colour}
						strokeWidth="2" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
