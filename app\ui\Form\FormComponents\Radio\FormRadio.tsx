import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Radio, { RadioProps } from 'components/Radio/Radio';

export default class FormRadio<T> extends React.Component<WrappedFieldInterface<T, RadioProps<T>>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<Radio
				{...props}
				selected={this.props.fieldProps.value}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				error={this.props.form.field.error} />
		);
	}
}