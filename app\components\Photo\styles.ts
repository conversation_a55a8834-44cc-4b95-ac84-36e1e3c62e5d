import { FORM_STYLES, FORM_CLASSES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	wrapper: {
		flexDirection: 'column',
		alignItems: 'center',
		marginTop: FORM_STYLES.WRAPPER_MARGIN_TOP,
		marginBottom: FORM_STYLES.WRAPPER_MARGIN_BOTTOM
	},
	content: {
		width: '80%'
	},
	addPhotoButton: {
		...FORM_CLASSES.BUTTON,
		width: 305,
		borderRadius: 10,
		backgroundColor: '#0054A2',
	},
	buttonText: {
		fontSize: 22,
		color: 'white',
		marginLeft: 10
	},
	imagesWrapper: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		alignContent: 'flex-start'
	},
	image: {
		marginBottom: 5,
		marginTop: 5,
		marginRight: 10,
		borderRadius: 10
	},
	modalBar: {
		position: 'absolute',
		bottom: 0,
		alignItems: 'center',
		width: '100%',
		backgroundColor: 'rgba(0,0,0,0.6)',
		height: 80,
		justifyContent: 'space-between',
		padding: 10,
		flexDirection: 'row'
	},
	subButtonContainer: {
		flexDirection: 'row',
	}
});

export default styles;
