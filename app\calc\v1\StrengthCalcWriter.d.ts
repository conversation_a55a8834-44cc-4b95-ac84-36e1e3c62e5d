/// <reference path="./bridge.d.ts" />

declare namespace StrengthCalcWriter {
    interface Damage {
        Orientation: number;
        Width: number;
        Depth: number;
        Summary: string | null;
    }
    interface DamageFunc extends Function {
        prototype: Damage;
        DamageType: Damage.DamageTypeFunc;
        new (): Damage;
    }
    var Damage: DamageFunc;
    module Damage {
        interface DamageType {
        }
        interface DamageTypeFunc extends Function {
            prototype: DamageType;
            Exposed: number;
            Enclosed: number;
            Mechanical: number;
            ShellRot: number;
            None: number;
        }
    }

    interface StrengthCalc {
        ExposedPockets: System.Collections.Generic.List$1<StrengthCalcWriter.Damage> | null;
        MechanicalDamages: System.Collections.Generic.List$1<StrengthCalcWriter.Damage> | null;
        EnclosedPockets: System.Collections.Generic.List$1<StrengthCalcWriter.EnclosedPocket> | null;
        cOriginalCircumference: number;
        cShellRotThisCycle: number;
        MeasurementTool: number;
        RemainingStrength: number;
        RemainingShell: number;
        Initialized: boolean;
        ErrorMessages: System.Collections.Generic.List$1<StrengthCalcWriter.Error> | null;
        HasErrors: boolean;
        Copy(): StrengthCalcWriter.StrengthCalc | null;
        Compare(pOther: StrengthCalcWriter.StrengthCalc | null): boolean;
        ConvertLocation(location: number): number;
        CalculateStrength(): boolean;
        PoleDecayPocket(Location: number, R: number, T: number, D: number, Area: {v: number}, CG: {v: number}, MOI: {v: number}, Y1: {v: number}, Y2: {v: number}, index: number): void;
    }
    interface StrengthCalcFunc extends Function {
        prototype: StrengthCalc;
        Decay_Properties: StrengthCalcWriter.StrengthCalc.Decay_PropertiesFunc;
        Tool: StrengthCalc.ToolFunc;
        new (): StrengthCalc;
        ctor: {
            new (): StrengthCalc
        };
        $ctor1: {
            new (pOriginalCircumference: number, pShellRotCirc: number, pExposedPockets: System.Collections.Generic.IEnumerable$1<StrengthCalcWriter.Damage> | null, pMechanicalDamage: System.Collections.Generic.IEnumerable$1<StrengthCalcWriter.Damage> | null, pEnclosedPockets: System.Collections.Generic.IEnumerable$1<StrengthCalcWriter.EnclosedPocket> | null, pTool: number): StrengthCalc
        };
    }
    var StrengthCalc: StrengthCalcFunc;
    module StrengthCalc {
        interface Decay_Properties {
            cDecayArea: number;
            cDecayCG: number;
            cDecayMOIatCG: number;
            cDecayC1: number;
            cDecayC2: number;
            getHashCode(): number;
            equals(o: StrengthCalcWriter.StrengthCalc.Decay_Properties): boolean;
            $clone(to: StrengthCalcWriter.StrengthCalc.Decay_Properties): StrengthCalcWriter.StrengthCalc.Decay_Properties;
        }
        interface Decay_PropertiesFunc extends Function {
            prototype: Decay_Properties;
            $ctor1: {
                new (pDecayAreaReduction: number, pDecayAreaReductionCG: number, pDecayMOIReduction: number, pDecayC1: number, pDecayC2: number): Decay_Properties
            };
            new (): Decay_Properties;
            ctor: {
                new (): Decay_Properties
            };
        }

        interface Tool {
        }
        interface ToolFunc extends Function {
            prototype: Tool;
            Calliper: number;
            Tape: number;
        }
    }

    interface LoadItemEnums {
    }
    interface LoadItemEnumsFunc extends Function {
        prototype: LoadItemEnums;
        ZoneTypeType: LoadItemEnums.ZoneTypeTypeFunc;
        SpanSize: LoadItemEnums.SpanSizeFunc;
        Span: LoadItemEnums.SpanFunc;
        MaterialDesign: LoadItemEnums.MaterialDesignFunc;
        LOCATION_CODES: LoadItemEnums.LOCATION_CODESFunc;
        LC_POLE_SPECIES: LoadItemEnums.LC_POLE_SPECIESFunc;
        LC_POLE_MATERIAL_TYPE: LoadItemEnums.LC_POLE_MATERIAL_TYPEFunc;
        LC_POLE_CLASS: LoadItemEnums.LC_POLE_CLASSFunc;
        ErrorItemType: LoadItemEnums.ErrorItemTypeFunc;
        EquipmentType: LoadItemEnums.EquipmentTypeFunc;
        new (): LoadItemEnums;
        ConvertSpanNetType(pType: number): string | null;
    }
    var LoadItemEnums: LoadItemEnumsFunc;
    module LoadItemEnums {
        interface ZoneTypeType {
        }
        interface ZoneTypeTypeFunc extends Function {
            prototype: ZoneTypeType;
            PwrTop: number;
            PwrLow: number;
            Comm: number;
        }

        interface SpanSize {
        }
        interface SpanSizeFunc extends Function {
            prototype: SpanSize;
            Unset: number;
            XS: number;
            S: number;
            M: number;
            L: number;
            XL: number;
        }

        interface Span {
        }
        interface SpanFunc extends Function {
            prototype: Span;
            Back: number;
            Front: number;
        }

        interface MaterialDesign {
        }
        interface MaterialDesignFunc extends Function {
            prototype: MaterialDesign;
            Wood: number;
            Concrete: number;
            SteelPlus: number;
        }

        interface LOCATION_CODES {
        }
        interface LOCATION_CODESFunc extends Function {
            prototype: LOCATION_CODES;
            LOCATION_CODE_NO: number;
            LOCATION_CODE_ABOVE: number;
            LOCATION_CODE_BELOW: number;
        }

        interface LC_POLE_SPECIES {
        }
        interface LC_POLE_SPECIESFunc extends Function {
            prototype: LC_POLE_SPECIES;
            LC_SPECIES_SP: number;
            LC_SPECIES_DF: number;
            LC_SPECIES_WC: number;
            LC_SPECIES_JP: number;
            LC_SPECIES_LP: number;
            LC_SPECIES_NP: number;
            LC_SPECIES_RP: number;
            LC_SPECIES_WF: number;
            LC_SPECIES_WH: number;
            LC_SPECIES_WL: number;
            LC_SPECIES_WP: number;
            LC_SPECIES_CH: number;
            LC_SPECIES_EC: number;
            LC_SPECIES_CONCRETE: number;
            LC_SPECIES_STEEL: number;
            LC_SPECIES_FIBERGLASS: number;
            LC_SPECIES_ALUMINUM: number;
            LC_SPECIES_TREE: number;
            LC_SPECIES_UNK: number;
        }

        interface LC_POLE_MATERIAL_TYPE {
        }
        interface LC_POLE_MATERIAL_TYPEFunc extends Function {
            prototype: LC_POLE_MATERIAL_TYPE;
            LC_POLE_MATERIAL_WOOD: number;
            LC_POLE_MATERIAL_CONC: number;
            LC_POLE_MATERIAL_STL: number;
            LC_POLE_MATERIAL_FRP: number;
            LC_POLE_MATERIAL_ALUM: number;
            LC_POLE_MATERIAL_UNK: number;
        }

        interface LC_POLE_CLASS {
        }
        interface LC_POLE_CLASSFunc extends Function {
            prototype: LC_POLE_CLASS;
            LC_CLASS_1: number;
            LC_CLASS_2: number;
            LC_CLASS_3: number;
            LC_CLASS_4: number;
            LC_CLASS_5: number;
            LC_CLASS_6: number;
            LC_CLASS_7: number;
            LC_CLASS_8: number;
            LC_CLASS_9: number;
            LC_CLASS_10: number;
            LC_CLASS_H1: number;
            LC_CLASS_H2: number;
            LC_CLASS_H3: number;
            LC_CLASS_H4: number;
            LC_CLASS_H5: number;
            LC_CLASS_H6: number;
        }

        interface ErrorItemType {
        }
        interface ErrorItemTypeFunc extends Function {
            prototype: ErrorItemType;
            FullSpan: number;
            SingleSpan: number;
            Equipment: number;
            NetDrop: number;
            None: number;
        }

        interface EquipmentType {
        }
        interface EquipmentTypeFunc extends Function {
            prototype: EquipmentType;
            StLght: number;
            Xfr: number;
            Misc: number;
        }
    }

    interface Error {
        ID: number;
        ErrorMessage: string | null;
        Type: number;
    }
    interface ErrorFunc extends Function {
        prototype: Error;
        new (): Error;
    }
    var Error: ErrorFunc;

    interface EnclosedPocket extends StrengthCalcWriter.Damage {
        Shell: number;
        Summary: string | null;
    }
    interface EnclosedPocketFunc extends Function {
        prototype: EnclosedPocket;
        new (): EnclosedPocket;
    }
    var EnclosedPocket: EnclosedPocketFunc;
}
