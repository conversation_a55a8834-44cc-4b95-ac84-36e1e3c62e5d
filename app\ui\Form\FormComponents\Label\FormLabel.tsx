import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput, TextInputProps, TextProps } from 'react-native';
import { RouteComponentProps } from 'react-router-native';
import { WrappedFieldInterface } from 'redi-form';

export default class FormLabel extends React.Component<WrappedFieldInterface<void, Props>> {

	render() {
        const props = this.props.componentProps();
		return (
            <View>
                <Text {...props}>
                    {this.props.fieldProps.value as any}
                </Text>
            </View>
		);
	}
}

interface Props extends TextProps{
}

interface State {

}