import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle } from 'react-native';
import styles from './styles';
import textStyles from 'config/styles/text-styles';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class AppButton extends React.PureComponent<Props, never> {
	static defaultProps = {
		theme: 'lightblue',
		height: scaleToDesign(60)
	};

	render() {
		return (
			<TouchableOpacity
				disabled={this.props.disabled}
				onPress={this.props.onPress}
				style={{
					marginVertical: this.props.marginVertical,
					...styles.root,
					...this.getThemeStyle(),
					...this.props.rootStyle,
					height: this.props.height,
					width: this.props.width
				}}>
				{this.props.children}
				{!this.props.children &&
					<Text
						style={{
							...textStyles.large,
							...this.props.textStyle
						}}>
						{this.props.content}
					</Text>
				}
			</TouchableOpacity>
		);
	}

	private getThemeStyle(): ViewStyle {
		switch (this.props.theme) {
			case 'lightblue': return {
				backgroundColor: theme.PRIMARY_LIGHT_BLUE
			};
			case 'blue': return {
				backgroundColor: theme.PRIMARY_MEDIUM_BLUE
			};
			case 'darkblue': return {
				backgroundColor: theme.PRIMARY_DARK_BLUE
			};
			case 'green': return {
				backgroundColor: theme.PRIMARY_GREEN
			};
			case 'lightblue-outline': return {
				borderColor: theme.PRIMARY_LIGHT_BLUE,
				borderWidth: 2
			};
			case 'red': return {
				backgroundColor: '#E9190F'
			};
			case 'none':
			default: return {

			};
		}
	}
}

interface Props {
	disabled?: boolean;
	content?: string;
	rootStyle?: ViewStyle;
	textStyle?: TextStyle;
	width?: number | string;
	marginVertical?: number;
	height: number;
	onPress?(): void;
	theme: 'none' | 'lightblue' | 'blue' | 'darkblue' | 'green' | 'lightblue-outline' | 'red';
}