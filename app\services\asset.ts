import config from 'config/config';
import { http, HttpResult } from 'redi-http';
import { AssetDto } from 'dtos/asset';

class assetService {
	getAsset(clientId: string, name: string): Promise<HttpResult<AssetDto>> {
		const url = `${config.apiUrl}Asset/GetAsset`;
		return http<AssetDto>({ method: 'GET', url, clientId, name });
	}
}
const AssetService = new assetService();
export default AssetService;


