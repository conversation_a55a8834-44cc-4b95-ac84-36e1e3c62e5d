import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import { Time } from 'redi-ui-utils';
import TimePicker, { TimePickerProps } from 'components/TimePicker/TimePicker';

export default class FormTimePicker extends React.PureComponent<WrappedFieldInterface<Time, TimePickerProps>> {
	render() {
		const props = this.props.componentProps();
		return (
			<TimePicker
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error}
				overrideStyles={{
					wrapper: {
						marginTop: 30
					}
				}} />
		);
	}
}