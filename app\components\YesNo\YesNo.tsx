import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import FormComponentError from 'components/Form/FormComponentError';

export default class YesNo extends React.PureComponent<YesNoProps, State> {
	constructor(props: YesNoProps) {
		super(props);

		this.state = {
			modalOpen: false
		};
	}

	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required} />
					<View style={[styles.buttonContainer, this.props.overrideStyles?.buttonContainer]}>
						<TouchableOpacity style={[
							styles.baseButton, styles.yesButton,
							this.props.overrideStyles?.yesButton,
							this.props.value === true ? { ...styles.yesButtonSelected, ...this.props.overrideStyles?.yesButtonSelected } : {}
						]}
							onPress={() => this.onChange(true)}>
							<Text style={[styles.baseButtonText, styles.yesButtonText, this.props.overrideStyles?.yesButtonText]}>
								{this.props.yesButtonText || "Yes"}
							</Text>
						</TouchableOpacity>
						<TouchableOpacity style={[
							styles.baseButton, styles.noButton,
							this.props.overrideStyles?.noButton,
							this.props.value === false ? { ...styles.noButtonSelected, ...this.props.overrideStyles?.noButtonSelected } : {}
						]}
							onPress={() => this.onChange(false)}>
							<Text style={[styles.baseButtonText, styles.noButtonText, this.props.overrideStyles?.noButtonText]}>
								{this.props.noButtonText || "No"}
							</Text>
						</TouchableOpacity>
					</View>
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}
	private onChange(value: boolean): void {
		this.props.onFocus?.(null);
		this.props.onChange(value);
		this.props.onBlur?.(null);
	}
}

export interface YesNoProps extends CommonFormComponentProps<boolean> {
	//optional ovveride for text to display on the yes button
	yesButtonText?: string;
	//optional override for text to display on the no button
	noButtonText?: string;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		//styles that go on the entire component wrapper view
		wrapper?: object;
		//styles that go on the label text component
		label?: object;
		//styles that go on the view that wraps just teh buttons
		buttonContainer?: object;
		//styles that go on the yes button TouchableOpacity
		yesButton?: object;
		//styles that go on the yes button text
		yesButtonText?: object;
		//styles that are applied when the yes button is selected.
		yesButtonSelected?: object;
		//styles that go on the no button TouchableOpacity
		noButton?: object;
		//styles that go on the no button text
		noButtonText?: object;
		//styles that are applied when the no button is selected
		noButtonSelected?: object;
	}
}

interface State {
	modalOpen: boolean;
}
