import { StyleSheet } from 'react-native';
import { FORM_STYLES, FORM_CLASSES, FORM_ERROR_STYLES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        flexDirection: 'column',
        alignItems: 'center',
        marginTop: FORM_STYLES.WRAPPER_MARGIN_TOP,
        marginBottom: FORM_STYLES.WRAPPER_MARGIN_BOTTOM
    },
    content: {
		width: '80%',
		flexDirection: 'column'
    },
    labelWrapper:{
        display: 'flex',
        paddingRight: 10,
        marginBottom: 15,
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: '100%',
    },
    labelText: {
        ...FORM_CLASSES.LABEL_TEXT
    },
    actionButton: {
        ...FORM_CLASSES.BUTTON,
        width: 305,
        borderRadius: 10,
        backgroundColor: '#0054A2',
		flexDirection: 'row',
		justifyContent: 'center',
		alignItems: 'center'
    },
    buttonText: {
        fontSize: 22,
        color: 'white',
        marginLeft: 10
	},
	displayWrapper: {
		justifyContent:"center",
		backgroundColor: 'rgb(0,44,85)',
		borderRadius: 10,
		height: FORM_STYLES.BUTTON_HEIGHT,
		left: -20,
		zIndex: -1,
		paddingLeft: 20,
        paddingRight: 20,
        width: 380,
        flexDirection: 'row',
        alignItems: 'center'
	},
	displayText: {
		fontFamily: FORM_STYLES.LABEL_FONT_FAMILY,
		fontSize: 20,
        color: '#FFFFFF',
        textAlign: 'center',
        paddingRight: 10,
        paddingLeft: 10
	},
    ...FORM_ERROR_STYLES
});

export default styles;
