import React from 'react';
import { View, Text, ViewStyle } from 'react-native';
import styles from './styles';
import textStyles from 'config/styles/text-styles';
import theme from 'config/styles/theme';

export default class PIPIcon extends React.PureComponent<Props, never> {
	render() {
		return (
			<View style={{
				...styles.box,
				...this.props.style,
				backgroundColor: theme.PRIMARY_DARK_BLUE
			}}>
				<Text style={textStyles.medium}>PIP</Text>
			</View>
		);
	}
}

interface Props {
	style?: ViewStyle;
}