#!/usr/bin/env bash

# Creates an .env from ENV variables for use with react-native-config
# Add a variable prefixed with RSS_ in appcenter
echo "PRE_BUILD SCRIPT"
ENV_WHITELIST=${ENV_WHITELIST:-"^RSS"}
printf "Creating an .env file with the following whitelist:\n"
printf "%s\n\n" $ENV_WHITELIST
set | egrep -e $ENV_WHITELIST | egrep -v "^_" | egrep -v "WHITELIST" > .env
printf "\n.env created with contents:\n"
cat .env