import { JobDto, JobStatusCode } from 'dtos/job';
import Database from './database';
import config from 'config/config';
import { http, HttpError } from 'redi-http';
import { showToast } from 'components/Toast/ToastProvider';
import { AppStore } from 'app/stores/app-store';
import { of } from 'rxjs';
import { repeatWhen, flatMap, delay, takeWhile } from 'rxjs/operators';
import { ConnectionStatus } from './heartbeat';
import { teamIdFilter } from 'utils/filter';
import { SyncingData } from 'stores/syncing-data';
import { SyncStore } from 'stores/sync-store';
import { HistoryData } from 'stores/history-data';
import { JobSummaryData } from 'stores/job-summary-data';
import { JobListData } from 'stores/job-list-data';
import { MapData } from 'stores/map-data';
import { AssetDto } from 'dtos/asset';
import uuid4 from 'uuid/v4';
import { AppFormTemplateDto } from 'dtos/formTemplate';
import { FormSubmissionDto } from 'redi-types';
import { polePickId, companyId } from 'app/form-builder/reinforcing-insp';
import AssetActivityService from './assetactivity';

class jobService {
	async getJobsFromServer() {
		const { connectionStatus, user } = AppStore.getValue();

		if (connectionStatus !== ConnectionStatus.Connected || user === null || user === undefined) {
			return;
		}

		const url = `${config.apiUrl}JobSchedule/GetScheduled`;
		try {
			const teamId = AppStore.getValue().teamId;
			let lastModified = AppStore.getValue().lastModified;

			const response = await http<JobDto[]>({
				method: 'GET',
				url,
				teamId,
				lastModified: lastModified?.toISOString()
			}, {
				dateParseOptions: {
					parseDate: false,
					parseTime: false
				}
			});

			if (response.error) {
				console.error(response.error);
			}
			else if (response.data) {
				const data = response.data;

				if (data.length !== 0) {
					const latestChange = new Date(
						Math.max(
							...data.map(x => new Date(x.latestChange).getTime())
						)
					);

					const existingExternalRecIdsNotToUpdate = (await Database.select(`
						SELECT externalRecId FROM jobs
						${teamIdFilter()}
						AND (statusCode = '${JobStatusCode.JInProgress}'
						OR statusCode = '${JobStatusCode.JComplete}')
					`)).map((x: any) => x.externalRecId);

					data.forEach(job => {
						job.hasUploaded = true;
						job.commentsJson = "[]";
					});

					const dataToAddOrUpdate = data.filter(x => !existingExternalRecIdsNotToUpdate.includes(x.externalRecId));

					const insertOrReplace = dataToAddOrUpdate.filter(x => x.deleted == false);
					const remove = dataToAddOrUpdate.filter(x => x.deleted == true);

					await Database.insertOrReplace<JobDto>(
						'jobs',
						[
							'formData',
							'externalRecId',
							'teamId',
							'createdOn',
							'deleted',
							'workOrder',
							'statusCode',
							'workflowCode',
							'assetId',
							'assetName',
							'maintenanceZone',
							'suburb',
							'latitude',
							'longitude',
							'esaInfo',
							'pipCustomer_Info',
							'pipComments',
							'fireRiskZoneClass',
							'decInfo',
							'latestChange',
							'hasUploaded',
							'commentsJson',
							'isLocked'
						],
						insertOrReplace
					);

					await Promise.all(remove.map(async x => {
						await this.deleteJob(x.externalRecId);
					}));
					
					JobService.refreshJobs();

					const userId = AppStore.getValue().user.UserId;
					const sql = `
						UPDATE user
						SET lastModified = ?
						WHERE userId = ?
					`;
					AppStore.update({ lastModified: latestChange });
					await Database.execute(sql, [latestChange, userId]);
				}
			}

		} catch (e) {
			if(!(e.error instanceof HttpError)) {
				showToast('error', e);
			} else {
				if(e.response && e.response.message) {
					showToast('error', e.response.message);
				}
			}
			console.warn(e);
			return e;
		}
	}

	private async deleteJob(externalRecId: string): Promise<void> {
		try {
			await Database.execute(`
				DELETE FROM jobs
				WHERE externalRecId = ?
			`, [externalRecId]);
		} catch (error) {
			console.error(error);
		}
	}

	async getJob(externalRecId: string): Promise<JobDto> {
		const fetchJob = () =>{
			const sql = `
				SELECT * FROM jobs
				WHERE externalRecId = ?
			`;
			var job =  Database
				.select<JobDto>(sql, [externalRecId])
				.then(x => x[0]);
			return job;
		};
		const job = await fetchJob();
		const { connectionStatus } = AppStore.getValue();
		if (connectionStatus === ConnectionStatus.Connected && job.statusCode === JobStatusCode.JComplete) {
			//If a Job is Completed on the App, check to see if the AssetName has updated and the form has been unlocked
			return AssetActivityService.getAssetActivitesFromServer(externalRecId).then(async (data) => {
				if (data && !data.error && data.data) {
					const activity = data.data;

					const sql = `
						UPDATE jobs
						SET assetId = ?, assetName = ?, isLocked = ?, formData = ?
						WHERE externalRecId = ?
					`;
					try {
						const formData = JSON.parse(job.formData) as Partial<FormSubmissionDto>;
						this.updateFormFieldValue(formData, polePickId, activity.enteredAssetName);
						const formDataJson = JSON.stringify(formData);
						await Database.execute(sql, [activity.assetId, activity.enteredAssetName, activity.isLocked, formDataJson, externalRecId]);

					} catch (e) {
						console.warn(e);
					}
				}
				return fetchJob();
			});
		}
		return fetchJob();
	}

	async updateComments(externalRecId: string, commentsJson: string): Promise<any> {
		const sql = `
			UPDATE jobs
			SET commentsJson = ?, hasUploaded = 0
			WHERE externalRecId = ?
		`;
		await Database.execute(sql, [commentsJson, externalRecId]);

		JobService.refreshSync();
	}
	async updateJobStatus(externalRecId: string, status: JobStatusCode): Promise<any> {
		const sql = `
			UPDATE jobs
			SET statusCode = '${status}', modifiedOnUtc = ?
			WHERE externalRecId = ?
		`;
		await Database.execute(sql, [new Date(), externalRecId]);
		JobService.refreshSync();
	}
	async updateFormData(externalRecId: string, data: string): Promise<void> {
		const sql = `
			UPDATE jobs
			SET formData = ?, modifiedOnUtc = ?
			WHERE externalRecId = ?
		`;
		await Database.execute(sql, [data, new Date(), externalRecId]);
		JobService.refreshSync();
	}
	start(): void {
		AppStore.isLoggedIn$.subscribe(isLoggedIn => {
			if (isLoggedIn && !this._started) {
				this._started = true;
				of({})
					.pipe(
						flatMap(() => this.getJobsFromServer()),
						repeatWhen(delay(10 * 1000)),
						takeWhile(() => AppStore.getValue().isLoggedIn)
					).subscribe(
						() => { },
						() => { },
						() => {
							this._started = false;
						}
					);
			}
		});
	}
	refreshSync(): void {
		SyncingData.load();
		SyncStore.refreshCompletedAndInProgress();
		HistoryData.load();
	}
	refreshJobs(): void {
		JobSummaryData.load();
		JobListData.load();
		MapData.load();
	}
	async createNewJob(asset: AssetDto | string, workflowCode: string, maintenanceZone: string, client: string, workOrder?: string): Promise<string> {
		let job: Partial<JobDto> = {
			teamId: +AppStore.getValue().teamId,
			createdOn: new Date(),
			modifiedOnUtc: new Date(),
			deleted: false,
			workOrder,
			workflowCode,
			statusCode: JobStatusCode.JPlanned,
			maintenanceZone,
			externalRecId: uuid4(),
			isLocked: false
		};

		const sql = `
		SELECT *
		FROM formTemplate
		WHERE workflowCode = ?
	`;
		const formTemplate = await Database.select<AppFormTemplateDto>(sql, [workflowCode]).then(x => x[0]);

		if (!formTemplate) {
			throw new Error('No form template found for: ' + workflowCode);
		}

		if (typeof asset === 'string') {
			job.assetName = asset;
		}
		else {
			job = {
				...job,
				...asset
			};
		}

		const jsonTemplate = formTemplate.jsonTemplate;

		let parsed:Partial<FormSubmissionDto> = null;
		try {
			parsed = JSON.parse(jsonTemplate) as Partial<FormSubmissionDto>;
		} catch(err) {
			throw new Error('Form template is corrupt' + jsonTemplate);
		}
		parsed.formId = formTemplate.formTemplateId;
		parsed.formVersionId = formTemplate.version.toString();
		parsed.formSubmissionId = uuid4();

		for (const page of parsed.data.pages) {
			for (const section of page.sections) {
				for (const field of section.fields) {
					if (field.textId === polePickId) {
						field.value = job.assetName;
					}
					else if (field.textId === companyId) {
						field.value = client;
					}
				}
			}
		}

		job.formData = JSON.stringify(parsed);

		try {
			await Database.insert<Partial<JobDto>>(
				'jobs',
				[
					'deleted',
					'teamId',
					'createdOn',
					'workOrder',
					'statusCode',
					'workflowCode',
					'assetId',
					'assetName',
					'maintenanceZone',
					'suburb',
					'latitude',
					'longitude',
					'esaInfo',
					'pipCustomer_Info',
					'pipComments',
					'fireRiskZoneClass',
					'decInfo',
					'latestChange',
					'formData',
					'externalRecId',
					'modifiedOnUtc',
					'isLocked'
				],
				[job]
			);
		} catch (error) {
			console.warn('Database job insert failed.', error);
		}

		return job.externalRecId;
	}

	updateFormFieldValue(parsed: Partial<FormSubmissionDto>, textId: string, value: string) {
		for (const page of parsed.data.pages) {
			for (const section of page.sections) {
				for (const field of section.fields) {
					if (field.textId === textId) {
						field.value = value;
						break;
					}
				}
			}
		}
	}

	private _started = false;
}

const JobService = new jobService();
export default JobService;


