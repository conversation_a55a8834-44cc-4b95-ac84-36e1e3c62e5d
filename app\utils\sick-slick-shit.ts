/* eslint-disable complexity */
import { FormLogicCompiled, CompiledAction, CompileActionValue } from 'redi-types';

const rootScope = {
    math: Math,
    $now: Date.now
};

class ExitError extends Error { }

export class ScriptRuntime {
    private stack: Array<any>;
    private wkVariable: any;

    constructor() {
        this.stack = [{ ...rootScope }];
    }

    addToRootScope(extraScope: object) {
        this.stack[0] = { ...this.stack[0], ...extraScope };
    }

    run(logic: FormLogicCompiled) {
        let rootAction: CompiledAction = logic.logic;
        try {
            this.runAction(rootAction);
        } catch (e) {
            if (!(e instanceof ExitError)) {
                throw e;
            }
        }
    }

    private get topScope() {
        return this.stack[this.stack.length - 1];
    };

    private runAction<T = any>(action: CompiledAction, evaluatePointer: boolean = false): T {
        switch (action.type) {
            case 'var_assign': {
                let pointer: string = this.runAction<string>(action.subActions[action.value.value]);
                if (action.arguments.length != 1) {
                    throw new Error('Arguments array for type var_assign must contain precisely one value. Dumbass.');
                }
                let value: any;
                let actionValue: CompileActionValue = action.arguments[0];
                switch (actionValue.type) {
                    case 'bool':
                    case 'number':
                    case 'string':
                    case 'other':
                        value = actionValue.value;
                        break;
                    case 'subaction':
                        value = this.runAction(action.subActions[action.arguments[0].value]);
                        break;
                    default:
                        throw new Error(`Invalid action value type of ${actionValue.type} for var_assign run action`);
                }

                this.topScope[pointer] = value;
                break;
            }
            case 'var_ref': {
                let pointer;
                if (action.value.type === 'accessor_name') {
                    pointer = action.value.value;
                } else if (action.value.type === 'subaction') {
                    pointer = this.runAction(action.subActions[action.value.value]);
                } else {
                    throw new Error(`No supporty ${action.value.type} for var_ref`);
                }

                let index = this.stack.length - 1;
                for (let scope = this.stack[index]; !!scope; scope = this.stack[--index]) {
                    if (pointer in scope) {
                        this.wkVariable = scope[pointer];
                        break;
                    }
                }
                if (action.next) {
                    return this.runAction(action.next, evaluatePointer);
                } else {
                    return evaluatePointer ? this.wkVariable : pointer;
                }
            }
            case 'var_accessor': {
                let pointer;
                if (action.value.type == 'accessor_name') {
                    pointer = action.value.value;
                } else if (action.value.type == 'subaction') {
                    pointer = this.runAction(action.subActions[action.value.value]);
                } else {
                    throw new Error(`No supporty ${action.value.type} for var_ref`);
                }
                this.wkVariable = this.wkVariable?.[pointer];
                if (action.next) {
                    return this.runAction(action.next, evaluatePointer);
                } else {
                    return evaluatePointer ? this.wkVariable : pointer;
                }
            }
            case 'function': {
                if (typeof this.wkVariable !== 'function') {
                    throw new Error(`${this.wkVariable} is not a function.`);
                }
                let args: Array<any> = action.arguments?.map(arg => {
                    switch (arg.type) {
                        case 'subaction': {
                            return this.runAction(action.subActions[arg.value], true);
                        }
                        case 'number':
                        case 'string':
                        case 'bool':
                        case 'other':
                            return arg.value;
                        case 'accessor_name': {
                            throw new Error('Arg type accessor_name not supported for function actions.');
                        }
                    }
                });
                return (this.wkVariable as Function).apply(null, args);
            }
            case 'if': {
                if (this.runAction(action.subActions[0])) {
                    return this.runAction(action.conditionalNext);
                } else if (action.next) {
                    return this.runAction(action.next);
                }
                break;
            }
            case 'exit': {
                throw new ExitError();
            }
            case 'operator': {
                let args: Array<any> = action.arguments?.map(arg => {
                    switch (arg.type) {
                        case 'subaction': {
                            return this.runAction(action.subActions[arg.value], true);
                        }
                        case 'number':
                        case 'string':
                        case 'bool':
                        case 'other':
                            return arg.value;
                        case 'accessor_name': {
                            throw new Error('Arg type accessor_name not supported for function actions.');
                        }
                    }
                });
                let result: any;
                switch (action.typeOptions.operator) {
                    case '!':
                        result = !args[0];
                        break;
                    case '!=':
                        result = args[1] != args[0];
                        break;
                    case '&&':
                        result = args[1] && args[0];
                        break;
                    case '*':
                        result = args[1] * args[0];
                        break;
                    case '+':
                        result = args[1] + args[0];
                        break;
                    case '-':
                        result = args[1] - args[0];
                        break;
                    case '/':
                        result = args[1] / args[0];
                        break;
                    case '<':
                        result = args[1] < args[0];
                        break;
                    case '<=':
                        result = args[1] <= args[0];
                        break;
                    case '==':
                        result = args[1] == args[0];
                        break;
                    case '>':
                        result = args[1] > args[0];
                        break;
                    case '>=':
                        result = args[1] >= args[0];
                        break;
                    case '||':
                        result = args[1] || args[0];
                        break;
                    case '%':
                        result = args[1] % args[0];
                        break;
                }
                return result;
            }
            case 'begin_scope': {
                this.stack.push({});
                if (action.next) {
                    return this.runAction(action.next);
                }
                break;
            }
            case 'end_scope': {
                this.stack.pop();
                if (action.next) {
                    return this.runAction(action.next);
                }
                break;
            }
            case 'action_group': {
                return this.runAction(action.subActions[0]);
            }
            default:
                throw new Error(`unexpecte action type ${action.type}`);
        }

        return null;
    }
}