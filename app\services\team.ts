import { TeamDto } from "dtos/team";
import { AppStore } from "stores/app-store";
import { ConnectionStatus } from "./heartbeat";
import config from "config/config";
import { http } from "redi-http";
import Database from "./database";

class teamService {
	getTeams(): Promise<TeamDto[]> {
		return Database.select<TeamDto>('SELECT * FROM teams');
	}
	async saveTeamsFromServer(): Promise<void> {
		if (AppStore.getValue().connectionStatus !== ConnectionStatus.Connected) {
			return;
		}

		const url = `${config.apiUrl}Team/GetTeams`;
		try {
			const response = await http<TeamDto[]>({ method: 'GET', url });
			if (response.data) {
				await Database.insertOrReplace<TeamDto>(
					'teams',
					['teamId', 'name', 'longName', 'sortOrder'],
					response.data
				);
			}
		} catch (error) {
			console.error(error);
		}
	}
}
const TeamService = new teamService();
export default TeamService;