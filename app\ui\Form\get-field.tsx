import { FormSub<PERSON>Field } from "redi-types";
import { TypedPath, StandardValidators, Field } from "redi-form";
import { View, Text, NativeSyntheticEvent } from "react-native";
import React from "react";
import { FORM_CLASSES } from "config/styles/form-component-common";
import FormText from "./FormComponents/Text/FormText";
import FormTextMultiLine from "./FormComponents/TextMultiLine/FormTextMultiLine";
import FormDropdown from "./FormComponents/Dropdown/FormDropdown";
import FormDropdownMultiChoice from "./FormComponents/DropdownMultiChoice/FormDropdownMultiChoice";
import FormPhoto from "./FormComponents/Photo/FormPhoto";
import FormRadio from "./FormComponents/Radio/FormRadio";
import FormCheckbox from "./FormComponents/Checkbox/FormCheckbox";
import FormCheckboxMultiChoice, { FormCheckboxMultiChoiceProps } from "./FormComponents/CheckboxMultiChoice/FormCheckboxMultiChoice";
import FormNumber from "./FormComponents/Number/FormNumber";
import FormNumberNeg from "./FormComponents/NumberNeg/FormNumberNeg";
import FormGPS from "./FormComponents/GPS/FormGPS";
import { GPSCoordinatesDto, GPSProps } from "components/GPS/GPS";
import FormDatePicker from "./FormComponents/DatePicker/FormDatePicker";
import FormTimePicker from "./FormComponents/TimePicker/FormTimePicker";
import FormDraw, { FormDrawProps } from "./FormComponents/Draw/FormDraw";
import FormButtonList, { FormButtonListProps } from "./FormComponents/ButtonList/FormButtonList";
import FormButtonListMulti, { FormButtonListMultiProps } from "./FormComponents/ButtonListMulti/FormButtonListMulti";
import FormYesNo from "./FormComponents/YesNo/FormYesNo";
import FormSignature, { FormSignatureProps } from "./FormComponents/Signature/FormSignature";
import { PhotoValueDto } from "components/Photo/Photo";
import FormYesNoNa, { FormYesNoNAProps } from './FormComponents/YesNoNA/FormYesNoNA';
import { DropdownProps } from "components/Dropdown/Dropdown";
import { DropdownMultiChoiceProps } from "components/Dropdown/DropdownMultiChoice";
import { YesNoProps } from "components/YesNo/YesNo";
import FormCheckboxLarge from "./FormComponents/CheckboxLarge/FormCheckboxLarge";
import FormYesNoString from "./FormComponents/YesNoString/FormYesNoString";

export function getFieldComponent(
	formValue: FormSubmissionField,
	fieldPath: TypedPath<FormSubmissionField>,
	onFormChange: (textId: string) => void,
	onFormBlur: (textId: string) => void,
	onFormFocus: (e: React.FocusEvent) => void,
	onAddPhoto: (photo: PhotoValueDto, textId: string) => void,
	onRemovePhoto: (photo: PhotoValueDto) => void
): React.ReactNode {
	const required = formValue.options?.required;
	const label = formValue.label;
	const placeholder = formValue.options?.placeholder;
	const options = formValue.options?.choices;
	const commonProps = { required, label, placeholder, options };
	const commonValidators = required ? StandardValidators.required : null;
	const callbacks = {
		onChange: () => onFormChange(formValue.textId),
		onFocus: (e:React.FocusEvent) => onFormFocus(e),
		onBlur: () => onFormBlur(formValue.textId)
	};

	switch (formValue.fieldType) {
		case 'Label':
			return (
				<View style={{
					paddingVertical: 15,
					alignItems: 'center'
				}}>
					<View style={{ width: '80%' }}>
						<Text style={FORM_CLASSES.LABEL_TEXT}>{formValue.label}</Text>
						<Text style={FORM_CLASSES.LABEL_TEXT}>{formValue.value}</Text>
					</View>
				</View>
			);
		case 'Text':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormText}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'TextMultiLine':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormTextMultiLine}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'TypeaheadText':
			return null;
		case 'Dropdown':
			return (
				<Field<DropdownProps<string>, string>
					path={(fieldPath).value.path()}
					component={FormDropdown}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'DropdownMultiChoice':
			return (
				<Field<Omit<DropdownMultiChoiceProps<string>, 'selected'>, string[]>
					path={(fieldPath).value.path()}
					component={FormDropdownMultiChoice}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						values: formValue.options.choices
					}}
					{...callbacks} />
			);
		case 'Photo':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormPhoto}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						onAdd: photo => onAddPhoto(photo, formValue.textId),
						onRemove: photo => onRemovePhoto(photo),
						maxHeight: formValue.options?.photoMaxHeight,
						maxWidth: formValue.options?.photoMaxWidth,
						maxPhotos: formValue.options?.maxPhotos ?? 6
					}}
					{...callbacks} />
			);
		case 'Radio':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormRadio}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						values: formValue.options.choices,
						selected: formValue.value,
						valuesPerRow: 3
					}}
					{...callbacks} />
			);
		case 'Checkbox':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormCheckbox}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						selected: !!formValue.value
					}}
					{...callbacks} />
			);
		case 'CheckboxLarge':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormCheckboxLarge}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						selected: !!formValue.value
					}}
					{...callbacks} />
			);
		case 'CheckboxMultiChoice':
			return (
				<Field<FormCheckboxMultiChoiceProps<string>, string[]>
					path={(fieldPath).value.path()}
					component={FormCheckboxMultiChoice}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						values: formValue.options.choices,
					}}
					{...callbacks} />
			);
		case 'Number':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormNumber}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'NumberNeg':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormNumberNeg}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'GPS':
			return (
				<Field<GPSProps, GPSCoordinatesDto>
					path={(fieldPath).value.path()}
					component={FormGPS}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'Date':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormDatePicker}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'DateTime':
			return null;
		case 'Time':
			return (
				<Field
					path={(fieldPath).value.path()}
					component={FormTimePicker}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'Draw':
			return (
				<Field<FormDrawProps, string>
					path={(fieldPath).value.path()}
					component={FormDraw}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'Barcode':
			return null;
		case 'ButtonList':
			return (
				<Field<FormButtonListProps, string>
					path={(fieldPath).value.path()}
					component={FormButtonList}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						values: options
					}}
					{...callbacks} />
			);
		case 'WebLink':
			return null;
		case 'ButtonListMulti':
			return (
				<Field<FormButtonListMultiProps, string[]>
					path={(fieldPath).value.path()}
					component={FormButtonListMulti}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						values: options
					}}
					{...callbacks} />
			);
		case 'YesNo':
			return (
				<Field<YesNoProps, boolean>
					path={(fieldPath).value.path()}
					component={FormYesNo}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'YesNoString':
			return (
				<Field<YesNoProps, string>
					path={(fieldPath).value.path()}
					component={FormYesNoString}
					validator={commonValidators}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		case 'YesNoNA':
			return (
				<Field<FormYesNoNAProps, boolean>
					path={(fieldPath).value.path()}
					component={FormYesNoNa}
					validator={commonValidators}
					componentProps={{
						...commonProps,
						selected: formValue.value
					}}
					{...callbacks} />
			);
		case 'MarkPicture':
			return null;
		case 'Signature':
			return (
				<Field<FormSignatureProps, string>
					path={(fieldPath).value.path()}
					validator={commonValidators}
					component={FormSignature}
					componentProps={{ ...commonProps }}
					{...callbacks} />
			);
		default:
			throw new Error('unkown field type');
	}
}