import React from 'react';
import { View, Text, ScrollView, NativeSyntheticEvent } from 'react-native';
import styles from './styles';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import ProgressBar from 'components/ProgressBar/ProgressBar';
import { FormSubmissionDto, FormSubmissionField, FormSubmissionPage, FormSubmissionSection, ReactComponent } from 'redi-types';
import { formFactory, WrappedForm, FieldArray, FormValueArray } from 'redi-form';
import { TypedPath, DateTime, string, number } from 'redi-ui-utils';
import FileService from 'services/file';
import SideMenu from './SideMenu/SideMenu';
import { FormStore } from 'stores/form-store';
import BackButton from 'project_components/BackButton/BackButton';
import { scaleToDesign } from 'utils/design-scale';
import { PhotoValueDto } from 'components/Photo/Photo';
import { polePickId } from 'app/form-builder/reinforcing-insp';
import { setVisibility } from './visibilty';
import { getFieldComponent } from './get-field';
import { AppStore } from 'stores/app-store';
import FormSubmit from './FormSubmit/FormSubmit';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

export interface MenuPageDto {
	label: string;
	ref: React.RefObject<View>;
	menuSections: Array<MenuSectionDto>;
	id: string;
}

export interface MenuSectionDto {
	label: string;
	ref: React.RefObject<View>;
	menuFields: Array<MenuFieldDto>;
	id: string;
}

export interface MenuFieldDto {
	label: string;
	ref: React.RefObject<View>;
}

@formFactory<FormSubmissionDto>({ enableReinitialize: true, debugMode: false })
class FormContent extends React.PureComponent<Props, State> {
	scroll: JSX.Element;
	changedField: string;
	constructor(props: Props) {
		super(props);
		this.state = {
			percent: 0,
			title: '',
			itemName: '',
			menuPages: [],
			assetFieldId: null,
			dataHasLoaded: false,
			visiblityHasBeenSet: false,
		};

		this.renderPages = this.renderPages.bind(this);
		this.renderSections = this.renderSections.bind(this);
		this.renderFields = this.renderFields.bind(this);
		this.onAddPhoto = this.onAddPhoto.bind(this);
		this.onRemovePhoto = this.onRemovePhoto.bind(this);
		this.onFormChange = this.onFormChange.bind(this);
		this.onFormBlur = this.onFormBlur.bind(this);
		this.onFormFocus = this.onFormFocus.bind(this);
		this.getAssetFieldId = this.getAssetFieldId.bind(this);
		this.getValueForField = this.getValueForField.bind(this);

		this.changedField = null;
	}

	componentDidMount() {
		const x = (<FieldArray path={this.props.paths.data.pages}>
			{p => this.renderPages(p)}
		</FieldArray>);
	}

	async componentDidUpdate(prevProps: PublicProps) {
		if (!this.state.title && this.props.initialValues?.data?.pages?.length) {
			let title: string = this.props.initialValues.data.pages[0].label;
			this.setState({ title: title });
		}

		if (prevProps.initialValues !== this.props.initialValues) {
			setTimeout(() => {
				const val = this.getValueForField(this.getAssetFieldId());
				this.setState({ itemName: val });
				setVisibility(this.props.getFormValue<FormSubmissionDto>(''));
				this.setSectionComplete();
				this.calculateProgress();
				this.setState({visiblityHasBeenSet: true});
			});
		}
	}
	render() {
		if(this.state.visiblityHasBeenSet == false) {
			return (
				<View style={{flexDirection:'column', height: '100%', justifyContent:'center', alignItems:'center'}}>
					{
						this.state.title != undefined &&
						this.state.title != null &&
						this.state.title != "" && 
						<Text>{this.state.title} is loading...</Text>
					}
					{
						(this.state.title == undefined ||
							this.state.title != null ||
							this.state.title != "") &&
						<Text>Loading...</Text> 
					}
				</View>
			);
		}
		return (
			<View>
				<View style={styles.header}>
					<View style={styles.section}>
						<BackButton />
						<Text style={styles.page}>1</Text>
					</View>
					<View style={styles.progressSection}>
						<Text style={styles.title}>{this.state.title}</Text>
						<ProgressBar backgroundColour={theme.PRIMARY_MEDIUM_BLUE} percent={this.state.percent} />
					</View>
					<View style={styles.section}>
						<Text style={textStyles.medium}>{this.state.itemName}</Text>
						<Text style={textStyles.medium}>{this.state.percent.toFixed(1)}%</Text>
					</View>
				</View>
				<KeyboardAwareScrollView ref={this._scrollViewRef}>
					<FieldArray path={this.props.paths.data.pages}>
						{p => this.renderPages(p)}
					</FieldArray>
					{this.state.percent === 100 &&
						<FormSubmit
							externalRecId={this.props.externalRecId}
							handleSubmit={this.props.handleSubmit} />
					}
					<View style={{ height: scaleToDesign(500) }} />
				</KeyboardAwareScrollView>
				<SideMenu scrollViewRef={this._scrollViewRef} />
			</View >
		);
	}

	private _scrollViewRef = React.createRef<KeyboardAwareScrollView>();
	private _menuPages = new Array<MenuPageDto>();
	private _fieldIds: Array<string> = [];
	private _firstLoadLogicDone: boolean = false;

	private getValueForField(textId: string): any {
		const form = this.props.getFormValue<FormSubmissionDto>('');
		if (form.data?.pages?.length > 0) {
			for (const page of form.data.pages) {
				for (const section of page.sections) {
					for (const field of section.fields) {
						if (field.textId === textId) {
							return (field as any).value;
						}
					}
				}
			}
		}
		return null;
	}
	private getFieldFromTextId(textId: string): any {
		const form = this.props.getFormValue<FormSubmissionDto>('');
		if (form.data?.pages?.length > 0) {
			for (const page of form.data.pages) {
				for (const section of page.sections) {
					for (const field of section.fields) {
						if (field.textId === textId) {
							return (field as any);
						}
					}
				}
			}
		}
		return null;
	}
	private getAssetFieldId(): string {
		return polePickId;
	}
	private onFormChange(textId: string) {
		console.warn('CHANGE', textId);
		this.changedField = textId;
	}
	private onFormBlur(textId: string): void {
		setTimeout(() => {
			console.warn('BLUR', textId);
			if (textId === this.getAssetFieldId()) {
				const val = this.getValueForField(this.getAssetFieldId());
				this.setState({ itemName: val });
			}
			setVisibility(this.props.getFormValue<FormSubmissionDto>(''));
			if (this.setSectionComplete()) {
				setVisibility(this.props.getFormValue<FormSubmissionDto>(''));
			}
			this.calculateProgress();
			this.props.handleSubmit((data) => {
				if (!data.startDate) {
					data.startDate = DateTime.toUTC(new Date());
				}
				if (data.completedDate) {
					data.completedDate = null;
				}
				data.completedByName = AppStore.getValue().user.Username;
				FileService.saveFormData(this.props.externalRecId, data);
			}, null, true);
			
			this.setState({visiblityHasBeenSet: true});
		});
	}
	private onFormFocus(e: React.FocusEvent): void {
		if(e && e.target && e.target != null) {
			if(this._scrollViewRef && this._scrollViewRef.current) {
				this._scrollViewRef.current.scrollToFocusedInput(e.target, 200);
			}
		}
	}
	private calculateProgress(): void {
		const form = this.props.getFormValue<FormSubmissionDto>('');
		let fields = 0;
		let completed = 0;
		if (form.data?.pages?.length > 0) {
			for (const page of form.data.pages) {
				for (const section of page.sections.filter(x => !x.isHidden)) {
					for (const field of section.fields.filter(x => !x.isHidden && x.options?.required)) {
						fields++;
						if (field.value !== '' && field.value !== null && field.value !== undefined) {
							completed++;
						}
					}
				}
			}
		}
		if (fields === 0) {
			this.setState({ percent: 0 });
		}
		else {
			this.setState({ percent: completed / fields * 100 });
		}
	}
	private setSectionComplete(): boolean {
		const form = this.props.getFormValue<FormSubmissionDto>('');
		if (form.data?.pages?.length < 1) {
			return false;
		}
		let hasChanged = false;
		for (const page of form.data.pages) {
			for (const section of page.sections) {
				let wasComplete = section.isComplete;
				if (section.isHidden) {
					section.isComplete = undefined;
				}
				else {
					section.isComplete = section.fields
						.filter(x => !x.isHidden && x.options?.required)
						.every(x =>
							x.value !== undefined && x.value !== null && x.value !== ''
						);
				}
				if (wasComplete !== section.isComplete) {
					hasChanged = true;
				}
			}
		}
		return hasChanged;
	}
	private onAddPhoto(photo: PhotoValueDto, textId: string) {
		FileService.saveFile({
			fileId: photo.fileName,
			fieldId: textId,
			filePath: photo.filePath,
			contentPath: photo.contentPath,
			externalRecId: this.props.externalRecId,
			hasUploaded: false,
			isUploading: false
		});
	}
	private onRemovePhoto(photo: PhotoValueDto) {
		FileService.removeFile(photo.filePath);
	}
	private renderPages(fields: FormValueArray<FormSubmissionPage>): React.ReactNode {
		this._menuPages = new Array<MenuPageDto>();

		const pageComponents = fields.map((path, index, key) => {
			const page = this.props.getFormValue(path);
			const pageRef = React.createRef<View>();
			this._menuPages.push({
				label: page.label,
				ref: pageRef,
				id: page.textId,
				menuSections: []
			});

			return (
				<View ref={pageRef} key={page.textId + index + key}>
					<FieldArray path={path.sections}>
						{s => this.renderSections(s)}
					</FieldArray>
				</View>
			);
		});

		FormStore.update({ menuPages: this._menuPages });

		if (!this._firstLoadLogicDone) {
			this._firstLoadLogicDone = true;
			for (const fieldId of this._fieldIds) {
				this.onFormChange(fieldId);
			}
		}

		return pageComponents;
	}
	private renderSections(fields: FormValueArray<FormSubmissionSection>): React.ReactNode {
		return fields.map((sectionPath, sectionIndex, sectionKey) => {
			const section = this.props.getFormValue(sectionPath);
			const sectionRef = React.createRef<View>();
			if (!section.isHidden) {
				this.addSectionToPage(section, sectionRef);
			}
			if (section.isHidden) {
				return <View key={section.textId + sectionIndex + sectionKey} />;
			}
			return (
				<View ref={sectionRef} key={section.textId + sectionIndex + sectionKey} style={{
					marginTop: 20
				}}>
					<View style={{ alignItems: 'center', paddingVertical: 15 }}>
						<Text style={{
							color: '#323232',
							fontSize: 24,
							letterSpacing: 1.5,
							fontFamily: 'Montserrat-Bold'
						}}>{section.label}</Text>
					</View>
					<FieldArray path={sectionPath.fields}>
						{f => this.renderFields(f as FormValueArray<FormSubmissionField>)}
					</FieldArray>
				</View>
			);
		});
	}
	private getField(formValue: FormSubmissionField, fieldPath: TypedPath<FormSubmissionField>): React.ReactNode {
		if (!this._firstLoadLogicDone) {
			this._fieldIds.push(formValue.textId);
		}
		return getFieldComponent(
			formValue,
			fieldPath,
			this.onFormChange,
			this.onFormBlur,
			this.onFormFocus,
			this.onAddPhoto,
			this.onRemovePhoto
		)
	}
	private renderFields(fields: FormValueArray<FormSubmissionField>): React.ReactNode {
		return fields.map((fieldPath, fieldindex, fieldKey) => {
			const formValue = this.props.getFormValue(fieldPath);
			const fieldRef = React.createRef<View>();
			if (!formValue.isHidden) {
				this.addFieldToMenu(formValue, fieldRef);
			}
			if (formValue.isHidden) {
				return <View key={formValue.textId + fieldindex + fieldKey} />;
			}

			// IF is custom type, run custom logic
			if (formValue.options?.custom)
				this.runCustomFunctionSwitch(formValue);

			return (
				<View ref={fieldRef} key={formValue.textId + fieldindex + fieldKey}>
					{this.getField(formValue, fieldPath)}
				</View>
			);
		});
	}
	private addSectionToPage(section: FormSubmissionSection, ref: React.RefObject<View>): void {
		const pageSearch = this.props.initialValues.data.pages
			.find(x => x.sections.some(y => y.textId === section.textId));

		const menuPageSearch = this._menuPages.find(x => x.id === pageSearch.textId);
		menuPageSearch.menuSections.push({
			label: section.label,
			ref,
			menuFields: [],
			id: section.textId
		});
	}
	private addFieldToMenu(field: FormSubmissionField, ref: React.RefObject<View>): void {
		try {
            const sections = this.props.initialValues.data.pages
                .reduce((pv, cv) => pv.concat(cv.sections), new Array<FormSubmissionSection>());
            const sectionSearch = sections.find(x => x.fields.some(y => y.textId === field.textId));

            const menuSections = this._menuPages
                .reduce((pv, cv) => pv.concat(cv.menuSections), new Array<MenuSectionDto>());
            const menuSectionSearch = menuSections.find(x => x.id === sectionSearch.textId);

            menuSectionSearch.menuFields.push({
                label: field.label || field.options?.placeholder,
                ref
            });
        } catch (e) {
            console.warn('Caught error: ', e);
        }
	}


	// - Custom Functions - //

	private runCustomFunctionSwitch(field: FormSubmissionField) {
		
		switch(field.options?.custom?.type) {

			// Form: SSInsp2
			case 'CircularSideLength':
				var diameter = this.getFieldFromTextId(field.options.custom.textId).value;
				var n = 0;
				if (diameter <= 273) n = 4;
				else if (diameter <= 508) n = 8;
				else if (diameter <= 762) n = 12;
				else n = 16;
				if (diameter)
					field.label = "Side Length = " + (diameter * 3.14159 / n).toFixed(2);
				else
					field.label = "Side Length = (diameter is empty)";
				break;

			// Form: SSInsp2
			case 'TMemberFlange':
				var restriction = field.options.custom.restriction;
				if (field.value && field.value == restriction)
					field.value = this.getFieldFromTextId(field.options.custom.textId).value;
				break;

			// Form: SSInsp2
			case 'TMemberPtAvg':
				var pt1 = Number(this.getFieldFromTextId(field.options.custom.textIdPt1).value);
				var pt2 = Number(this.getFieldFromTextId(field.options.custom.textIdPt2).value);
				var pt3 = Number(this.getFieldFromTextId(field.options.custom.textIdPt3).value);
				var pt4 = Number(this.getFieldFromTextId(field.options.custom.textIdPt4).value);
				var finalAvg = (pt1 + pt2 + pt3 + pt4) / 4;
				if (pt1 && pt2 && pt3 && pt4) 
					field.label = "Average pt = " + finalAvg.toFixed(2) + "mm";
				else
					field.label = "Average pt = (enter all pt's)";
				break;

			// Form: SSInsp2
			case 'SectionLoss':
				// IF this change isn't the user entering the field manually
				if (field.textId != this.changedField) {
					var numerator = Number(this.getFieldFromTextId(field.options.custom.numerator).value);
					var denom = Number(this.getFieldFromTextId(field.options.custom.denom).value);
					var sectionLoss = ( 1 - (numerator/denom) ) * 100;
					if (numerator && denom)
						field.value = sectionLoss.toFixed(2).toString();
					else
						field.value = null;
				}
				break;

			// Form: SSInsp2
			case 'TMemberSectionLoss':
				// IF this change isn't the user entering the field manually
				if (field.textId != this.changedField) {
					var pt1 = Number(this.getFieldFromTextId(field.options.custom.textIdPt1).value);
					var pt2 = Number(this.getFieldFromTextId(field.options.custom.textIdPt2).value);
					var pt3 = Number(this.getFieldFromTextId(field.options.custom.textIdPt3).value);
					var pt4 = Number(this.getFieldFromTextId(field.options.custom.textIdPt4).value);
					var avPt = (pt1 + pt2 + pt3 + pt4) / 4;
					var origThk = Number(this.getFieldFromTextId(field.options.custom.textIdOrigThk).value);
					var sectionLoss = ( 1 - (avPt/origThk) ) * 100;
					if (pt1 && pt2 && pt3 && pt4 && origThk)
						field.value = sectionLoss.toFixed(2).toString();
					else
						field.value = null;
				}
				break;

			// Form: SSInsp2
			case 'AnchorBoltsRodSectionLoss':
				// IF this change isn't the user entering the field manually
				if (field.textId != this.changedField) {
					var origDiam = Number(this.getFieldFromTextId(field.options.custom.textIdOrigDiam).value);
					var thinnest = Number(this.getFieldFromTextId(field.options.custom.textIdThinnest).value);
					var sectionLoss = ((origDiam - thinnest)/origDiam) * 100;

					if (origDiam && thinnest)
						field.value = sectionLoss.toFixed(2).toString();
					else
						field.value = null;
				}
				break;

			// Form: SSInsp2
			case 'PoleSteelAboveGroundSectionLoss':
				// IF this change isn't the user entering the field manually
				if (field.textId != this.changedField) {
					var thick1 = Number(this.getFieldFromTextId(field.options.custom.textIdThick1).value);
					var thick2 = Number(this.getFieldFromTextId(field.options.custom.textIdThick2).value);
					var thick3 = Number(this.getFieldFromTextId(field.options.custom.textIdThick3).value);
					var origThick = Number(this.getFieldFromTextId(field.options.custom.origThick).value);
					var thickAv = (thick1 + thick2 + thick3) / 3;
					var sectionLoss = ( 1 - (thickAv/origThick) ) * 100;
					if (thick1 && thick2 && thick3 && origThick)
						field.value = sectionLoss.toFixed(2).toString();
					else
						field.value = null;
				}
				break;

			// Form: SSInsp2
			case 'FlatThicknessAvg':
				// IF this change isn't the user entering the field manually
				if (field.textId != this.changedField) {
					var thick1 = Number(this.getFieldFromTextId(field.options.custom.textIdThick1).value);
					var thick2 = Number(this.getFieldFromTextId(field.options.custom.textIdThick2).value);
					var thick3 = Number(this.getFieldFromTextId(field.options.custom.textIdThick3).value);
					var finalAvg = (thick1 + thick2 + thick3) / 3;
					if (thick1 && thick2 && thick3) 
						field.label = "Average Thickness = " + finalAvg.toFixed(2) + "mm";
					else
						field.label = "Average Thickness = (enter all thickness values)";
				}
				break;

			default:
				break;
		}
	}

}

interface Props extends WrappedForm<FormSubmissionDto>, PublicProps { }

interface PublicProps {
	initialValues: Partial<FormSubmissionDto>;
	externalRecId: string;
	workflowCode: string;
	/** Called when a value changes. This is called after the state has changed and the render complete */
	onChange?<R>(path: string, pathValue?: R, newFormValue?: FormSubmissionDto, prevFormValue?: FormSubmissionDto): void;
}

export default FormContent as ReactComponent<PublicProps>;
export type FormContentType = FormContent;

interface State {
	percent: number;
	title: string;
	itemName: string;
	menuPages: Array<MenuPageDto>;
	assetFieldId?: string;
	dataHasLoaded: boolean;
	visiblityHasBeenSet: boolean;
}
