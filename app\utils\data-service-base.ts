import { Store, Query, createStore, createQuery, filterNil } from '@datorama/akita';

export abstract class DataServiceBase<TData, TArgs> {
	constructor(name: string, init: Partial<State<TData, TArgs>> = { args: {} as TArgs, isVisible: false }) {
		this.store = createStore<State<TData, TArgs>>(init, { name, resettable: true });
		this.query = createQuery(this.store);
	}

	protected readonly store: Store<State<TData, TArgs>>;
	protected readonly query: Query<State<TData, TArgs>>;

	get data$() { return this.query.select(x => x.data).pipe(filterNil); }
	reset = () => this.store.reset();
	protected abstract _load(): Promise<void>;

	setArgs(args: TArgs): void {
		this.store.update({ args });
		this.load();
	}
	setPartialArgs(args: Partial<TArgs>): void {
		this.store.update(prev => ({
			args: {
				...prev.args,
				...args
			}
		}));
		this.load();
	}
	setVisible() {
		this.store.update({ isVisible: true });
	}
	load(): Promise<void> {
		if (this.store.getValue().isVisible) {
			return this._load();
		}
		return Promise.resolve();
	}
}

type State<TData, TArgs> = {
	data: TData;
	args: TArgs;
	isVisible: boolean;
};
