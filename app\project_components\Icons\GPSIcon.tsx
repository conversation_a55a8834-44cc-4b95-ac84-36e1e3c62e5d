import React from 'react';
import Svg, { G, Path, Polyline, Circle, Rect, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class GPSIcon extends React.PureComponent<Props, never> {
    static defaultProps = {
        colour: theme.PRIMARY_LIGHT_BLUE
    };

    render() {
        let width: string = scaleToDesign(30) + "px";
		let height: string = scaleToDesign(30) + "px";
        return (
            <Svg width={width} height={height} viewBox="0 0 35 35">
            <G id="Artboard" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                <Circle id="Oval-2" stroke={this.props.colour}  strokeWidth="2" cx="17.5" cy="17.5" r="12.5"></Circle>
                <Circle id="Oval" stroke={this.props.colour}  strokeWidth="2" cx="17.5" cy="17.5" r="5"></Circle>
                <Line x1="17.5" y1="5" x2="17.5" y2="1" id="Line" stroke={this.props.colour}  strokeWidth="2" strokeLinecap="round"></Line>
                <Line x1="32" y1="19.5" x2="32" y2="15.5" id="Line" stroke={this.props.colour}  strokeWidth="2" strokeLinecap="round" transform="translate(32.000000, 17.500000) rotate(90.000000) translate(-32.000000, -17.500000) "></Line>
                <Line x1="3" y1="19.5" x2="3" y2="15.5" id="Line" stroke={this.props.colour}  strokeWidth="2" strokeLinecap="round" transform="translate(3.000000, 17.500000) rotate(90.000000) translate(-3.000000, -17.500000) "></Line>
                <Line x1="17.5" y1="34" x2="17.5" y2="30" id="Line" stroke={this.props.colour}  strokeWidth="2" strokeLinecap="round"></Line>
            </G>
        </Svg>
		);
    }
}

interface Props {
    colour: string;
}