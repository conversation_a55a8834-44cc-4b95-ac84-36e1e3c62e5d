import React from 'react';
import { View, Text, Modal, TouchableOpacity } from 'react-native';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import { ReactComponent } from 'redi-types';
import { StoreComponent } from 'utils/store-component';
import { AppStore } from 'stores/app-store';
import { map } from 'rxjs/operators';
import { ConnectionStatus } from 'services/heartbeat';
import modalStyles from 'config/styles/modal-styles';
import AppButton from 'components/AppButton/AppButton';
import Redi_Text from 'components/Text/Redi_Text';
import * as RNFS from 'react-native-fs';
import Database from 'services/database';
import { FileForUploadDto } from 'dtos/file';

@StoreComponent({
	isOffline: AppStore.connectionStatus$.pipe(map(x => x === ConnectionStatus.Disconnected)),
	currentTeam: AppStore.team$
})
class ExportData extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			filename: 'ExportData'
		};
	}

	async componentDidMount() {

	}

	render() {
		return (
			<Modal
				transparent
				visible
				onRequestClose={this.props.onClose}>
				<View style={{ alignItems: 'center' }}>
					<View style={modalStyles.content}>
						<View style={modalStyles.header}>
							<Text style={textStyles.large_2}>Export Data</Text>
							<TouchableOpacity onPress={this.props.onClose}>
								<CloseIcon />
							</TouchableOpacity>
						</View>
						<Redi_Text
							placeholder="Unique Filename"
							onChange={(value) => this.setState({filename: value})}
							value={this.state.filename} />
						<AppButton
							rootStyle={{ marginTop: 150 }}
							theme="lightblue"
							content="Confirm"
							onPress={() => this.exportData()} />
					</View>
				</View>
			</Modal>
		);
	}

	private writeNewFile(paths:string[], filename:string, value:string, options:string) {
		console.log('path', paths);
		return RNFS.writeFile(paths[0] + '/' + filename, value, options);
	}

	private async exportImages(filename:string) {
		const sql = `
			SELECT
				f.fileId
				,f.contentPath
				,f.modifiedOnUtc
				,j.assetName
				,f.externalRecId
				,f.fieldId
			FROM files f
			JOIN jobs j ON j.externalRecId = f.externalRecId
			WHERE f.hasUploaded = 0
			ORDER BY j.modifiedOnUtc
		`;

		// all files which haven't been uploaded
		const results = await Database.select<FileForUploadDto>(sql);

		console.log('exportImages', results);

		let errors:string[] = [];
		for (let ii = 0, ilen = results.length; ii < ilen; ii++) {
			let newName = filename + '__' + results[ii].fileId + '__' + results[ii].contentPath.substring(results[ii].contentPath.lastIndexOf('/') + 1);
			if (!newName.includes('.')) {
				newName += '.jpg';
			}
			RNFS.getAllExternalFilesDirs()
				.then(paths => RNFS.copyFile(results[ii].contentPath, paths[0] + '/' + newName))
				.then(() => console.log('successfully backed up:', newName))
				.catch(err => {
					errors.push(JSON.stringify(err));
					console.log('err', err);
				});
		}

		if (errors.length > 0) {
			throw 'export_images:\r\n' + errors.join('\r\n');
		}
	}

	private exportDb() {
		let root = RNFS.DocumentDirectoryPath.substring(0, RNFS.DocumentDirectoryPath.lastIndexOf('/files'));

		RNFS.getAllExternalFilesDirs()
			.then(paths => RNFS.copyFile(root + '/databases/logsys.db', paths[0] + '/' + this.state.filename + '.db'))
			.then(() => {
				console.log('successful backup of db');
				this.exportImages(this.state.filename);
			})
			.catch(err => {
				throw 'export_db:' + JSON.stringify(err);
			});

		this.props.onClose();
	}

	private exportData() {
		this.exportDb();
	}
}

export default ExportData as ReactComponent<PublicProps>;

interface Props extends PublicProps {
	isOffline: boolean;
	currentTeam: string;
}

interface PublicProps {
	onClose(): void;
}

interface State {
	filename: string;
}