import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import GPS, { GPSCoordinatesDto, GPSProps } from 'components/GPS/GPS';

export default class FormGPS extends React.Component<WrappedFieldInterface<GPSCoordinatesDto, GPSProps>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<GPS
				{...props}
				value={this.props.fieldProps.value}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				error={this.props.form.field.error}
				onChange={(value) => this.props.fieldProps.onChange(value)} />
		);
	}
}