import React from 'react';
import { View } from 'react-native';
import { FormSubmissionDto, FormSubmissionSection } from 'redi-types';
import FormContent, { FormContentType } from './Form';
import { RouteComponentProps } from 'react-router-native';
import FileService from 'services/file';
import JobService from 'services/job';
import { JobDto } from 'dtos/job';
import { string } from 'redi-ui-utils';

// eslint-disable-next-line react/require-optimization
export default class FormPage extends React.Component<Props, State> {
	constructor(props: any) {
		super(props);
		this.state = {
			form: null,
			workflowCode: null
		};
		this.onFormChange = this.onFormChange.bind(this);
	}

	async componentDidMount() {
		const formData = await FileService.getFormData(this.props.match.params.id);
		const job = await JobService.getJob(this.props.match.params.id);

		if (!formData) {
			throw new Error('NO FORM DATA');
		}

		// Handle repeats
		formData.data.pages.forEach((page, pageIndex) => {

			// For each section
			for ( var iSection = 0; iSection < page.sections.length; iSection++ ) {
				var section = page.sections[iSection];

				// Check if repeats
				if (section?.repeat) {		
					var copiedSections = [];
					const replaceAllIndexNums = (inObject: FormSubmissionSection, index: number) => {
						var tempJson = JSON.stringify(inObject);
						tempJson = tempJson.replace(/<INDEX_NUM>/gi, index+'');
						return JSON.parse(tempJson);
					}
					for(var i = 0; i < section.repeat - 1; i++) {
						// Copy to new object and push to the new array
						copiedSections.push(Object.assign({}, section));
						// Replace "<INDEX_NUM>" in all fields
						copiedSections[i] = replaceAllIndexNums(copiedSections[i], i+2);
						// Add 'Add Another?' if needed
						if(section.addAnotherLabel) {
							if (i != section.repeat - 2) {
								copiedSections[i].fields.push({
									"textId": section.textId + "AddAnother" + (i+2),
									"label": section.addAnotherLabel,
									"fieldType": "YesNo"
								});
							}
							if (!copiedSections[i].visibilityConditions) {
								copiedSections[i].visibilityConditions = [];
							}
							copiedSections[i].visibilityConditions.push({
								"textId": section.textId + "AddAnother" + (i+1),
								"value": true,
								"operator": "equal"
							});
						}
						copiedSections[i].repeat = null;
					}

					// Make same alterations to original section, except 'Another' condition
					formData.data.pages[pageIndex].sections[iSection] = replaceAllIndexNums(section, 1);
					if(section.addAnotherLabel) {
							formData.data.pages[pageIndex].sections[iSection].fields.push({
							"textId": section.textId + "AddAnother" + 1,
							"label": section.addAnotherLabel,
							"fieldType": "YesNo"
						});
					}
					formData.data.pages[pageIndex].sections[iSection].repeat = null;
		
					// Now add those copied sections
					copiedSections.forEach((newSection, index) => {
						formData.data.pages[pageIndex].sections.splice(1 + iSection, 0, newSection);
						iSection++;
					});
				}
			}
		});

		this.setState({
			form: formData,
			workflowCode: job.workflowCode
		});
	}

	render() {
		return (
			<View>
				<FormContent
					ref={this._formRef}
					onChange={this.onFormChange}
					initialValues={this.state.form}
					workflowCode={this.state.workflowCode}
					externalRecId={this.props.match.params.id} />
			</View>
		);
	}

	private _formRef = React.createRef<FormContentType>();

	private onFormChange(path: string, pathValue?: any, newFormValue?: FormSubmissionDto, prevFormValue?: FormSubmissionDto) {
		this._formRef.current?.forceUpdate();
	}
}

interface Props extends RouteComponentProps<{ id: string }> {

}

interface State {
	form: Partial<FormSubmissionDto>;
	workflowCode: string;
}
