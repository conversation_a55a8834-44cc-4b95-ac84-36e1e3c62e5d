import React from 'react';
import Svg, { G, <PERSON>, Polyline } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class HistoryIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path d="M7.01396475,26.0032302 C9.48915546,29.0518413 13.2670693,31 17.5,31 C24.9558441,31 31,24.9558441 31,17.5 C31,10.0441559 24.9558441,4 17.5,4 C10.0441559,4 4,10.0441559 4,17.5" stroke={this.props.colour} strokeWidth="2" />
					<Polyline stroke={this.props.colour} strokeWidth="2" transform="translate(4.000000, 17.500000) scale(1, -1) translate(-4.000000, -17.500000) " points="8 19.5 4 15.5 0 19.5" />
					<Polyline stroke={this.props.colour} strokeWidth="2" points="17.5 10.5 17.5 17.5 22.5 17.5" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
