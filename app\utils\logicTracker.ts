import { string, createTypedPath, TypedPath } from 'redi-ui-utils';
import {
	FormSubmissionDto,
	FormLogicCompiled,
	FormSubmissionPage,
	FormSubmissionSection,
	FormSubmissionField,
	FormSubmissionRepeatingField
} from 'redi-types';
import { ScriptRuntime } from './sick-slick-shit';
import { WrappedForm } from 'redi-form';

let path = createTypedPath<FormSubmissionDto>();

export class LogicTracker {
	private doneLogics: Array<Pair> = [];
	private updatedTextIds: Array<string> = [];

	constructor(private textId: string, private submission: FormSubmissionDto, private form: WrappedForm<FormSubmissionDto>) {
		this.setValue = this.setValue.bind(this);
		this.getValue = this.getValue.bind(this);
		this.show = this.show.bind(this);
		this.hide = this.hide.bind(this);
	}

	run() {
		let logics: Array<FormLogicCompiled> = this.submission.data.logic.filter(s => s.ids.includes(this.textId));
		logics.forEach(logic => {
			let scriptRuntime: ScriptRuntime = new ScriptRuntime();
			scriptRuntime.addToRootScope({
				$triggerIds: logic.ids,
				$setValue: this.setValue,
				$getValue: this.getValue,
				$hide: this.hide,
				$show: this.show
			});
			scriptRuntime.run(logic);
			this.doneLogics.push({
				textId: this.textId,
				logic: logic
			});
		});

		let circularRefDetected: boolean = false;
		while (this.updatedTextIds.length && !circularRefDetected) {
			let textId = this.updatedTextIds.shift();
			let logics = this.submission.data.logic.filter(s => s.ids.includes(textId));

			// eslint-disable-next-line no-loop-func
			logics.forEach(logic => {
				if (this.doneLogics.find(s => s.textId === textId && s.logic === logic)) {
					circularRefDetected = true;
				} else {
					let scriptRuntime: ScriptRuntime = new ScriptRuntime();
					scriptRuntime.addToRootScope({
						$triggerIds: logic.ids,
						$setValue: this.setValue,
						$getValue: this.getValue,
						$hide: this.hide,
						$show: this.show
					});
					scriptRuntime.run(logic);
					this.doneLogics.push({
						textId: this.textId,
						logic: logic
					});
				}
			});
		}

		if (circularRefDetected) {
			throw new Error('Circular reference detected.');
		}
	}

	setValue(textId: string, newValue: any) {
		if (!this.updatedTextIds.includes(textId)) {
			this.updatedTextIds.push(textId);
		}
	}

	getValue(textId: string) {
		let field = this.findField(textId);
		if (field) {
			if (isRepeating(field)) {
				throw new Error('Cannot get value of repeating field.');
			} else {
				return field.value;
			}
		} else {
			throw new Error('Could not find field with id ' + textId);
		}
	}

	show(textId: string) {
		let pagePath: FormSubmissionPage = this.findPagePath(textId);
		if (pagePath) {
			return;
		}

		let sectionPath: FormSubmissionSection = this.findSectionPath(textId);
		if (sectionPath) {
			sectionPath.options.hide = false;
			return;
		}

		let fieldPath = this.findFieldPath(textId);
		if (fieldPath) {
			if (isRepeating(fieldPath)) {
				this.form.setValue(fieldPath.fieldOptions.hide, false);
			} else {
				this.form.setValue(fieldPath.options.hide, false);
			}
		}
	}

	hide(textId: string) {
		let pagePath: FormSubmissionPage = this.findPagePath(textId);
		if (pagePath) {
			return;
		}

		let sectionPath: TypedPath<FormSubmissionSection> = this.findSectionPath(textId);
		if (sectionPath) {
			this.form.setValue(sectionPath.options.hide, true);
			return;
		}

		let fieldPath = this.findFieldPath(textId);
		if (fieldPath) {
			if (isRepeating(fieldPath)) {
				this.form.setValue(fieldPath.fieldOptions.hide, true);
			} else {
				this.form.setValue(fieldPath.options.hide, true);
			}
		}
	}

	findPagePath(textId: string): FormSubmissionPage {
		for (let ii = 0; ii < this.submission.data.pages.length; ii++) {
			const page = this.submission.data.pages[ii];
			if (textId === page.textId) {
				return path.data.pages[ii];
			}
		}
	}

	findSectionPath(textId: string): TypedPath<FormSubmissionSection> {
		for (let ii = 0; ii < this.submission.data.pages.length; ii++) {
			const page = this.submission.data.pages[ii];
			for (let jj = 0; jj < page.sections.length; jj++) {
				const section = page.sections[jj];
				if (section.textId === textId) {
					return path.data.pages[ii].sections[jj];
				}
			}
		}
	}

	findFieldPath(textId: string) {
		for (let ii = 0; ii < this.submission.data.pages.length; ii++) {
			const page = this.submission.data.pages[ii];
			for (let jj = 0; jj < page.sections.length; jj++) {
				const section = page.sections[jj];
				for (let kk = 0; kk < section.fields.length; kk++) {
					const field = section.fields[kk];
					if (field.textId === textId) {
						return path.data.pages[ii].sections[jj].fields[kk];
					}
				}
			}
		}
	}

	findPage(textId: string): FormSubmissionPage {
		return this.submission.data.pages.find(s => s.textId === textId);
	}

	findSection(textId: string): FormSubmissionSection {
		for (const page of this.submission.data.pages) {
			for (const section of page.sections) {
				if (section.textId === textId) {
					return section;
				}
			}
		}
	}

	findField(textId: string) {
		for (const page of this.submission.data.pages) {
			for (const section of page.sections) {
				for (const field of section.fields) {
					if (field.textId === textId) {
						return field;
					}
				}
			}
		}
	}
}

type Pair = {
	textId: string;
	logic: FormLogicCompiled;
};

const isRepeating = function(field: FormSubmissionRepeatingField | FormSubmissionField): field is FormSubmissionRepeatingField {
	return field && 'repeatingFieldId' in field;
};
