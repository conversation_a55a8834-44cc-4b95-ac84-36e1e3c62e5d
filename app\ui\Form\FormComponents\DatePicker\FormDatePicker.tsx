import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import DatePicker, { DatePickerProps } from 'components/DatePicker/DatePicker';

export default class FormDatePicker extends React.PureComponent<WrappedFieldInterface<Date, DatePickerProps>> {
	render() {
		const props = this.props.componentProps();
		return (
			<DatePicker
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error}
				overrideStyles={{
					wrapper: {
						marginTop: 30
					}
				}} />
		);
	}
}