import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';
import theme from 'config/styles/theme';

const styles = scaleAllToDesign({
	root: {
		flex: 1,
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 60
	},
	title: {
		flexDirection: 'row',
		justifyContent: 'center',
		alignItems: 'center'
	},
	sectionRoot: {
		paddingVertical: 30,
		marginHorizontal: 85,
		borderBottomColor: '#D2D2D2'
	},
	addComment: {
		color: '#393939',
		fontSize: 20,
		marginLeft: 10
	},
	textContent: {
		fontSize: 20,
		letterSpacing: 1.5, 
		fontFamily: theme.FONT_REGULAR,
		color: theme.TEXT_DARK,
		marginVertical: 10
	},
	chevron: {
		height: 35,
		width: 35,
		backgroundColor: '#E6E6E6',
		borderRadius: 18,
		marginTop: 22,
		marginRight: 20
	},
	expand: {
		flexDirection: 'row',
		height: 35,
		alignItems: 'center',
		marginBottom: 30
	},
	fileItem: {
		backgroundColor: '#E6E6E6',
		borderRadius: 10,
		height: 47,
		marginBottom: 10,
		justifyContent: 'center',
		paddingHorizontal: 20
	}
});
export default styles;
