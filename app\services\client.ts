import config from 'config/config';
import { http } from 'redi-http';
import { ClientDto } from 'dtos/client';
import Database from './database';
import { AppStore } from 'stores/app-store';
import { ConnectionStatus } from './heartbeat';

class clientService {
	getClients(): Promise<ClientDto[]> {
		return Database.select<ClientDto>('SELECT * FROM clients ORDER BY name ASC');
	}
	async saveClientsFromServer(): Promise<void> {
		if (AppStore.getValue().connectionStatus !== ConnectionStatus.Connected) {
			return;
		}

		const url = `${config.apiUrl}Client/GetClients`;
		try {
			const response = await http<ClientDto[]>({ method: 'GET', url });
			if (response.data) {
				await Database.insertOrReplace<ClientDto>(
					'clients',
					['clientId', 'name'],
					response.data
				);
			}
		} catch (error) {
			console.error(error);
		}
	}
}
const ClientService = new clientService();
export default ClientService;


