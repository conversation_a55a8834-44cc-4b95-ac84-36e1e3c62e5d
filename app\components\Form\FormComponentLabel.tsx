import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { FORM_CLASSES } from 'config/styles/form-component-common';

export default class FormComponentLabel extends React.PureComponent<Props, never> {
	render() {
		if (!this.props.label) {
			return null;
		}
		const required = this.props.required ? ' *' : '';
		return (
			<View style={[styles.labelWrapper, this.props?.overrideStyles?.labelWrapper]}>
				<Text style={[styles.labelText, this.props?.overrideStyles?.labelText]}>{this.props.label + required}</Text>
			</View>
		);
	}
}

interface Props {
	label?: string;
	required?: boolean;
	overrideStyles?: {
		labelWrapper?: ViewStyle;
		labelText?: ViewStyle;
	}
}

const styles = StyleSheet.create({
	labelWrapper: {
		display: 'flex',
		paddingLeft: 0,
		paddingRight: 10,
		marginBottom: 15,
		justifyContent: 'flex-start',
		flexDirection: 'row',
		width: '100%',
	},
	labelText: {
		...FORM_CLASSES.LABEL_TEXT
	}
});