import SQLite from 'react-native-sqlite-storage';
import Migration, { migrationTableSql, migrations } from './migrations';

class database {
	private _db: SQLite.SQLiteDatabase;
	private _dateCols: string[] = ['modifiedOnUtc', 'createdOn'];

	get db() {
		return this._db;
	}

	async connect(): Promise<void> {
		this._db = await SQLite.openDatabase({
			name: 'logsys.db',
			location: 'default'
		});
	}
	execute(sql: string, args?: any[]): Promise<SQLite.ResultSet> {
		const __args = new Array<any>();
		if (args?.length > 0) {
			for (let arg of args) {
				if (arg instanceof Date) {
					arg = arg.toISOString();
				}
				__args.push(arg);
			}
		}
		return new Promise((res, rej) => {
			this._db.transaction(tx => tx.executeSql(sql, __args, (tx, result) => res(result), (tx, e) => rej(e)));
		});
	}
	select<T>(sql: string, args?: any[]): Promise<T[]> {
		return this.execute(sql, args)
			.then(x => <T[]>x.rows.raw())
			.then(x => {
				if (x) {
					x.forEach((y: any) => {
						Object.keys(y).forEach(key => {
							if (this._dateCols.includes(key)) {
								y[key] = new Date(y[key]);
							}
						});
					});
				}
				return x;
			});
	}
	insertOrReplace<T extends { [key: string]: any }>(table: string, props: (keyof T)[], items: T[]): Promise<void> {
		return this._insert(true, table, props, items);
	}
	insert<T extends { [key: string]: any }>(table: string, props: (keyof T)[], items: T[]): Promise<void> {
		return this._insert(false, table, props, items);
	}
	migrate(): Promise<void> {
		return this.transaction(tx => {
			tx.executeSql(migrationTableSql, []);
			tx.executeSql('SELECT * FROM migrations', [], (tx, results) => {
				const rows = results.rows.raw() as Migration[];

				for (const [key, value] of migrations) {
					const search = rows.find(x => x.name === key);
					if (!search) {
						for (const sql of value.split(';')) {
							if (sql.trim() !== '') {
								tx.executeSql(sql, [], null, (_, e) => console.error(`ERR: ${e} SQL: ${sql}`));
							}
						}
						const sql = 'INSERT INTO migrations (name, executed) VALUES (?, ?)';
						tx.executeSql(sql, [key, new Date().toISOString()], null, (_, e) => console.error(e));
					}
				}
			});
		});
	}
	transaction(func: (tx: SQLite.Transaction) => void): Promise<void> {
		return new Promise((res, rej) => this._db.transaction(func, e => rej(e), () => res()));
	}
	reset(): Promise<any> {
		return SQLite.deleteDatabase({
			name: 'logsys.db',
			location: 'default'
		});
	}

	private _insert<T extends { [key: string]: any }>(replace: boolean, table: string, props: (keyof T)[], items: T[]): Promise<void> {
		return this.transaction(tx => {
			for (const item of items) {
				let sql = `
					INSERT ${replace ? 'OR REPLACE' : ''} INTO ${table} (${props.join(',')})
					VALUES
				`;

				const args: T[keyof T][] = [];
				sql += `(${props.map(x => '?').join(',')})`;
				args.push(
					...props.map(x => {
						if ((item[x] as any) instanceof Date) {
							return item[x].toISOString();
						}
						if (typeof (item[x] as any) === 'boolean') {
							return item[x] ? 1 : 0;
						}
						return item[x];
					})
				);
				tx.executeSql(sql, args);
			}
		});
	}
}

const Database = new database();
export default Database;
