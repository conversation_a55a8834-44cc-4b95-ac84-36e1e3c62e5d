import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';
import theme from 'config/styles/theme';

const styles = scaleAllToDesign({
	root: {
		backgroundColor: '#0062BC',
		left: 0,
		top: 126,
		position: 'absolute',
		borderTopRightRadius: 40,
		borderBottomRightRadius: 40
	},
	menuBtn: {
		paddingHorizontal: 15,
		paddingVertical: 12
	},
	panel: {
		backgroundColor: '#0062BC',
		width: 500,
		height: 1000,
		borderTopRightRadius: 40,
		borderBottomRightRadius: 40,
		justifyContent: 'flex-start',
		paddingBottom: 30
	},
	items: {
		height: 850
	},
	menuItem: {
		color: theme.WHITE,
		textAlign: 'left',
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2,
		fontSize: 18,
		marginBottom: 5
	},
	menuItemRoot: {
		paddingLeft: 15
	},
	chevron: {
		height: 35,
		width: 35,
		backgroundColor: '#fff',
		borderRadius: 18,
		marginRight: 13
	},
	itemLabelContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		marginBottom: 10
	}
});
export default styles;