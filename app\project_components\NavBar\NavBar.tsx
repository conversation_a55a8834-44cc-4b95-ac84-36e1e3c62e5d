import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import IconTextButton from 'project_components/IconTextButton/IconTextButton';
import navigator from 'services/navigator';
import { AppStore } from 'app/stores/app-store';
import SyncIcon from 'project_components/Icons/SyncIcon';
import InProgressFormIcon from 'project_components/Icons/InProgressFormIcon';
import CompletedIcon from 'project_components/Icons/CompletedIcon';
import ProgressBar from 'components/ProgressBar/ProgressBar';
import theme from 'config/styles/theme';
import MapIcon from 'project_components/Icons/MapIcon';
import PolesIcon from 'project_components/Icons/PolesIcon';
import SettingsIcon from 'project_components/Icons/SettingsIcon';
import { StoreComponent, StoreProps } from 'utils/store-component';
import { SyncStore } from 'stores/sync-store';


@StoreComponent({
	inProgress: SyncStore.inProgress$,
	syncingProgress: SyncStore.syncingProgress$,
	completed: SyncStore.completed$,
	syncing: SyncStore.syncing$
})
class NavBar extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			selected: undefined,
			hide: true
		};
	}

	componentDidMount() {
		this.props.subscribe(AppStore.currentRoute$, x => {
			let selected: SelectedItem;
			if (x === '/map') {
				selected = 'map';
			} else if (x.startsWith('/job')) {
				selected = 'jobs';
			} else if (x.startsWith('/sync')) {
				selected = 'sync';
			} else if (x === '/settings') {
				selected = 'settings';
			}
			const hide = x === '/login' || x === '' || x === '/reset';
			this.setState({ selected, hide });
		});
	}

	render() {
		if (this.state.hide) {
			return null;
		}
		return (
			<View style={styles.root}>
				<View style={styles.navBar}>
					<View style={styles.primaryNavItems}>
						<IconTextButton
							display="MAP"
							selected={this.state.selected === 'map'}
							onClick={() => navigator.go('/map')} >
							<MapIcon />
						</IconTextButton>
						<IconTextButton
							display="JOBS"
							selected={this.state.selected === 'jobs'}
							onClick={() => navigator.go('/job/scheduled')}>
							<PolesIcon />
						</IconTextButton>
						<IconTextButton
							display="SYNC"
							selected={this.state.selected === 'sync'}
							onClick={() => navigator.go('/sync/syncing')}>
							<SyncIcon />
						</IconTextButton>
					</View>
					<IconTextButton
						selected={this.state.selected === 'settings'}
						onClick={() => navigator.go('/settings')}>
						<SettingsIcon />
					</IconTextButton>
				</View>
				<View style={styles.syncSection}>
					<TouchableOpacity style={styles.syncBarButton} onPress={() => navigator.go('/sync/history/inProgress')}>
						<InProgressFormIcon colour={theme.WHITE} />
						<Text style={styles.syncBarText}>{this.props.inProgress}</Text>
					</TouchableOpacity>
					<TouchableOpacity style={styles.syncBarButton} onPress={() => navigator.go('/sync/history/completed')}>
						<CompletedIcon colour={theme.WHITE} />
						<Text style={styles.syncBarText}>{this.props.completed}</Text>
					</TouchableOpacity>
					<TouchableOpacity style={styles.syncBarButton} onPress={() => navigator.go('/sync/syncing')}>
						<SyncIcon colour={theme.WHITE} />
						<Text style={styles.syncBarText}>{this.props.syncing}</Text>
					</TouchableOpacity>
					<ProgressBar percent={this.props.syncingProgress ?? 0} />
				</View>
			</View>
		);
	}
}

export default NavBar as React.ComponentType;

interface State {
	selected: SelectedItem;
	hide: boolean;
}

type SelectedItem = 'map' | 'jobs' | 'sync' | 'settings' | undefined;

interface Props extends StoreProps {
	syncingProgress: number;
	inProgress: number;
	completed: number;
	syncing: number;
}