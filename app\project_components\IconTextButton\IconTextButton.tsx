import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import theme from 'config/styles/theme';

export default class IconTextButton extends React.PureComponent<Props, never> {
	render() {
		return (
			<TouchableOpacity onPress={this.props.onClick} style={[styles.root, this.props.selected && styles.selected]}>
				{React.Children.map(
					this.props.children,
					e => React.cloneElement(e as React.ReactElement<any>, {
						colour: this.props.selected ? theme.PRIMARY_DARK_BLUE : theme.PRIMARY_LIGHT_BLUE
					})
				)}
				{this.props.display && this.props.selected && (
					<Text style={[styles.text, this.props.selected && styles.selected]}>{this.props.display}</Text>
				)}
			</TouchableOpacity>
		);
	}
}

interface Props {
	display?: string;
	selected: boolean;
	onClick?(): void;
}
