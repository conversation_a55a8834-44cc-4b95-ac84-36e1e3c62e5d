import React from 'react';
import { View, Text, Modal, TouchableOpacity } from 'react-native';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import { ReactComponent } from 'redi-types';
import { StoreComponent } from 'utils/store-component';
import { AppStore } from 'stores/app-store';
import { map } from 'rxjs/operators';
import { ConnectionStatus } from 'services/heartbeat';
import modalStyles from 'config/styles/modal-styles';
import AppButton from 'components/AppButton/AppButton';
import Redi_Text from 'components/Text/Redi_Text';

@StoreComponent({
	isOffline: AppStore.connectionStatus$.pipe(map(x => x === ConnectionStatus.Disconnected)),
	currentTeam: AppStore.team$
})
class ImportData extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
		};
	}

	async componentDidMount() {

	}

	render() {
		return (
			<Modal
				transparent
				visible
				onRequestClose={this.props.onClose}>
				<View style={{ alignItems: 'center' }}>
					<View style={modalStyles.content}>
						<View style={modalStyles.header}>
							<Text style={textStyles.large_2}>Import Data</Text>
							<TouchableOpacity onPress={this.props.onClose}>
								<CloseIcon />
							</TouchableOpacity>
						</View>
						<AppButton
							rootStyle={{ marginTop: 150 }}
							theme="lightblue"
							content="Confirm"
							onPress={() => this.importData()} />
					</View>
				</View>
			</Modal>
		);
	}

	private async scanFiles(): Promise<void> {
		console.log("scanFiles");
	}

	// eslint-disable-next-line require-await
	private async importData(): Promise<void> {
		console.log("exportData");
		this.props.onClose();
	}
}

export default ImportData as ReactComponent<PublicProps>;

interface Props extends PublicProps {
	isOffline: boolean;
	currentTeam: string;
}

interface PublicProps {
	onClose(): void;
}

interface State {
}