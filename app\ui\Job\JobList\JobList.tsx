import React from 'react';
import { View, Text } from 'react-native';
import { RouteComponentProps } from 'react-router-native';
import HeaderBar from 'components/HeaderBar/HeaderBar';
import styles from './styles';
import theme from 'config/styles/theme';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import textStyles from 'config/styles/text-styles';
import JobTable from './JobTable';
import SearchBar from 'components/SearchBar/SearchBar';
import Select from 'components/Select/Select';
import SelectItem from 'components/Select/SelectItem';
import { JobListData } from 'stores/job-list-data';
import AddPole from '../AddPole/AddPole';
import AppButton from 'components/AppButton/AppButton';
import BackButton from 'project_components/BackButton/BackButton';
import { scaleToDesign } from 'utils/design-scale';

export default class JobList extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		let title = props.match.params.workflowCode;
		if (props.match.params.maintenanceZone) {
			title = decodeURIComponent(props.match.params.maintenanceZone);
		}
		this.state = {
			title,
			search: '',
			selected: 'AllJobs',
			showAddPole: false
		};
	}

	render() {
		return (
			<View>
				<HeaderBar>
					<View style={styles.root}>
						<BackButton />
						<View style={styles.title}>
							<ScheduledFormIcon colour={theme.WHITE} />
							<Text style={{ ...textStyles.large, marginLeft: scaleToDesign(16) }}>{this.state.title}</Text>
						</View>
						<AppButton
							theme="lightblue-outline"
							width={scaleToDesign(160)}
							height={scaleToDesign(57)}
							onPress={() => this.setState({ showAddPole: true })}
							content="Add Pole" />
					</View>
				</HeaderBar>
				<View style={{ paddingHorizontal: scaleToDesign(40), paddingTop: scaleToDesign(30), height: '100%', maxHeight: scaleToDesign(1140), paddingBottom: scaleToDesign(40) }}>
					<View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
						<SearchBar
							query={this.state.search}
							onSearch={e => {
								this.setState({ search: e });
								JobListData.setPartialArgs({ search: e });
							}}
							style={{ marginBottom: scaleToDesign(30) }} />
						<Select
							selected={this.state.selected}
							onSelected={e => {
								this.setState({ selected: e });
								JobListData.setPartialArgs({ status: e });
							}}>
							<SelectItem value="AllJobs" display="All Jobs" />
							<SelectItem value="Completed" display="Completed" />
							<SelectItem value="CompletedToday" display="Completed Today" />
							<SelectItem value="InProgress" display="In Progress" />
							<SelectItem value="Scheduled" display="Scheduled" />
						</Select>
					</View>
					<JobTable {...this.props.match.params} />
				</View>
				{this.state.showAddPole &&
					<AddPole
						workOrder={this.props.match.params.workOrder}
						workflowCode={this.props.match.params.workflowCode}
						maintenanceZone={this.props.match.params.maintenanceZone}
						onClose={() => this.setState({ showAddPole: false })} />
				}
			</View>
		);
	}

}

interface Props extends RouteComponentProps<{
	workflowCode: string,
	workOrder: string,
	maintenanceZone?: string
}> { }

interface State {
	title: string;
	search: string;
	selected: string;
	showAddPole: boolean;
}