import React from 'react';
import Svg, { <PERSON>, <PERSON>, <PERSON> } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class MapIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path
						d="M19.5908851,32.9435869 L20.412885,31.8179461 C26.5807996,23.2885413 29.6647569,17.0708116 29.6647569,13.1647569 C29.6647569,6.44634719 24.2184097,1 17.5,1 C10.7815903,1 5.33524307,6.44634719 5.33524307,13.1647569 C5.33524307,17.1557232 8.55473873,23.5599615 14.99373,32.3774719 L15.4090781,32.9435866 C16.2603799,34.0983607 17.8866258,34.3443737 19.0414,33.4930719 C19.2510769,33.3384976 19.4363108,33.1532638 19.5908851,32.9435869 Z"
						stroke={this.props.colour}
						strokeWidth="2" />
					<Circle stroke={this.props.colour} strokeWidth="2" cx="17.5" cy="13.1522798" r="4.43762485" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
