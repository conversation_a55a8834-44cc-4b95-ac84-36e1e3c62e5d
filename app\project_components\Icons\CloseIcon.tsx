import React from 'react';
import Svg, { G, Line } from 'react-native-svg';
import { scaleToDesign } from 'utils/design-scale';

export default class CloseIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: '#B4B4B4'
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Line x1="10" y1="17.5" x2="25" y2="17.5" stroke={this.props.colour} strokeWidth="2" transform="translate(17.500000, 17.500000) rotate(45.000000) translate(-17.500000, -17.500000) " />
					<Line x1="10" y1="17.5" x2="25" y2="17.5" stroke={this.props.colour} strokeWidth="2" transform="translate(17.500000, 17.500000) scale(-1, 1) rotate(45.000000) translate(-17.500000, -17.500000) " />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
