import { Store, Query, createStore, createQuery, filterNil, UpdateStateCallback } from '@datorama/akita';

export abstract class StoreBase<TState> {
	constructor(name: string, init: Partial<TState> = {}) {
		this.store = createStore<TState>(init, { name, resettable: true });
		this.query = createQuery(this.store);
	}

	protected readonly store: Store<TState>;
	protected readonly query: Query<TState>;

	reset = () => this.store.reset();
	getValue = () => this.query.getValue();

	update(state: Partial<TState>): any
	update(stateCallback: UpdateStateCallback<TState>): any
	update(s: any) {
		this.store.update(s);
	}
}
