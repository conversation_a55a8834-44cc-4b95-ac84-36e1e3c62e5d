import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	wrapper: {
		flexDirection: 'column',
		padding: 10,
	},
	rowWrapper: {
		flexDirection: 'row',
		display: "flex",
		margin: 2,
		justifyContent: 'space-evenly'
	},
	checkboxWrapper: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start'
	},
	checkboxLabel: {
		fontSize: 22,
	},
	checkboxOuter: {
		height: 20,
		width: 20,
		borderWidth: 2,
		borderColor: '#9B6AA2',
		marginLeft: 8,
		marginRight: 8,
	},
	checkboxInner: {
		height: "100%",
		width: "100%",
		backgroundColor: "#9B6AA2"
	},
});

export default styles;
