module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      require.resolve('babel-plugin-module-resolver'),
      {
        root: ["."],
        alias: {
          "components": "./app/components",
          "config": "./app/config",
          "dtos": "./app/dtos",
          "project_components": "./app/project_components",
          "services": "./app/services",
          "stores": "./app/stores",
          "ui": "./app/ui",
          "utils": "./app/utils",
          "app": "./app"
        }
      }

    ],
    ["@babel/plugin-proposal-decorators", { "legacy": true }]
  ]
};
