import React from 'react';
import { View, Text, TouchableOpacity, SectionList } from 'react-native';
import { RouteComponentProps } from 'react-router-native';
import Select from 'components/Select/Select';
import SelectItem from 'components/Select/SelectItem';
import { HistoryData, HistorySectionData } from 'stores/history-data';
import { getStatusIcon } from 'ui/Job/JobIcons/status-icon'
import tableStyles from 'project_components/Table/style';
import styles from './styles';
import { StoreComponent } from 'utils/store-component';
import navigator from 'services/navigator';
import SyncIcon from 'project_components/Icons/SyncIcon';
import textStyles from 'config/styles/text-styles';
import { DateTime } from 'redi-ui-utils';
import { JobStatusCode } from 'dtos/job';
import { scaleToDesign } from 'utils/design-scale';

@StoreComponent({
	data: HistoryData.data$
})
export default class History extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			selected: 'all'
		};
	}

	componentDidMount() {
		HistoryData.setVisible();
		HistoryData.setArgs({ filter: this.props.match.params.filter });
		this.updateFilter();
	}
	componentDidUpdate(prevProps: Props) {
		if (prevProps.match.params.filter !== this.props.match.params.filter) {
			this.updateFilter();
			HistoryData.setPartialArgs({ filter: this.props.match.params.filter });
		}
	}
	componentWillUnmount() {
		HistoryData.reset();
	}

	render() {
		return (
			<View style={{ paddingHorizontal: scaleToDesign(40), paddingTop: scaleToDesign(30), height: '100%', maxHeight: scaleToDesign(1140), paddingBottom: scaleToDesign(40) }}>
				<View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
					<Select
						selected={this.state.selected}
						onSelected={e => {
							this.setState({ selected: e });
							HistoryData.setPartialArgs({ filter: e });
						}}>
						<SelectItem value="all" display="All" />
						<SelectItem value="inProgress" display="In Progress" />
						<SelectItem value="completed" display="Completed" />
					</Select>
				</View>
				<SectionList
					keyExtractor={(item, index) => index.toString()}
					sections={this.props.data}
					renderSectionHeader={({ section: { title, index } }) =>
						<View style={{
							...styles.sectionHeader,
							justifyContent: index === 0 ? 'space-between' : 'center'
						}}>
							{index === 0 && <Text style={textStyles.mediumGray}>Pick ID</Text>}
							<Text style={textStyles.mediumGray}>{title}</Text>
							{index === 0 && <Text style={textStyles.mediumGray}>Submitted</Text>}
						</View>
					}
					renderItem={({ item: x }) =>
						<TouchableOpacity
							onPress={() => navigator.go(`/jobDetail/${x.externalRecId}`)}
							style={{ ...tableStyles.row, alignItems: 'center' }}>
							<View style={styles.assetCell}>
								{x.isUploading ? <SyncIcon size={28} /> : getStatusIcon(x.statusCode)}
								<Text style={{
									...styles.assetName,
									color: x.statusCode === JobStatusCode.JComplete ?
										'#8B8B8B' : styles.assetName.color
								}}>{x.assetName}</Text>
							</View>
							<View style={{ alignItems: 'flex-end', flex: 1 }}>
								<Text style={{
									...textStyles.mediumGray,
									textTransform: 'lowercase'
								}}>
									{x.isUploading ? 'syncing...' :
										x.modifiedOnUtc ?
											DateTime.format(new Date(x.modifiedOnUtc), 'h:mm a') : ''}
								</Text>
							</View>
						</TouchableOpacity>
					} />
			</View>
		);
	}

	private updateFilter(): void {
		this.setState({ selected: this.props.match.params.filter })
	}
}

interface Props extends RouteComponentProps<{ filter: HistoryFilter }> {
	data: HistorySectionData[];
}
interface State {
	selected: HistoryFilter;
}

export type HistoryFilter = 'all' | 'inProgress' | 'completed';