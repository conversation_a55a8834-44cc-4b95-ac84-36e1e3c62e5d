import * as React from "react";
import { OmitName } from 'redi-types';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import modalStyles from 'config/styles/modal-styles';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import AppButton from 'components/AppButton/AppButton';
import theme from 'config/styles/theme';

let instance: ConfirmModal = null;

/**
 * This should be singleton instance throughout the lifetime of the app. Place in shell or similar location
 */
export class ConfirmModal extends React.PureComponent<Props, State> {
	unmounted: boolean;
	constructor(props: Props) {
		super(props);

		instance = this;

		this.state = {
			show: null
		};
	}

	componentWillUnmount() {
		this.unmounted = true;
		if (instance === this) {
			instance = null;
		}
	}

	show(opts: ModalProps) {
		if (!this.unmounted) {
			this.setState({ show: opts });
		}
	}

	hide() {
		if (!this.unmounted) {
			this.setState({ show: null });
		}
	}

	render() {
		if (this.state.show) {
			return (
				<Modal
					onRequestClose={this.state.show.onClose}
					transparent
					visible>
					<View style={{ alignItems: 'center' }}>
						<View style={modalStyles.content}>
							<View style={modalStyles.header}>
								<Text style={textStyles.large_2}>{this.state.show.title ?? 'Confirm'}</Text>
								<TouchableOpacity onPress={this.state.show.onClose}>
									<CloseIcon />
								</TouchableOpacity>
							</View>
							<View>
								<Text style={{
									...textStyles.large,
									marginTop: 20,
									marginBottom: 40,
									color: theme.TEXT_DARK
								}}>{this.state.show.bodyText}</Text>
							</View>
							<View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
								<AppButton
									theme="red"
									content={this.state.show.confirmText ?? 'Confirm'}
									width="48%"
									onPress={() => this.state.show.onConfirm()} />
								<AppButton
									theme="lightblue-outline"
									content="Cancel"
									width="48%"
									textStyle={{ color: theme.TEXT_DARK }}
									onPress={() => this.state.show.onClose()} />
							</View>
						</View>
					</View>
				</Modal>
			);
		} else {
			return null;
		}
	}
}

interface Props { }
interface State {
	show: ModalProps;
}

export type ShowDialogOptions = OmitName<ModalProps, "onClose" | "onConfirm">;

export interface ModalProps {
	onClose?: () => void;
	title?: string;
	bodyText?: string;
	confirmText?: string;
	onConfirm?: (e?: React.MouseEvent) => void;
}


/**
 * Show a modal popup based on the given props
 * @param opts Modal dialog props
 */
export function confirmDialog(opts: ShowDialogOptions) {
	if (instance) {
		return new Promise((resolve, reject) => {
			instance.show({
				...opts,
				onClose: () => {
					instance && instance.hide();
					reject();
				},
				onConfirm: () => {
					instance && instance.hide();
					resolve();
				}
			});
		});
	} else {
		return Promise.reject("ModalDialog instance does not exist");
	}
}