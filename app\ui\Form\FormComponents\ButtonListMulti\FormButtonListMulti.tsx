import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput, TextInputProps } from 'react-native';
import { WrappedFieldInterface } from 'redi-form';
import { OmitName } from 'redi-types';
import ButtonList from 'components/ButtonList/ButtonList';
import ButtonListMulti from 'components/ButtonList/ButtonListMulti';

export default class FormButtonListMulti extends React.Component<WrappedFieldInterface<string[], FormButtonListMultiProps>> {

    constructor(props: any) {
        super(props);
    }

    render() {
        const props = this.props.componentProps();

        return (
            <ButtonListMulti<string>
                {...props}
                values={this.props.values}
                selected={this.props.fieldProps.value}
                onChange={this.props.fieldProps.onChange}>
            </ButtonListMulti>
        );
    }
}

export interface FormButtonListMultiProps {
    values: Array<string>;
}

interface State {

}