import { StyleSheet } from 'react-native';
import theme from './theme';
import { scaleToDesign } from 'utils/design-scale';

const textStyles = StyleSheet.create({
	large: {
		fontSize: scaleToDesign(22),
		color: theme.WHITE,
		textAlign: 'center',
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: scaleToDesign(2, false)
	},
	large_2: {
		fontSize: scaleToDesign(24),
		color: theme.TEXT_DARK,
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: scaleToDesign(2.3, false)
	},
	medium: {
		fontSize: scaleToDesign(15),
		color: theme.WHITE,
		textAlign: 'center',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: scaleToDesign(1.5, false)
	},
	mediumGray: {
		fontSize: scaleToDesign(16),
		color: '#A0A0A0',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: scaleToDesign(1, false)
	},
	regular: {
		fontSize: scaleToDesign(20),
		letterSpacing: scaleToDesign(1.5, false),
		fontFamily: theme.FONT_REGULAR,
		color: theme.TEXT_DARK
	}
});

export default textStyles;