import { FileDto } from 'dtos/file';
import Database from './database';
import JobService from 'services/job';
import { FormFieldTypes, FormSubmissionDto } from 'redi-types';
import { JobDto } from 'dtos/job';
import { HttpUtils } from 'redi-http';
import { GPSCoordinatesDto } from 'components/GPS/GPS';

class fileService {
	async saveFile(file: FileDto): Promise<void> {
		const dbFile: FileDto = {
			...file,
			hasUploaded: false,
			isUploading: false,
			modifiedOnUtc: new Date()
		};

		await Database.insert<FileDto>(
			'files',
			['fieldId', 'fileId', 'hasUploaded', 'modifiedOnUtc', 'contentPath', 'filePath', 'externalRecId'],
			[dbFile]
		);
		await Database.execute(`
			UPDATE jobs
			SET modifiedOnUtc = ?
			WHERE externalRecId = ?
		`, [dbFile.modifiedOnUtc, file.externalRecId]);
		JobService.refreshSync();
	}
	async removeFile(filePath: string): Promise<void> {
		const sql = 'DELETE FROM files WHERE filePath = ?';
		await Database.execute(sql, [filePath]);
		JobService.refreshSync();
	}
	async saveFormData(id: string, data: Partial<FormSubmissionDto>): Promise<void> {
		const converted = HttpUtils.convertDate(data, true);
		const mapDetails = this.getMapDetails(data);
		const assetName = this.getAssetName(data);
		if (assetName) {
			await Database.execute(`
			UPDATE jobs
			SET formData = ?, latitude = ?, longitude = ?, assetName = ?, hasUploaded = 0
			WHERE externalRecId = ?
		`, [JSON.stringify(converted), mapDetails?.latitude, mapDetails?.longitude, assetName, id]);
		} else {
			await Database.execute(`
				UPDATE jobs
				SET formData = ?, latitude = ?, longitude = ?, hasUploaded = 0
				WHERE externalRecId = ?
			`, [JSON.stringify(converted), mapDetails?.latitude, mapDetails?.longitude, id]);
		}
	}
	async getFormData(id: string): Promise<Partial<FormSubmissionDto>> {
		const data = await Database.select<JobDto>(`
			SELECT formData FROM jobs
			WHERE externalRecId = ?
			LIMIT 1
		`, [id]).then(x => x[0]);
		if(!data) {
			return null;
		}
		const parsed = JSON.parse(data.formData);
		const converted = HttpUtils.convertDate(parsed, false);
		return converted;
	}
	async clearFormData(id: string): Promise<void> {
		await Database.execute(`
			UPDATE jobs
			SET formData = NULL
			WHERE externalRecId = ?
		`, [id]);
	}
	private getMapDetails(data: Partial<FormSubmissionDto>): GPSCoordinatesDto {		
		return this.searchFieldValue<GPSCoordinatesDto>(data, 'gpsLocationId');
	}

	private getAssetName(data: Partial<FormSubmissionDto>): string {
		return this.searchFieldValue<string>(data, 'polePickId');
	}

	private searchFieldValue<T>(data: Partial<FormSubmissionDto>, textId: string): T {
		if (data && data.data && data.data.pages) {
			for (let page of data.data.pages) {
				if (!page.sections) {  return null; }
				for (let section of page.sections) {
					if (!section.fields) {  return null; }
					for (let field of section.fields) {
						if (textId === field.textId) {
							return field.value as T;
						}
					}
				}
			}
		}
		return null;
	}
}
const FileService = new fileService();
export default FileService;
