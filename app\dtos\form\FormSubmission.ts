import { number, string } from "redi-ui-utils";
import { FormVisibility } from "ui/Form/visibilty";

declare module "redi-types" {
	export interface FormSubmissionDto extends DtoBase {
		formSubmissionId: string;
		formId: string;
		formVersionId: string;
		completedByName: string;
		data?: FormSubmission;

		startDate?: Date;
		completedDate?: Date;
	}

	export interface FormSubmission {
		pages: FormSubmissionPage[];
	}

	export interface FormSubmissionPage {
		label: string;
		textId: string;
		sections: FormSubmissionSection[];
	}

	export interface FormSubmissionSection extends FormVisibility {
		label: string;
		textId: string;
		fields: FormSubmissionField[];
		isComplete?: boolean;
		repeat?: number;
		addAnotherLabel?: string;
	}

	export interface FormSubmissionField extends FormVisibility {
		textId: string;
		label?: string;
		fieldType: FormFieldTypes;
		dontClearOnHide?: boolean;
		value?: any;
		options?: {
			required?: boolean;
			choices?: any[];
			placeholder?: string;
			photoMaxHeight?: number;
			photoMaxWidth?: number;
			custom?: any;
			maxPhotos?: number;
		};
	}	
}
