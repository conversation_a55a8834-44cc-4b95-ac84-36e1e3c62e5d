import Config from 'react-native-config';

const config: REDIConfig = {
	apiUrl: Config.RSS_API_URL,
	appUrl: Config.RSS_APP_URL,
	buildVersion: Config.RSS_BUILD_VERSION,
	environment: Config.RSS_ENVIRONMENT
};

export default config;

interface REDIConfig {
	readonly apiUrl: string;
	readonly appUrl: string;
	readonly buildVersion: string;
	readonly environment: 'Local' | 'Development' | 'Production';
}
