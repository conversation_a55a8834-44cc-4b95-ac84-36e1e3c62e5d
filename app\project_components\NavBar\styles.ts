import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	root: {
		backgroundColor: theme.PRIMARY_DARK_BLUE,
		height: 80,
		borderTopLeftRadius: 40,
		borderTopRightRadius: 40,
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 10,
		position: 'absolute',
		bottom: 0,
		width: '100%'
	},
	navBar: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		flex: 1
	},
	primaryNavItems: {
		flexDirection: 'row',
		justifyContent: 'space-evenly',
		alignItems: 'center'
	},
	syncSection: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		backgroundColor: theme.PRIMARY_MEDIUM_BLUE,
		width: 385,
		height: 60,
		borderRadius: 40,
		marginLeft: 10,
		paddingHorizontal: 10
	},
	syncBarButton: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 10
	},
	syncBarText: {
		fontSize: 22,
		color: theme.WHITE,
		textAlign: 'center',
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2,
		marginLeft: 15
	}
});
export default styles;
