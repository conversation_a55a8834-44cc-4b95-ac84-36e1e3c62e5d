import React from 'react';
import { View, Text, TouchableOpacity, PanResponder, PanResponderInstance, GestureResponderEvent } from 'react-native';
import styles from './styles';
import Canvas, { CanvasRenderingContext2D } from 'react-native-canvas';
import { scaleToDesign } from 'utils/design-scale';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import FormComponentError from 'components/Form/FormComponentError';

export interface SignatureCoordinate {
	x: number;
	y: number;
}

export interface SignatureStroke {
	coordinates: Array<SignatureCoordinate>;
}

export interface SignatureDto {
	strokes: Array<SignatureStroke>;
	dots: Array<SignatureCoordinate>;
}

export default class Signature extends React.PureComponent<SignatureProps, State> {
	private _panResponder: PanResponderInstance;
	private _context: CanvasRenderingContext2D;

	private _signature: SignatureDto;
	private _currentStroke: SignatureStroke;

	private _width: number = scaleToDesign(500);
	private _height: number = scaleToDesign(200);

	private _startX: number = 0;
	private _startY: number = 0;
	private _lastOutsideX: number = 0;
	private _lastOutsideY: number = 0;

	constructor(props: SignatureProps) {
		super(props);
		this._signature = {
			strokes: [],
			dots: [],
		};
		this.state = {
		};
		this.redrawSignature = this.redrawSignature.bind(this);
		this.clearCanvas = this.clearCanvas.bind(this);
	}

	componentDidMount() {
		if (this.props.value) {
			this._signature = JSON.parse(this.props.value);
			this.redrawSignature();
		}
	}
	componentWillMount() {
		this._panResponder = PanResponder.create({
			// Ask to be the responder:
			onStartShouldSetPanResponder: () => true,
			onStartShouldSetPanResponderCapture: () => true,
			onMoveShouldSetPanResponder: () => true,
			onMoveShouldSetPanResponderCapture: () => true,

			onPanResponderGrant: (evt, gestureState) => {
				this.props.onFocus?.(null);
				// The gesture has started. Show visual feedback so the user knows
				// what is happening!

				// gestureState.d{x,y} will be set to zero now
				this._startX = evt.nativeEvent.locationX;
				this._startY = evt.nativeEvent.locationY;
				let xPos: number = this._startX + gestureState.dx;
				let yPos: number = this._startY + gestureState.dy;
				this._currentStroke = {
					coordinates: [
						{
							x: xPos,
							y: yPos
						}
					]
				};

				this._context.beginPath();
				this._context.arc(xPos, yPos, 1, 0, 2 * Math.PI, false);
				this._context.fillStyle = 'black';
				this._context.fill();
				this._context.lineWidth = 1;
				this._context.strokeStyle = '#000000';
				(this._context as any).stroke();
				this._signature.dots.push({ x: xPos, y: yPos });

				this._context.beginPath();
				this._context.moveTo(xPos, yPos);
				this._signature.strokes.push(this._currentStroke);
			},
			onPanResponderMove: (evt: GestureResponderEvent, gestureState) => {
				// The most recent move distance is gestureState.move{X,Y}

				// The accumulated gesture distance since becoming responder is
				// gestureState.d{x,y}
				let xPos: number = this._startX + gestureState.dx;
				let yPos: number = this._startY + gestureState.dy;

				if (xPos < 0 || xPos > this._width || yPos < 0 || yPos > this._height) {
					this._lastOutsideX = xPos;
					this._lastOutsideY = yPos;
					if (this._currentStroke) {
						let endPathX: number = this._lastOutsideX;
						let endPathY: number = this._lastOutsideY;
						if (endPathX < 0) {
							endPathX = 0;
						} else if (endPathX > this._width) {
							endPathX = this._width;
						}
						if (endPathY < 0) {
							endPathY = 0;
						} else if (endPathY > this._height) {
							endPathY = this._height;
						}

						this._context.lineTo(endPathX, endPathY);
						(this._context as any).stroke();
						this._currentStroke.coordinates.push({ x: endPathX, y: endPathY });
						this._currentStroke = null;
					}
				} else {
					if (!this._currentStroke) {
						let startPathX: number = this._lastOutsideX;
						let startPathY: number = this._lastOutsideY;
						if (startPathX < 0) {
							startPathX = 0;
						} else if (startPathX > this._width) {
							startPathX = this._width;
						}
						if (startPathY < 0) {
							startPathY = 0;
						} else if (startPathY > this._height) {
							startPathY = this._height;
						}

						let xPos: number = this._startX + gestureState.dx;
						let yPos: number = this._startY + gestureState.dy;
						this._currentStroke = {
							coordinates: [
								{
									x: startPathX,
									y: startPathY
								},
								{
									x: xPos,
									y: yPos
								}
							]
						};
						this._context.beginPath();
						this._context.moveTo(startPathX, startPathY);
						this._context.lineTo(xPos, yPos);
						this._signature.strokes.push(this._currentStroke);
					} else {
						this._currentStroke.coordinates.push({ x: evt.nativeEvent.locationX, y: evt.nativeEvent.locationY });
						this._context.lineTo(xPos, yPos);
						(this._context as any).stroke();
					}
				}
			},
			onPanResponderTerminationRequest: () => true,
			onPanResponderRelease: () => {
				// The user has released all touches while this view is the
				// responder. This typically means a gesture has succeeded
				if (this._currentStroke) {
					this._currentStroke = null;
				}
				this.props.onChange(JSON.stringify(this._signature));
				this.props.onBlur?.(null);
			},
			onPanResponderTerminate: () => {
				if (this._currentStroke) {
					this._currentStroke = null;
				}
				// Another component has become the responder, so this gesture
				// should be cancelled
				this.props.onChange(JSON.stringify(this._signature));
				this.props.onBlur?.(null);
			},
			onShouldBlockNativeResponder: () => {
				// Returns whether this component should block native components from becoming the JS
				// responder. Returns true by default. Is currently only supported on android.
				return true;
			},
		});
	}

	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required} />
						<View {...this._panResponder.panHandlers} style={[styles.canvasView, { height: this._height, width: this._width }]}>
							<Text style={[styles.signatureText]}>Signature</Text>
							<Canvas style={{ width: this._width, height: this._height }} ref={this.handleCanvas} />
						</View>
					<TouchableOpacity style={[styles.clearButton]} onPress={this.clearCanvas}>
						<Text style={[styles.clearText]}>Clear</Text>
					</TouchableOpacity>
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private redrawSignature() {
		if (!this._context) {
			return;
		}
		this._context.clearRect(0, 0, this._width, this._height);
		this._signature.strokes.forEach(stroke => {
			stroke.coordinates.forEach((coordinate, index) => {
				if (index === 0) {
					this._context.beginPath();
					this._context.moveTo(coordinate.x, coordinate.y);
				} else {
					this._context.lineTo(coordinate.x, coordinate.y);
				}
			});
			(this._context as any).stroke();
		});
		this._signature.dots.forEach(dot => {

			this._context.beginPath();
			this._context.arc(dot.x, dot.y, 1, 0, 2 * Math.PI, false);
			this._context.fillStyle = 'black';
			this._context.fill();
			this._context.lineWidth = 1;
			this._context.strokeStyle = '#000000';
			(this._context as any).stroke();

		});
	}
	private handleCanvas = (canvas: any) => {
		if (!canvas) {
			this._context = null;
			return;
		}
		this._context = canvas.getContext('2d');
		canvas.width = this._width;
		canvas.height = this._height;
	};
	private clearCanvas() {
		if (this._context) {
			this._context.clearRect(0, 0, this._width, this._height);
			this.props.onChange(null);
			this.props.onBlur?.(null);
		}
	}
}

export interface SignatureProps extends CommonFormComponentProps<string> {
	overrideStyles?: {
		wrapper: object;
		//styles that go on the View that wraps the label
		labelWrapper?: object,
		//styles tyhat go on the label Text element
		labelText?: object,
	}
}

interface State {
}