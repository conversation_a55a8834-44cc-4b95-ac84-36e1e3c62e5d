import React from 'react';
import Svg, { G, <PERSON>, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class ScheduledFormIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE,
		size: 35
	};

	render() {
		return (
			<Svg width={scaleToDesign(this.props.size) + 'px'} height={scaleToDesign(this.props.size) + 'px'} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path d="M8.12873721,1 L18.3104173,1 L18.3104173,1 L18.3104173,9.56406604 C18.3104173,11.2209203 19.653563,12.564066 21.3104173,12.564066 L29.8712628,12.564066 L29.8712628,12.564066 L29.8712628,31 C29.8712628,32.6568542 28.528117,34 26.8712628,34 L8.12873721,34 C6.47188296,34 5.12873721,32.6568542 5.12873721,31 L5.12873721,4 C5.12873721,2.34314575 6.47188296,1 8.12873721,1 Z" stroke={this.props.colour} strokeWidth="2" />
					<Line x1="18.3104173" y1="1" x2="29.8712628" y2="12.564066" stroke={this.props.colour} strokeWidth="2" />
					<Line x1="11.6546287" y1="18.3995167" x2="23.3453713" y2="18.3995167" stroke={this.props.colour} strokeWidth="2" />
					<Line x1="11.6546287" y1="23.5991903" x2="23.3453713" y2="23.5991903" stroke={this.props.colour} strokeWidth="2" />
					<Line x1="11.6546287" y1="28.7988639" x2="23.3453713" y2="28.7988639" stroke={this.props.colour} strokeWidth="2" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
	size: number;
}
