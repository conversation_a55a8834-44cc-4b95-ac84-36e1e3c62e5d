import React from 'react';
import Svg, { G, Path, Polyline, Circle, Rect, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class ClockIcon extends React.PureComponent<Props, never> {
    static defaultProps = {
        colour: theme.PRIMARY_LIGHT_BLUE
    };

    render() {
        let width: string = scaleToDesign(30) + "px";
		let height: string = scaleToDesign(30) + "px";
        return (
            <Svg width={width} height={height} viewBox="0 0 35 35">
                <G id="icon/time" stroke="none" strokeWidth="2" fill="none" fill-rule="evenodd" strokeLinecap="round" strokeLinejoin="round">
                    <Circle id="Oval-2" stroke={this.props.colour} strokeWidth="2" cx="17.5" cy="17.5" r="16.5"></Circle>
                    <Polyline id="Path-5" stroke={this.props.colour} strokeWidth="2" points="23.5 23.5 17.5 17.5 17.5 9.5"></Polyline>
                </G>
            </Svg>
		);
    }
}

interface Props {
    colour: string;
}