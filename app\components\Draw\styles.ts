import { StyleSheet } from 'react-native';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        flexDirection: 'column',
        padding: 20,
    },
    canvasView: {
        overflow: "hidden",
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#EEEEEE',
        borderRadius: 5,
    },
    signatureText: {
        fontSize: 16,
        color: '#888888',
        position: 'absolute',
        top: 10,
        left: 10
    },
    clearButton: {
        height: 36,
        width: 54,
        borderRadius: 6,
        backgroundColor: '#0054A2',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10,
    },
    clearText: {
        fontSize: 16,
        color: 'white',
    }
});

export default styles;
