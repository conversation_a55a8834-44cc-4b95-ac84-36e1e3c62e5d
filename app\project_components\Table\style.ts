import { StyleSheet } from 'react-native';
import theme from 'config/styles/theme';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	root: {
		backgroundColor: theme.WHITE,
		flex: 1
	},
	header: {
		flexDirection: 'row',
		paddingHorizontal: 20
	},
	headerCell: {
		height: 40,
		flex: 1
	},
	row: {
		flexDirection: 'row',
		backgroundColor: '#FAFAFA',
		borderRadius: 15,
		height: 53,
		marginVertical: 5,
		paddingHorizontal: 20
	},
	rowCell: {
		justifyContent: 'center',
		alignItems: 'center',
		height: 53,
		textAlign: 'center',
		textAlignVertical: 'center'
	}
});

export default styles;