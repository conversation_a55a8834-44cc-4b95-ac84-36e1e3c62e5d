import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';

export default class Radio<T> extends React.PureComponent<RadioProps<T>, never> {
	render() {
		let valuesPerRow: number = this.props.valuesPerRow ? this.props.valuesPerRow : 3;
		let rowCount: number = Math.floor((this.props.values.length + valuesPerRow - 1) / valuesPerRow);
		let rows: Array<React.ReactNode> = [];
		for (var ii = 0; ii < rowCount; ii++) {
			rows.push(this.renderRow(this.props.values.slice(ii * valuesPerRow, (ii + 1) * valuesPerRow), ii));
		}
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				{rows.map(s => s)}
				<FormComponentError error={this.props.error} />
			</View>
		);
	}

	private valueSelected(newValue: T) {
		this.props.onFocus?.(null);
		this.props.onChange(newValue);
		this.props.onBlur?.(null);
	}
	private renderRow(values: Array<T>, key: any): React.ReactNode {
		return (
			<View style={[styles.rowWrapper, this.props.overrideStyles?.rowWrapper]} key={key}>
				{values.map((value, index) =>
					<TouchableOpacity key={this.props.keyProp ? (value as any)[this.props.keyProp] : value} onPress={() => this.valueSelected(value)}>
						<View style={[styles.optionWrapper, this.props.overrideStyles?.optionWrapper]}>
							<View style={[styles.radioOuter, this.props.overrideStyles?.radioOuter]}>
								{value == this.props.selected &&
									<View style={[styles.radioInner, this.props.overrideStyles?.radioInner]}>
									</View>
								}
							</View>
							<Text style={[styles.optionText, this.props.overrideStyles?.optionText]}>
								{this.props.displayTransform ? this.props.displayTransform(value) : value}
							</Text>
						</View>
					</TouchableOpacity>
				)}
			</View>
		);
	}
}

export interface RadioProps<T> extends Omit<CommonFormComponentProps<T>, 'value'> {
	//The list of values that make up the radio button options
	values: Array<T>;
	//The currently selected value - or null if nothing should be selected.
	selected: T;
	//optional function to transform the type of value to a string for display.
	displayTransform?: (value: T) => string;
	//optional amount of radio buttons to show per row, defaults to 3
	valuesPerRow?: number;
	//optional. When looping over the values teh defaulkt key is the index. 
	//supplying this keyProp will use this property of the type T to assign the key
	keyProp?: string;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		//The styles that go on the view that wraps the entire component
		wrapper?: object,
		//Styles that go on the view that wraps each row
		rowWrapper?: object,
		//styles that go on view that wraps each option
		optionWrapper?: object,
		//styles that go on the Text that displays the radio label
		optionText?: object,
		//styles that on the outer circle of the radio
		radioOuter?: object,
		//styles that go on the inner circle of the radio which only displayed when the radio button is selected.
		radioInner?: object
	}
}