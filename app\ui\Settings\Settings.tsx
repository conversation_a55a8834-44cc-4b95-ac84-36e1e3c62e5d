import React from 'react';
import { View, Text, Linking } from 'react-native';
import config from 'config/config';
import { AppStore } from 'app/stores/app-store';
import Database from 'services/database';
import SecurityService from 'services/security';
import { UserDto } from 'dtos/security';
import { StoreComponent } from 'utils/store-component';
import { ConnectionStatus } from 'services/heartbeat';
import ChangePassword from './ChangePassword/ChangePassword';
import AppButton from 'components/AppButton/AppButton';
import HeaderBar from 'components/HeaderBar/HeaderBar';
import BackButton from 'project_components/BackButton/BackButton';
import textStyles from 'config/styles/text-styles';
import SettingsIcon from 'project_components/Icons/SettingsIcon';
import styles from './styles';
import { scaleToDesign } from 'utils/design-scale';
import ChangeTeam from './ChangeTeam/ChangeTeam';
import { string } from 'redi-ui-utils';
import { confirmDialog } from 'components/ConfirmModal/ConfirmModal';
import RNExitApp from 'react-native-exit-app';
import navigator from 'services/navigator';
import ExportData from './ExportData/ExportData';
import ImportData from './ImportData/ImportData';

@StoreComponent({
	connectionStatus: AppStore.connectionStatus$,
	user: AppStore.user$,
	lastModified: AppStore.lastModified$,
	team: AppStore.team$
})
export default class Settings extends React.PureComponent<Props, State> {
	state = {
		showChangePassword: false,
		showChangeTeam: false,
		showExportData: false,
		showImportData: false
	};

	render() {
		return (
			<React.Fragment>
				<HeaderBar>
					<View style={styles.backContainer}>
						<View style={{ width: scaleToDesign(30) }} />
						<BackButton />
					</View>
					<View style={styles.headerContainer}>
						<SettingsIcon colour="#fff" />
						<Text style={{ ...textStyles.large, marginLeft: 16 }}>SETTINGS</Text>
					</View>
					<View style={{ flex: 1 }} />
				</HeaderBar>
				<View style={styles.content}>
					<Text style={styles.username}>{this.props.user?.Username} ({this.props.team ?? 'NO TEAM'})</Text>
					<AppButton
						theme="blue"
						content="Reset App"
						marginVertical={scaleToDesign(15)}
						onPress={() => this.reset()} />
					{this.props.connectionStatus === ConnectionStatus.Connected &&
						<AppButton
							theme="blue"
							content="Change Password"
							marginVertical={scaleToDesign(15)}
							onPress={() => this.setState({ showChangePassword: true })} />
					}
					<AppButton
						theme="blue"
						content="Change Team"
						marginVertical={scaleToDesign(15)}
						onPress={() => this.setState({ showChangeTeam: true })} />
					{/*
					<AppButton
						theme="blue"
						content="Manual Export Data"
						marginVertical={scaleToDesign(15)}
						onPress={() => this.setState({ showExportData: true })} />
					*/}
					<AppButton
						theme="darkblue"
						content="Logout"
						marginVertical={scaleToDesign(15)}
						onPress={() => SecurityService.logout()} />
					<View style={{ flex: 1 }} />
					<Text onPress={() => Linking.openURL(config.appUrl)}>{config.appUrl}</Text>
					<Text>BUILD: {config.buildVersion}</Text>
					<Text>ENV: {config.environment}</Text>
					<Text>User: {this.props.user?.Username} {this.props.user?.UserId}</Text>
					<Text>Email: {this.props.user?.email}</Text>
					<Text>Lastest job change on server: {this.props.lastModified?.toString()} </Text>
					<Text>Connection Status: {this.props.connectionStatus} </Text>
				</View>
				{this.state.showChangePassword &&
					<ChangePassword onClose={() => this.setState({ showChangePassword: false })} />
				}
				{this.state.showChangeTeam &&
					<ChangeTeam onClose={() => this.setState({ showChangeTeam: false })} />
				}
				{this.state.showExportData &&
					<ExportData onClose={() => this.setState({ showExportData: false })} />
				}
				{this.state.showImportData &&
					<ImportData onClose={() => this.setState({ showImportData: false })} />
				}
			</React.Fragment>
		);
	}

	private reset(): void {
		confirmDialog({
			title: 'Confirm Reset App',
			bodyText: 'Are you sure you want to reset the App? This will delete all data, then close the App.',
			confirmText: 'Reset'
		}).then(async () => {
			try {
				await Database.reset();
			} catch (error) {
				console.error(error);
			}
			navigator.go('/reset');
			setTimeout(() => {
				RNExitApp.exitApp();
			}, 3000);
		});
	}
}

interface Props {
	user: UserDto;
	lastModified: Date;
	connectionStatus: ConnectionStatus;
	team: string;
}

interface State {
	showChangePassword: boolean;
	showChangeTeam: boolean;
	showExportData: boolean;
	showImportData: boolean;
}