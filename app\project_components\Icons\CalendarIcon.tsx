import React from 'react';
import Svg, { G, Rect, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class CalendarIcon extends React.PureComponent<Props, never> {
    static defaultProps = {
        colour: theme.PRIMARY_LIGHT_BLUE
    };

    render() {
        let width: string = scaleToDesign(30) + "px";
		let height: string = scaleToDesign(30) + "px";
        return (
            <Svg width={width} height={height} viewBox="0 0 35 35">
                <G id="icon/calendar" stroke="none" strokeWidth="2" fill="none" fill-rule="evenodd" strokeLinecap="round" strokeLinejoin="round">
                    <Rect id="Rectangle" stroke={this.props.colour} strokeWidth="2" x="2.5" y="4" width="30" height="30" rx="3"></Rect>
                    <Line x1="9.5" y1="1.5" x2="9.5" y2="6.5" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="17.5" y1="1.5" x2="17.5" y2="6.5" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="25.5" y1="1.5" x2="25.5" y2="6.5" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="2.5" y1="13" x2="32.5" y2="13" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="16" y1="18" x2="19" y2="18" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="24" y1="18" x2="27" y2="18" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="8" y1="18" x2="11" y2="18" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="16" y1="29" x2="19" y2="29" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="24" y1="29" x2="27" y2="29" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="8" y1="29" x2="11" y2="29" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="16" y1="23.499754" x2="19" y2="23.499754" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="24" y1="23.499754" x2="27" y2="23.499754" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                    <Line x1="8" y1="23.499754" x2="11" y2="23.499754" id="Line" stroke={this.props.colour} strokeWidth="2"></Line>
                </G>
            </Svg>
		);
    }
}

interface Props {
    colour: string;
}