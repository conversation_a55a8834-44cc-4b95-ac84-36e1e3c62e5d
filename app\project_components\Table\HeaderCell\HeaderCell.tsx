import React from 'react';
import { TouchableOpacity } from 'react-native';
import styles from './style';
import ChevronIcon from 'project_components/Icons/ChevronIcon';

export default class HeaderCell extends React.PureComponent<Props, never> {
	render() {
		return (
			<TouchableOpacity
				disabled={!this.props.sortable}
				style={{
					...styles.root,
					...this.props.style,
					justifyContent: this.props.align ?? 'center'
				}}
				onPress={() => this.onPress()}>
				{React.cloneElement(this.props.children, {
					style: {
						...styles.child,
						...this.props.children.props.style
					}
				})}
				{this.props.sortable && this.props.sort !== Sort.None &&
					<ChevronIcon position={this.getPosition()} />
				}
			</TouchableOpacity>
		);
	}

	private onPress(): void {
		switch (this.props.sort) {
			case Sort.None:
				this.props.onSortChange(Sort.Desc);
				break;
			case Sort.Desc:
				this.props.onSortChange(Sort.Asc);
				break;
			case Sort.Asc:
			default:
				this.props.onSortChange(Sort.None);
				break;
		}
	}
	private getPosition(): 'up' | 'down' {
		if (this.props.sort === Sort.Asc) {
			return 'up';
		}
		return 'down';
	}
}

interface Props {
	children: React.ReactElement;
	style: any;
	sort?: Sort;
	sortable: boolean;
	onSortChange(sort: Sort): void;
	align?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
}

export enum Sort {
	None = 'None',
	Desc = 'Desc',
	Asc = 'Asc'
}

