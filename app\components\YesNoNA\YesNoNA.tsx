import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import React from 'react';
import FormComponentLabel from 'components/Form/FormComponentLabel';
import FormComponentError from 'components/Form/FormComponentError';

export enum YesNoNASelection {
	Yes,
	No,
	NA
}

export default class YesNoNA extends React.PureComponent<YesNoNAProps, State> {

	constructor(props: YesNoNAProps) {
		super(props);

		this.state = {
			modalOpen: false
		};
	}

	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<FormComponentLabel
					label={this.props.label}
					required={this.props.required} />
				<View style={[styles.buttonWrapper, this.props.overrideStyles?.buttonWrapper]}>
					<TouchableOpacity style={[
						styles.baseButton, styles.yesButton,
						this.props.overrideStyles?.yesButton,
						this.props.selected === YesNoNASelection.Yes ? { ...styles.yesButtonSelected, ...this.props.overrideStyles?.yesButtonSelected } : {}
					]}
						onPress={() => this.props.onChange(YesNoNASelection.Yes)}>
						<Text style={[styles.baseButtonText, styles.yesButtonText, this.props.overrideStyles?.yesButtonText]}>
							{this.props.yesButtonText || "Yes"}
						</Text>
					</TouchableOpacity>
					<TouchableOpacity style={[
						styles.baseButton, styles.noButton,
						this.props.overrideStyles?.noButton,
						this.props.selected === YesNoNASelection.No ? { ...styles.noButtonSelected, ...this.props.overrideStyles?.noButtonSelected } : {}
					]}
						onPress={() => this.props.onChange(YesNoNASelection.No)}>
						<Text style={[styles.baseButtonText, styles.noButtonText, this.props.overrideStyles?.noButtonText]}>
							{this.props.noButtonText || "No"}
						</Text>
					</TouchableOpacity>
					<TouchableOpacity style={[
						styles.baseButton, styles.naButton,
						this.props.overrideStyles?.naButton,
						this.props.selected === YesNoNASelection.NA ? { ...styles.naButtonSelected, ...this.props.overrideStyles?.naButtonSelected } : {}
					]}
						onPress={() => this.props.onChange(YesNoNASelection.NA)}>
						<Text style={[styles.baseButtonText, styles.naButtonText, this.props.overrideStyles?.naButtonText]}>
							{this.props.naButtonText || "N/A"}
						</Text>
					</TouchableOpacity>
				</View>
				<FormComponentError error={this.props.error} />
			</View>
		);
	}
}

export interface YesNoNAProps {
	//callback that gets called everytime a selecteion is made. The newly selected value is passed in as an argument. The type of this arg is the YesNoSelection enum
	onChange: (selection: YesNoNASelection) => void;
	//the currently selected option or null if ntohing should be selected.
	selected: YesNoNASelection;
	//optional label to display above the buttons
	label?: string;
	//optional ovveride for text to display on the yes button
	yesButtonText?: string;
	//optional override for text to display on the no button
	noButtonText?: string;
	//optional override for text to display on the n/a button
	naButtonText?: string;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		//styles that go on the entire component wrapper view
		wrapper?: object;
		//styles that go on the label text component
		label?: object;
		//styles that go on the view that wraps just teh buttons
		buttonWrapper?: object;
		//styles that go on the yes button TouchableOpacity
		yesButton?: object;
		//styles that go on the yes button text
		yesButtonText?: object;
		//styles that are applied when the yes button is selected.
		yesButtonSelected?: object;
		//styles that go on the no button TouchableOpacity
		noButton?: object;
		//styles that go on the no button text
		noButtonText?: object;
		//styles that are applied when the no button is selected
		noButtonSelected?: object;
		//styles that go on the n/a button TouchableOpacity
		naButton?: object;
		//styles that go on the n/a button text
		naButtonText?: object;
		//styles that are applied when the n/a button is selected
		naButtonSelected?: object;
	};
	error?: string;
	required?: boolean;
}

interface State {
}