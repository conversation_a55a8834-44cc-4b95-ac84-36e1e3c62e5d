import React from 'react';
import { View, Text, ViewStyle } from 'react-native';
import styles from './styles';
import textStyles from 'config/styles/text-styles';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class BFMIcon extends React.PureComponent<Props, never> {
	render() {
		const color = this.props.rating === 'LOW' ? theme.PRIMARY_GREEN : '#E9190F';

		if(this.props.rating == undefined || this.props.rating != null || this.props.rating != "") {
			return null;
		}

		return (
			<View style={{
				...styles.box,
				...this.props.style,
				backgroundColor: color,
				width: scaleToDesign(76)
			}}>
				<Text style={textStyles.medium}>BFM: {this.props.rating?.slice(0, 1)}</Text>
			</View>
		);
	}
}

interface Props {
	rating: 'LOW' | string;
	style?: ViewStyle;
}