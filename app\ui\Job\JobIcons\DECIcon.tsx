import React from 'react';
import { View, Text, ViewStyle } from 'react-native';
import styles from './styles';
import textStyles from 'config/styles/text-styles';

export default class DECIcon extends React.PureComponent<Props, never> {
	render() {
		return (
			<View style={{
				...styles.box,
				...this.props.style,
				backgroundColor: '#801A86'
			}}>
				<Text style={textStyles.medium}>DEC</Text>
			</View>
		);
	}
}

interface Props {
	style?: ViewStyle;
}