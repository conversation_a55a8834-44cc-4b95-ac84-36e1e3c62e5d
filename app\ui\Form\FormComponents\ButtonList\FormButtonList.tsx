import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput, TextInputProps } from 'react-native';
import { WrappedFieldInterface } from 'redi-form';
import { OmitName } from 'redi-types';
import ButtonList from 'components/ButtonList/ButtonList';

export default class FormButtonList extends React.Component<WrappedFieldInterface<string, FormButtonListProps>> {

    constructor(props: any) {
        super(props);

        this.onChange = this.onChange.bind(this);
    }

    onChange(value: string){
        this.props.fieldProps.onChange(value);
    }

	render() {
        const props = this.props.componentProps();

		return (
			<ButtonList<string>
                {...props}
                values={this.props.values}
                selected={this.props.fieldProps.value}
                onChange={this.onChange}>
			</ButtonList>
		);
	}
}

export interface FormButtonListProps {
    values: Array<string>;
}

interface State {

}