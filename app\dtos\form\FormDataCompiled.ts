declare module "redi-types" {
	export interface FormDataCompiled {
		imageCompression: {
			enabled: boolean;
			maxHeightWidth?: number;
			qualityPercent: number;
		};
		logic?: FormLogicCompiled[];
		pages: FormDataPageCompiled[];
	}

	export interface FormDataPageCompiled {
		pageName: string;
		/** User avaliable/defined page id */
		textId: string;
		/** not available on the UI. used as a uuid when pageId has not yet been added/created */
		pageId: string;
		canRepeat: boolean;
		hide: boolean;
		sections: FormDataSectionCompiled[];
	}

	export interface FormDataSectionCompiled {
		sectionId: string;
		sectionName: string;
		textId: string;
		canRepeat: boolean;
		fields: (FormDataFieldCompiled | FormDataRepeatingFieldCompiled)[];
		hide: boolean;
	}

	export interface FormDataRepeatingFieldCompiled {
		repeatingFieldId: string;
		label: string;
		textId: string;
		fieldType: FormFieldTypesRepeating;
		rows: FormDataFieldRowCompiled[];
		hide: boolean;

		options: FormDataRepeatingField["options"];
	}

	export interface FormDataFieldRowCompiled {
		rowId: string;
		textId: string;
		rowIndex: number;
		fields: (FormDataFieldCompiled | FormDataRepeatingFieldCompiled)[];
		hide: boolean;

		options: FormDataFieldRow["options"];
	}

	export interface FormDataFieldCompiled {
		fieldId: string;
		label: string;
		textId: string;
		fieldType: FormFieldTypes;
		isRequired: boolean;
		hide: boolean;
		value?: any;

		/** a dummy prop to store the values of preview */
		preview?: FormSubmissionField;

		options: FormDataField["options"];
	}

	export interface FormLogicCompiled {
		/** Fields to compare */
		ids: string[];
		runOnLoad?: boolean;
		logic: CompiledAction;
    }
}
