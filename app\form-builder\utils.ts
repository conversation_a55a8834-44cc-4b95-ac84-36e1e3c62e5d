import { CompiledAction, FormLogicCompiled } from 'redi-types';
import { LogicCompiler } from 'forms-logic-compiler';

export function showWhenGreaterOrEqual(showId: string, triggerId: string, greaterOrEqualToValue: number): FormLogicCompiled {
	let script = `#if($getValue("${triggerId}") >= ${greaterOrEqualToValue}){
        $show("${showId}");
    }#else{
        $hide("${showId}");
    }`;

	const compiler = new LogicCompiler(script);


	const compiledScript = compiler.compile() as CompiledAction;


	return {
		ids: [triggerId],
		logic: compiledScript
	};
}

export function showWhenAll(showId: string, triggers: Array<LogicTrigger>): FormLogicCompiled {
	let triggerIds: Array<string> = [];

	let script = '#if(';
	for (var ii = 0; ii < triggers.length; ii++) {
		let wkTrigger: LogicTrigger = triggers[ii];
		if (ii > 0) {
			script += ' && ';
		}

		if (typeof wkTrigger.value === 'string') {
			script += `$getValue("${wkTrigger.fieldId}") == "${wkTrigger.value}"`;
		} else {
			script += `$getValue("${wkTrigger.fieldId}") == ${wkTrigger.value}`;
		}
		triggerIds.push(wkTrigger.fieldId);
	}

	script += `){
        $show("${showId}");
    }#else{
        $hide("${showId}");
    }`;


	const compiler = new LogicCompiler(script);

	const compiledScript = compiler.compile() as CompiledAction;

	return {
		ids: triggerIds,
		logic: compiledScript
	};
}


export function hideWhenAll(hideId: string, triggers: Array<LogicTrigger>): FormLogicCompiled {
	let triggerIds: Array<string> = [];

	let script = '#if(';
	for (var ii = 0; ii < triggers.length; ii++) {
		let wkTrigger: LogicTrigger = triggers[ii];
		if (ii > 0) {
			script += ' && ';
		}

		if (typeof wkTrigger.value === 'string') {
			script += `$getValue("${wkTrigger.fieldId}") == "${wkTrigger.value}"`;
		} else {
			script += `$getValue("${wkTrigger.fieldId}") == ${wkTrigger.value}`;
		}
		triggerIds.push(wkTrigger.fieldId);
	}

	script += `){
        $hide("${hideId}");
    }#else{
        $show("${hideId}");
    }`;


	const compiler = new LogicCompiler(script);

	const compiledScript = compiler.compile() as CompiledAction;

	return {
		ids: triggerIds,
		logic: compiledScript
	};
};

interface LogicTrigger {
	fieldId: string;
	value: any;
}
