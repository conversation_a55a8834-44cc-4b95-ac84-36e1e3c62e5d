import { FormSubmissionDto } from 'redi-types';
import { ShowWhen, ShowWhenGreaterThan} from 'ui/Form/visibilty';


// reinforcing info page
export const polePickId = 'polePickId';
export const companyId = 'companyId';
const trussSpecificationId = 'trussSpecificationId';
const woodSpeciesId = 'woodSpeciesId';
const poleDiameterId = 'poleDiameterId';
const pipInformationId = 'pipInformationId';

// workflow no page
const pictureOfPoleIdId = 'pictureOfPoleIdId';
const currentDateId = 'currentDateId';
const currentTimeId = 'currentTimeId';
const gpsLocationId = 'gpsLocationId';
const canProceedId = 'canProceedId';
const earthWiringId = 'earthWiringId';
const overallPhotoId = 'overallPhotoId';
const photoEarthWiringConfigurationId = 'photoEarthWiringConfigurationId';
const photo1ReasonForNotProceedingId = 'photo1ReasonForNotProceedingId';
const photo2ReasonForNotProceedingId = 'photo2ReasonForNotProceedingId';
const photo3ReasonForNotProceedingId = 'photo3ReasonForNotProceedingId';
const selectReasonForNotProceedingId = 'selectReasonForNotProceedingId';
const phoneNOCCId = 'phoneNOCCId';
const inspectorsCommentsId = 'inspectorsCommentsId';
const pleaseEnterYourNameId = 'pleaseEnterYourNameId';
const pleaseProvideSignatureId = 'pleaseProvideSignatureId';

// workflow yes
// section id
const infoSectionId = 'infoSectionId';
const workflowYesSectionId = 'workflowYesSectionId';
const workflowNoSectionId = 'workflowNoSectionId';
const finishSectionId = 'finishSectionId';


// field id's
const areThereCablesId = 'areThereCablesId';
const selectCorrectPoleSpeciesId = 'selectCorrectPoleSpeciesId';
const enterGroundlineDiamaterId = 'enterGroundlineDiamaterId';
const selectDesignSpecificationId = 'selectDesignSpecificationId';
const soundTestPoleId = 'soundTestPoleId';
const haveYouCheckedDbydId = 'haveYouCheckedDbydId';
const doesPoleRequireHoldingId = 'doesPoleRequireHoldingId';


const recordGoodWoodReadingId = 'recordGoodWoodReadingId';
const capturePhotoOfExistingReinforcingId = 'capturePhotoOfExistingReinforcingId';
const howManyReinforcingsRemovedId = 'howManyReinforcingsRemovedId';
const typeOfReinforcingRemovedId = 'typeOfReinforcingRemovedId';
const confirmReinforcementOrientationDeviationId = 'confirmReinforcementOrientationDeviationId';
const photoChainWinchAtBaseOfPoleId = 'photoChainWinchAtBaseOfPoleId';
const confirmReinforcementOrientationId = 'confirmReinforcementOrientationId';
const confirmReinforcingInstalledId = 'confirmReinforcingInstalledId';
const siteCleanId = 'siteCleanId';

const photo1CVTId = 'photo1CVTId';
const photo2CVTId = 'photo2CVTId';
const photo3CVTId = 'photo3CVTId';


const bayLength1Id = 'bayLength1Id';
const bayAngle1Id = 'bayAngle1Id';
const bayLength2Id = 'bayLength2Id';
const bayAngle2Id = 'bayAngle2Id';
const bayLength3Id = 'bayLength3Id';
const bayAngle3Id = 'bayAngle3Id';
const bayLength4Id = 'bayLength4Id';
const bayAngle4Id = 'bayAngle4Id';

const capturePhotoEarthWireConfigurationId = 'capturePhotoEarthWireConfigurationId';
const closeUpTrussPhotoId = 'closeUpTrussPhotoId';
const photoSecondTrussId = 'photoSecondTrussId';
const attachPoleReinforcementPlateId = 'attachPoleReinforcementPlateId';
const confirmGrabRemovedId = 'confirmGrabRemovedId';
const confirmGrabRemovedPhotoId = 'confirmGrabRemovedPhotoId';

const areWoodSpeciesAndGroundlineDiamaterCorrectId = 'areWoodSpeciesAndGroundlineDiamaterCorrectId';
const doesPoleHaveExistingReinforcingId = 'doesPoleHaveExistingReinforcingId';
const confirmPrescribedOrientationMetId = 'confirmPrescribedOrientationMetId';
const howManyBaysId = 'howManyBaysId';

const bandingSet1PhotoId = 'bandingSet1PhotoId';
const bandingSet2PhotoId = 'bandingSet2PhotoId';

export const reinforcingInspForm: Partial<FormSubmissionDto> = {
	data: {
		pages: [
			{
				textId: 'page1',
				label: 'Logsys Reinforcing Workflow2',
				sections: [
					{
						textId: infoSectionId,
						label: 'Reinforcing Info',
						fields: [
							{
								textId: polePickId,
								fieldType: 'Text',
								options: {
									required: true,
									placeholder: 'Pole Pick ID'
								}
							},
							{
								textId: companyId,
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Company',
									choices: ['Western Power', 'Aurora Energy NZ', 'Ausnet', 'Jemena', 'Unison Power', 'Wellington Electricity', 'PowerNet', 'SA Power', 'PowerCo', 'Vline']
								}
							},
							{
								textId: trussSpecificationId,
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Truss Specification',
									choices: [
										"C-5100-5","C-5100-5 D","C-1080","C-1080 D","C-1180","C-1180 D","C-1280","C-1280 D",
										"C-1380","C-1380 D","C2-3610-5","C2-3610-5 D","C2-4910-5","C2-4910-5 D","C2-5610-5",
										"C2-5610-5 D","C2-7110-6","C2-7110-6 D","C2-3613-5","C2-3613-5 D","C2-4913-5","C2-4913-5 D",
										"C2-5613-5","C2-5613-5 D","C2-7113-6","C2-7113-6 D"
									]
								}
							},
							{
								textId: woodSpeciesId,
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Wood Species',
									choices: ['Blackbutt', 'Blackbutt WA', 'Blood Wood Red', 'Broadleaf Red Iron Bark', 'Cadaga', 'Cooktown Iron Bark',
									"Crow's Ash", 'Eastern Blackbutt', 'Grebox', 'Grey Gum / Gum Grey', 'Grey Iron Bark', 'Grey Mahogany', 'Gympie Messmate',
									'Hickory Ash', 'Iron Bark', 'Jarrah', 'Karri', 'Maritime Pine', 'Pinus Sylvestric / Baltic / Scotts', 'Radiata Pine',
									'Red Iron Bark', 'Red Mahogany', 'Rose Gum', 'Scotts Pine', 'Silvertop Stringy', 'Slash Pine', 'Spotted Gum',
									'Stringy Bark', 'Tallonwood', 'Tingle Wood', 'Unknown', 'Wandoo', 'White Mahogany', 'Yellow Stringy Bark']
								}
							},
							{
								textId: poleDiameterId,
								fieldType: 'Text',
								options: {
									required: true,
									placeholder: 'Pole Diameter at Ground Line'
								}
							},
							{
								textId: pipInformationId,
								fieldType: 'Label',
								value: 'PIP INFO HERE asdfjalsdfjasldfj asdfj alsdf',
								label: 'PIP Information'
							},
							{
								textId: pictureOfPoleIdId,
								label: 'Picture of Pole ID',
								fieldType: 'Photo',
								options: {
									required: true,
									photoMaxHeight: 500,
									photoMaxWidth: 500
								}
							},
							{
								textId: currentDateId,
								label: 'Date',
								fieldType: 'Date',
								options: {
									required: true
								}
							},
							{
								textId: currentTimeId,
								label: 'Time',
								fieldType: 'Time',
								options: {
									required: true
								}
							},
							{
								textId: gpsLocationId,
								label: 'Location (stand next to the pole and capture exact location)',
								fieldType: 'GPS',
								options: {
									required: true
								}
							},
							{
								textId: canProceedId,
								label: 'Can you proceed?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							}
						]
					},
					{
						textId: workflowNoSectionId,
						label: 'Workflow No',
						visibilityConditions: [
							ShowWhen(canProceedId, false),
							ShowWhen(infoSectionId, true)
						],
						shouldAllVisibilityConditionsBeMet: true,
						fields: [
							{
								textId: photo1ReasonForNotProceedingId,
								label: 'Photo 1 of the reason for not being able to proceed',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photo2ReasonForNotProceedingId,
								label: 'Photo 2 of the reason for not being able to proceed',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photo3ReasonForNotProceedingId,
								label: 'Photo 3 of the reason for not being able to proceed',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: selectReasonForNotProceedingId,
								label: 'Select the reason for not proceeding. If reason is not on the list then please provide one.',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Reason',
									choices: ['Already Staked', 'Broken Earth', 'Burnt at Ground', 'DNX (EQNI)', 'Fungus Growth', 'Ground too Hard',
										'HV no AV', 'Insufficient Good Wood', 'Knot Deterioration', 'Lightning Damage', 'NREQ', 'New Pole Installed',
										'No Access - Boggy', 'No Access - Crop', 'No Access - Fence', 'No Access - Terrain', 'No Access - Vegetation',
										'No Access - Wet', 'Non-Wood Pole', 'Pole too Skinny', 'Pole top Defects', 'Too many Assets', 'Underground Assets',
										'Unsafe', 'Other - See comments']
								}
							},
							{
								textId: phoneNOCCId,
								label: 'If it is UNSAFE, Phone NOCC on 13 13 51 and record the reference number that is provided',
								fieldType: 'Text',
								options: {
									placeholder: 'Reference Number'
								}
							}
						]
					},
					{
						textId: workflowYesSectionId,
						label: 'Workflow Yes',
						visibilityConditions: [
							ShowWhen(canProceedId, true),
							ShowWhen(infoSectionId, true)
						],
						shouldAllVisibilityConditionsBeMet: true,
						fields: [
							{
								textId: earthWiringId,
								label: 'Is there an earth wiring configuration on the pole?',
								fieldType: 'YesNo',
								options: {
									required: true
								},
								visibilityConditions: [ShowWhen(companyId, 'Aurora Energy NZ')]
							},
							{
								textId: photoEarthWiringConfigurationId,
								label: 'Take a photo of the earth wiring configuration of the pole before commencing work.',
								fieldType: 'Photo',
								options: {
									required: true
								},
								visibilityConditions: [ShowWhen(earthWiringId, true)]
							},
							{
								textId: areThereCablesId,
								label: 'Are there Cables? Select the correct combination from the list',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Cables',
									choices: ["HV and I don't have a VA", 'HV Cables and I have a VA', 'LV Cables', 'None']
								}
							},
							{
								textId: areWoodSpeciesAndGroundlineDiamaterCorrectId,
								label: 'Are the Wood Species and Groundline Diameter Correct?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: selectCorrectPoleSpeciesId,
								label: 'Select the correct pole species',
								fieldType: 'Dropdown',
								visibilityConditions: [ShowWhen(areWoodSpeciesAndGroundlineDiamaterCorrectId, false)],
								options: {
									required: true,
									placeholder: 'Wood Species',
									choices: ['Blackbutt', 'Blackbutt WA', 'Blood Wood Red', 'Broadleaf Red Iron Bark', 'Cadaga', 'Cooktown Iron Bark',
										"Crow's Ash", 'Eastern Blackbutt', 'Grebox', 'Grey Gum / Gum Grey', 'Grey Iron Bark', 'Grey Mahogany', 'Gympie Messmate',
										'Hickory Ash', 'Iron Bark', 'Jarrah', 'Karri', 'Maritime Pine', 'Pinus Sylvestric / Baltic / Scotts', 'Radiata Pine',
										'Red Iron Bark', 'Red Mahogany', 'Rose Gum', 'Scotts Pine', 'Silvertop Stringy', 'Slash Pine', 'Spotted Gum',
										'Stringy Bark', 'Tallonwood', 'Tingle Wood', 'Unknown', 'Wandoo', 'White Mahogany', 'Yellow Stringy Bark']
								}
							},
							{
								textId: enterGroundlineDiamaterId,
								label: 'Enter the correct Groundline Diameter',
								fieldType: 'Number',
								visibilityConditions: [ShowWhen(areWoodSpeciesAndGroundlineDiamaterCorrectId, false)],
								options: {
									required: true,
									placeholder: 'Diameter'
								}
							},
							{
								textId: selectDesignSpecificationId,
								label: 'Select the new design specification from the truss to pole sizing',
								fieldType: 'Dropdown',
								visibilityConditions: [ShowWhen(areWoodSpeciesAndGroundlineDiamaterCorrectId, false)],
								options: {
									required: true,
									placeholder: 'Specification',
									choices: ['C-1380', 'C-1380 D', 'C-1280', 'C-1280 D', 'C-1180', 'C-1180 D', 'C-1080', 'C-1080D',
										'C2-7110-6', 'C2-71106 D', 'C2-5610-5', 'C2-5610-5 D', 'C2-4910-5', 'C-4910-5 D', 'C2-3610-5', 'C2-3610-5 D',
										'C2-5100-5', 'C2-5100-5 D', 'C2-7113-6', 'C2-7113-6 D', 'C2-5613-5', 'C2-5613-5 D', 'C2-4913-5', 'C2-4913-5 D',
										'C2-4913-5 D', 'C2-3613-5', 'C2-3613-5 D']
								}
							},
							{
								textId: soundTestPoleId,
								label: 'Sound test the pole from the groundline to limit of reach and confirm. If the sound check FAIL, then investigate the Good Wood from existing inspection holes and record Good Wood reading.',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Sound Check',
									choices: ['Sound Check OK', 'Sound Check Fail']
								}
							},
							{
								textId: recordGoodWoodReadingId,
								label: 'Record Good Wood Reading at 1m or above (mm)',
								fieldType: 'Number',
								visibilityConditions: [ShowWhen(soundTestPoleId, 'Sound Check Fail')],
								options: {
									required: true,
									placeholder: 'Good Wood Reading'
								}
							},
							{
								textId: haveYouCheckedDbydId,
								label: 'Have you checked your DBYD documentation?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: doesPoleRequireHoldingId,
								label: 'Does the pole require holding with the pole grab?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: doesPoleHaveExistingReinforcingId,
								label: 'Does the pole have existing reinforcing?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: capturePhotoOfExistingReinforcingId,
								label: 'Capture a photo of all the existing reinforcing',
								fieldType: 'Photo',
								visibilityConditions: [ShowWhen(doesPoleHaveExistingReinforcingId, true)],
								options: {
									required: true
								}
							},
							{
								textId: howManyReinforcingsRemovedId,
								label: 'How many reinforcings have been removed?',
								fieldType: 'Dropdown',
								visibilityConditions: [ShowWhen(doesPoleHaveExistingReinforcingId, true)],
								options: {
									required: true,
									placeholder: 'Reinforcings Removed',
									choices: ['1', '2', '3', '4']
								}
							},
							{
								textId: typeOfReinforcingRemovedId,
								label: 'What type of Reinforcing has been removed?',
								fieldType: 'Dropdown',
								visibilityConditions: [ShowWhen(doesPoleHaveExistingReinforcingId, true)],
								options: {
									required: true,
									placeholder: 'Reinforcing Type',
									choices: ['H-Irons', 'Angle Irons', 'Other']
								}
							},
							{
								textId: overallPhotoId,
								label: 'Capture an overall photo of the pole. If the GRAB is required please show it in the photo attached to the pole.',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photoChainWinchAtBaseOfPoleId,
								label: 'Capture a photo of the chain winch at the base of the pole with the truss in place, before driving the truss into the ground.',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: confirmPrescribedOrientationMetId,
								label: 'Confirm if the prescribed orientation was met?',
								fieldType: 'YesNo',
								options: {
									required: true,
								}
							},
							{
								textId: confirmReinforcementOrientationId,
								label: 'Confirm reinforcement orientation?',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Orientation',
									choices: ['Angle no stay', 'Angle no stay with tap', 'Angle with stay', 'Angle with stay with tap', 'Corner multiple stay',
										'Corner one stay', 'Dead end no stay', 'Dead end stay', 'In line', 'In line four way', 'In line with tap', 'Stay']
								}
							},
							{
								textId: confirmReinforcementOrientationDeviationId,
								label: 'Confirm reinforcement orientation deviation?',
								fieldType: 'Dropdown',
								visibilityConditions: [ShowWhen(confirmPrescribedOrientationMetId, false)],
								options: {
									required: true,
									placeholder: 'Deviation',
									choices: ['0 - 15 Deg', '15 - 30 Deg', 'Other']
								}
							},
							{
								textId: confirmReinforcingInstalledId,
								label: 'Confirm the reinforcement to be installed.',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Reinforcement',
									choices: ['C-1380', 'C-1380 D', 'C-1280', 'C-1280 D', 'C-1180', 'C-1180 D', 'C-1080', 'C-1080D',
										'C2-7110-6', 'C2-71106 D', 'C2-5610-5', 'C2-5610-5 D', 'C2-4910-5', 'C-4910-5 D', 'C2-3610-5', 'C2-3610-5 D',
										'C2-5100-5', 'C2-5100-5 D', 'C2-7113-6', 'C2-7113-6 D', 'C2-5613-5', 'C2-5613-5 D', 'C2-4913-5', 'C2-4913-5 D',
										'C2-4913-5 D', 'C2-3613-5', 'C2-3613-5 D']
								}
							},
							{
								textId: howManyBaysId,
								label: 'How many bays are there?',
								fieldType: 'Dropdown',
								options: {
									required: true,
									placeholder: 'Bays',
									choices: ['1', '2', '3', '4']
								}
							},
							{
								textId: bayLength1Id,
								label: 'Measure bay length 1 using your range finder.',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 0)],
								options: {
									required: true,
									placeholder: 'Bay Length 1'
								}
							},
							{
								textId: bayAngle1Id,
								label: 'Measure bay angle 1 (Bay 1 is always 0 Degrees)',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 0)],
								options: {
									required: true,
									placeholder: 'Bay Angle 1'
								}
							},
							{
								textId: bayLength2Id,
								label: 'Measure bay length 2 using your range finder.',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 1)],
								options: {
									required: true,
									placeholder: 'Bay Length 2'
								}
							},
							{
								textId: bayAngle2Id,
								label: 'Measure bay angle 2',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 1)],
								options: {
									required: true,
									placeholder: 'Bay Angle 2'
								}
							},
							{
								textId: bayLength3Id,
								label: 'Measure bay length 3 using your range finder.',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 2)],
								options: {
									required: true,
									placeholder: 'Bay Length 3'
								}
							},
							{
								textId: bayAngle3Id,
								label: 'Measure bay angle 3',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 2)],
								options: {
									required: true,
									placeholder: 'Bay Angle 3'
								}
							},
							{
								textId: bayLength4Id,
								label: 'Measure bay length 4 using your range finder.',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 3)],
								options: {
									required: true,
									placeholder: 'Bay Length 4'
								}
							},
							{
								textId: bayAngle4Id,
								label: 'Measure bay angle 4',
								fieldType: 'Number',
								visibilityConditions: [ShowWhenGreaterThan(howManyBaysId, 3)],
								options: {
									required: true,
									placeholder: 'Bay Angle 4'
								}
							},
							{
								textId: confirmGrabRemovedId,
								label: 'Has the Grab been removed from the Pole?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: confirmGrabRemovedPhotoId,
								label: 'Confirm with a photo that the grab has been removed.',
								fieldType: 'Photo',
								visibilityConditions: [ShowWhen(confirmGrabRemovedId, true)],
								options: {
									required: true
								}
							},
							{
								textId: photo1CVTId,
								label: 'Take Photo 1 using WiFi camera and CVT as required. (Minimum photo size 8MP).',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photo2CVTId,
								label: 'Take Photo 2 using WiFi camera and CVT as required. (Minimum photo size 8 MP)',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photo3CVTId,
								label: 'Take Photo 3 using WiFi camera and CVT as required. (Minimum photo size 8MP)',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: attachPoleReinforcementPlateId,
								label: 'Attach pole reinforcement plate on pole using approved roof screw. Stamp the Pole ID, Month, Year and truss type. All markings must be stamped with a punch kit. No ink. Capture a photo of the installed plate',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: siteCleanId,
								label: 'Have you left the site Clean?',
								fieldType: 'YesNo',
								options: {
									required: true
								}
							},
							{
								textId: closeUpTrussPhotoId,
								label: 'Capture a close up of the truss. If two trusses are installed capture a single truss per photo.',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: photoSecondTrussId,
								label: 'Photo of second truss',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: 'captureCloseUpLabelId',
								fieldType: 'Label',
								label: 'Capture photos',
								dontClearOnHide: true,
								value: 'Capture a close up photo of each banding set showing the seals. (2 Photos)'
							},
							{
								textId: bandingSet1PhotoId,
								label: 'Photo 1',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: bandingSet2PhotoId,
								label: 'Photo 2',
								fieldType: 'Photo',
								options: {
									required: true
								}
							},
							{
								textId: capturePhotoEarthWireConfigurationId,
								label: 'Capture a photo of the earth wire configuration of the pole after the reinforcing is installed.',
								fieldType: 'Photo'
							}
						]
					},
					{
						textId: finishSectionId,
						label: 'Finish',
						visibilityConditions: [
							ShowWhen(workflowYesSectionId, true),
							ShowWhen(workflowNoSectionId, true)
						],
						fields: [
							{
								textId: inspectorsCommentsId,
								label: "Inspector's Comments",
								fieldType: 'TextMultiLine',
								options: {
									placeholder: 'Comments'
								}
							},
							{
								textId: pleaseEnterYourNameId,
								label: 'Please enter your Name',
								fieldType: 'Text',
								options: {
									required: true,
									placeholder: 'Name'
								}
							},
							{
								textId: pleaseProvideSignatureId,
								label: 'Please provide a signature. I have followed and completed the tasks to the specification and standard work practices.',
								fieldType: 'Signature',
								options: {
									required: true
								}
							}
						]
					}
				]
			}
		]
	}
};
//debugger;