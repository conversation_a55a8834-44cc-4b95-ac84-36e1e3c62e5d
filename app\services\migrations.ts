export const migrationTableSql = `
	CREATE TABLE IF NOT EXISTS migrations (
		name TEXT NOT NULL PRIMARY KEY,
		executed DATETIME NOT NULL
	);
`;

const script_1 = `
CREATE TABLE user (
	username TEXT NOT NULL PRIMARY KEY,
	hash TEXT NOT NULL,
	jwt TEXT NOT NULL,
	isActive INT NOT NULL DEFAULT 0 CHECK(isActive = 0 OR isActive = 1),
	lastModified DATETIME NULL,
	userId TEXT NULL,
	teamId INT NULL,
	teamName TEXT NULL
);

CREATE TABLE jobs (
	-- jobschedule
	externalRecId TEXT NOT NULL PRIMARY KEY,
	teamId INT NOT NULL,
	createdOn DATETIME,
	deleted INT NOT NULL CHECK(deleted = 0 OR deleted = 1),
	workOrder TEXT NULL,
	statusCode TEXT NOT NULL,
	workflowCode TEXT NULL,
	assetId INT NULL,
	assetName TEXT NOT NULL,
	maintenanceZone TEXT NULL,
	suburb TEXT NULL,
	latitude NUMERIC NULL,
	longitude NUMERIC NULL,
	esaInfo TEXT NULL,
	pipCustomer_Info TEXT NULL,
	pipComments TEXT NULL,
	fireRiskZoneClass TEXT NULL,
	decInfo TEXT NULL,
	formData TEXT NULL,
	modifiedOnUtc DATETIME NULL,
	latestChange DATETIME NULL,
	hasUploaded INT NOT NULL CHECK(hasUploaded = 0 OR hasUploaded = 1) DEFAULT 0,
	isUploading INT NOT NULL CHECK(isUploading = 0 OR isUploading = 1) DEFAULT 0,
	commentsJson TEXT NULL,
	isLocked INT NOT NULL CHECK(isLocked = 0 OR isLocked = 1) DEFAULT 0
);

CREATE TABLE files (
	fileId TEXT NOT NULL PRIMARY KEY,
	externalRecId TEXT NOT NULL,
	fieldId TEXT NOT NULL,
	filePath TEXT NOT NULL,
	contentPath TEXT NOT NULL,
	hasUploaded INT NOT NULL CHECK(hasUploaded = 0 OR hasUploaded = 1),
	modifiedOnUtc DATETIME NULL,
	isUploading INT NOT NULL CHECK(isUploading = 0 OR isUploading = 1) DEFAULT 0,
	
	FOREIGN KEY(externalRecId) REFERENCES jobs(externalRecId)
);

CREATE TABLE clients (
	clientId TEXT NOT NULL PRIMARY KEY,
	name TEXT NOT NULL
);

CREATE TABLE formTemplate (
	formTemplateId TEXT NOT NULL PRIMARY KEY,
	workflowCode TEXT NOT NULL,
	workflowDescription TEXT NOT NULL,
	jsonTemplate TEXT NOT NULL,
	version INTEGER NOT NULL
);

CREATE TABLE teams (
	teamId INT NOT NULL PRIMARY KEY,
	name TEXT NOT NULL,
	longName TEXT NULL,
	sortOrder INT NOT NULL
);
`;

export const migrations = new Map<string, string>([
	['script_1', script_1]
]);

export default interface Migration {
	name: string;
	executed: Date;
}
