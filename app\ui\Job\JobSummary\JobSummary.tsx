import React from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import navigator from 'services/navigator';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import InProgressFormIcon from 'project_components/Icons/InProgressFormIcon';
import styles from './styles';
import { OtherWorkflowDto, NetworkInspDto, JobSummaryDto } from 'dtos/job';
import { JobSummaryData } from 'app/stores/job-summary-data';
import { StoreComponent } from 'utils/store-component';
import tableStyles from 'project_components/Table/style';
import { map } from 'rxjs/operators';
import CompletedIcon from 'project_components/Icons/CompletedIcon';
import theme from 'config/styles/theme';

@StoreComponent({
	other: JobSummaryData.data$.pipe(map(x => x.other)),
	networkInspections: JobSummaryData.data$.pipe(map(x => x.networkInspections))
})
class JobSummary extends React.PureComponent<Props, never> {
	componentDidMount() {
		JobSummaryData.setVisible();
		JobSummaryData.setArgs({ status: this.props.status });
	}
	componentDidUpdate(prevProps: Props) {
		if (this.props.status !== prevProps.status) {
			JobSummaryData.setArgs({ status: this.props.status });
		}
	}
	componentWillUnmount() {
		JobSummaryData.reset();
	}

	render() {
		const { other, networkInspections } = this.props;
		return (
			<View style={{ paddingHorizontal: 40 }}>
				{other?.length === 0 && networkInspections?.length === 0 &&
					<Text style={styles.header}>NO JOBS</Text>
				}
				{networkInspections?.length > 0 &&
					<React.Fragment>
						<Text style={styles.header}>NETWORK INSPECTIONS</Text>
						<FlatList
							keyExtractor={(item, index) => index.toString()}
							data={networkInspections}
							renderItem={({ item: x }) =>
								<TouchableOpacity
									style={{ ...tableStyles.row, alignItems: 'center' }}
									onPress={() => navigator.go(`/job/NetworkInspection/${x.workOrder}/${encodeURIComponent(x.maintenanceZone)}`)} >
									<View style={{ ...styles.titleCell, flex: 2 }}>
										{this.getIcon(x)}
										<Text style={styles.titleName}>{x.maintenanceZone}</Text>
									</View>
									<View style={{ alignItems: 'flex-end', flex: 1 }}>
									<Text style={{ ...styles.detail }}>{
											this.props.status === "scheduled" ?
											(x.planned + x.inProgress) :
											this.props.status === "inProgress" ?
											x.inProgress :
											x.completed
										} / {x.total}</Text>
									</View>
									<View style={{ alignItems: 'flex-end', flex: 1 }}>
										<Text style={styles.detail}>{x.workOrder}</Text>
									</View>
								</TouchableOpacity>
							} />
					</React.Fragment>
				}
				{other?.length > 0 &&
					<React.Fragment>
						<Text style={styles.header}>OTHER</Text>
						<FlatList
							keyExtractor={(item, index) => index.toString()}
							data={other}
							renderItem={({ item: x }) =>
								<TouchableOpacity
									style={{ ...tableStyles.row, alignItems: 'center' }}
									onPress={() => navigator.go(`/job/${x.workflowCode}/${x.workOrder}`)} >
									<View style={{ ...styles.titleCell, flex: 2 }}>
										{this.getIcon(x)}
										<Text style={styles.titleName}>{x.workflowDescription}</Text>
									</View>
									<View style={{ alignItems: 'flex-end', flex: 1 }}>
										<Text style={{ ...styles.detail }}>{
											this.props.status === "scheduled" ?
											(x.planned + x.inProgress) :
											this.props.status === "inProgress" ?
											x.inProgress :
											x.completed
										} / {x.total}</Text>
									</View>
									<View style={{ alignItems: 'flex-end', flex: 1 }}>
										<Text style={styles.detail}>{x.workOrder}</Text>
									</View>
								</TouchableOpacity>
							} />
					</React.Fragment>
				}
			</View>
		);
	}

	private getIcon(data: OtherWorkflowDto | NetworkInspDto): React.ReactElement {
		if (data.inProgress === 0 && data.completed === 0) {
			return <ScheduledFormIcon size={28} />;
		}
		else if (data.planned === 0 && data.inProgress === 0 && data.completed !== 0) {
			return <CompletedIcon size={28} colour={theme.PRIMARY_GREEN} />;
		}
		return <InProgressFormIcon size={28} colour="#FE7F2D" />;
	}
}

export default JobSummary as React.ComponentType<PublicProps>;

interface Props extends PublicProps {
	networkInspections: NetworkInspDto[];
	other: OtherWorkflowDto[];
}

interface PublicProps {
	status: string;
}