import React, { Component } from 'react';
import { View, Text, TouchableHighlight, ScrollView, Animated, Easing, TouchableOpacity, Image, Modal, PanResponder, PanResponderInstance, GestureResponderEvent, findNodeHandle } from 'react-native';
import { string, number } from 'redi-ui-utils';
import ImagePicker from 'react-native-image-picker';
import styles from './styles';
import Canvas, { CanvasRenderingContext2D } from 'react-native-canvas';
import { scaleToDesign } from 'utils/design-scale';

export interface DrawCoordinate {
    x: number;
    y: number;
}

export interface DrawStroke {
    coordinates: Array<DrawCoordinate>;
}

export interface DrawDto {
    strokes: Array<DrawStroke>;
    dots: Array<DrawCoordinate>;
}

export default class Draw extends React.PureComponent<SignatureProps, State> {
    private _panResponder: PanResponderInstance;
    private _context: CanvasRenderingContext2D;

    private _signature: DrawDto;
    private _currentStroke: DrawStroke;

    private _width: number = scaleToDesign(600);
    private _height: number = scaleToDesign(400);

    private _startX: number = 0;
    private _startY: number = 0;
    private _lastOutsideX: number = 0;
    private _lastOutsideY: number = 0;

    constructor(props: SignatureProps) {
        super(props);
        this._signature = {
            strokes: [],
            dots: [],
        };
        this.state = {
        };
        this.redrawSignature = this.redrawSignature.bind(this);
        this.clearCanvas = this.clearCanvas.bind(this);
    }

    redrawSignature() {
        if (!this._context) {
            return;
        }
        this._context.clearRect(0, 0, this._width, this._height);
        this._signature.strokes.forEach(stroke => {
            stroke.coordinates.forEach((coordinate, index) => {
                if (index === 0) {
                    this._context.beginPath();
                    this._context.moveTo(coordinate.x, coordinate.y);
                } else {
                    this._context.lineTo(coordinate.x, coordinate.y);
                }
            });
            (this._context as any).stroke();
        });
        this._signature.dots.forEach(dot => {

            this._context.beginPath();
            this._context.arc(dot.x, dot.y, 1, 0, 2 * Math.PI, false);
            this._context.fillStyle = 'black';
            this._context.fill();
            this._context.lineWidth = 1;
            this._context.strokeStyle = '#000000';
            (this._context as any).stroke();

        });
    }

    clearCanvas() {
        if (this._context) {
            this._context.clearRect(0, 0, this._width, this._height);
        }
    }

    componentWillMount() {
        this._panResponder = PanResponder.create({
            // Ask to be the responder:
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,

            onPanResponderGrant: (evt, gestureState) => {
                // The gesture has started. Show visual feedback so the user knows
                // what is happening!

                // gestureState.d{x,y} will be set to zero now
                this._startX = evt.nativeEvent.locationX;
                this._startY = evt.nativeEvent.locationY;
                let xPos: number = this._startX + gestureState.dx;
                let yPos: number = this._startY + gestureState.dy;
                this._currentStroke = {
                    coordinates: [
                        {
                            x: xPos,
                            y: yPos
                        }
                    ]
                };

                this._context.beginPath();
                this._context.arc(xPos, yPos, 1, 0, 2 * Math.PI, false);
                this._context.fillStyle = 'black';
                this._context.fill();
                this._context.lineWidth = 1;
                this._context.strokeStyle = '#000000';
                (this._context as any).stroke();
                this._signature.dots.push({ x: xPos, y: yPos });

                this._context.beginPath();
                this._context.moveTo(xPos, yPos);
                this._signature.strokes.push(this._currentStroke);
            },
            onPanResponderMove: (evt: GestureResponderEvent, gestureState) => {
                // The most recent move distance is gestureState.move{X,Y}

                // The accumulated gesture distance since becoming responder is
                // gestureState.d{x,y}
                let xPos: number = this._startX + gestureState.dx;
                let yPos: number = this._startY + gestureState.dy;

                if (xPos < 0 || xPos > this._width || yPos < 0 || yPos > this._height) {
                    this._lastOutsideX = xPos;
                    this._lastOutsideY = yPos;
                    if (this._currentStroke) {
                        let endPathX: number = this._lastOutsideX;
                        let endPathY: number = this._lastOutsideY;
                        if (endPathX < 0) {
                            endPathX = 0;
                        } else if (endPathX > this._width) {
                            endPathX = this._width;
                        }
                        if (endPathY < 0) {
                            endPathY = 0;
                        } else if (endPathY > this._height) {
                            endPathY = this._height;
                        }

                        this._context.lineTo(endPathX, endPathY);
                        (this._context as any).stroke();
                        this._currentStroke.coordinates.push({ x: endPathX, y: endPathY });
                        this._currentStroke = null;
                    }
                } else {
                    if (!this._currentStroke) {
                        let startPathX: number = this._lastOutsideX;
                        let startPathY: number = this._lastOutsideY;
                        if (startPathX < 0) {
                            startPathX = 0;
                        } else if (startPathX > this._width) {
                            startPathX = this._width;
                        }
                        if (startPathY < 0) {
                            startPathY = 0;
                        } else if (startPathY > this._height) {
                            startPathY = this._height;
                        }

                        let xPos: number = this._startX + gestureState.dx;
                        let yPos: number = this._startY + gestureState.dy;
                        this._currentStroke = {
                            coordinates: [
                                {
                                    x: startPathX,
                                    y: startPathY
                                },
                                {
                                    x: xPos,
                                    y: yPos
                                }
                            ]
                        };
                        this._context.beginPath();
                        this._context.moveTo(startPathX, startPathY);
                        this._context.lineTo(xPos, yPos);
                        this._signature.strokes.push(this._currentStroke);
                    } else {
                        this._currentStroke.coordinates.push({ x: evt.nativeEvent.locationX, y: evt.nativeEvent.locationY });
                        this._context.lineTo(xPos, yPos);
                        (this._context as any).stroke();
                    }
                }
            },
            onPanResponderTerminationRequest: (evt, gestureState) => true,
            onPanResponderRelease: (evt, gestureState) => {
                // The user has released all touches while this view is the
                // responder. This typically means a gesture has succeeded
                if (this._currentStroke) {
                    this._currentStroke = null;
                }
            },
            onPanResponderTerminate: (evt, gestureState) => {
                if (this._currentStroke) {
                    this._currentStroke = null;
                }
                // Another component has become the responder, so this gesture
                // should be cancelled
            },
            onShouldBlockNativeResponder: (evt, gestureState) => {
                // Returns whether this component should block native components from becoming the JS
                // responder. Returns true by default. Is currently only supported on android.
                return true;
            },
        });
    }

    handleCanvas = (canvas: any) => {
        this._context = canvas.getContext('2d');
        canvas.width = this._width;
        canvas.height = this._height;
    }

    render() {
        return (
            <View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
                <View {...this._panResponder.panHandlers} style={[styles.canvasView, { height: this._height, width: this._width }]}>
                    <Canvas style={{ width: this._width, height: this._height }} ref={this.handleCanvas} />
                </View>
                <TouchableOpacity style={[styles.clearButton]} onPress={this.clearCanvas}>
                    <Text style={[styles.clearText]}>Clear</Text>
                </TouchableOpacity>
            </View>
        );
    }
}

export interface SignatureProps {
    //This function is called whenever the selected value changes. The newly selected value is passed in as an argument.
    onChange: (selected: Array<string>) => void;
    //Label to display above the button
    label?: string;
    //optional. Ovveride the styles for this component
    overrideStyles?: {
        wrapper: object;
    }
}

interface State {
}