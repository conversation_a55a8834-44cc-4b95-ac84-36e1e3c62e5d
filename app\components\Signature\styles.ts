import { StyleSheet } from 'react-native';
import { FORM_STYLES, FORM_CLASSES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        flexDirection: 'column',
        marginTop: FORM_STYLES.WRAPPER_MARGIN_TOP,
        marginBottom: FORM_STYLES.WRAPPER_MARGIN_BOTTOM,
        alignItems: 'center'
    },
    content: {
		width: '80%',
        flexDirection: 'column',
    },
    labelWrapper:{
        display: 'flex',
        paddingLeft: 0,
        paddingRight: 10,
        marginBottom: 15,
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: '100%',
    },
    labelText: {
        ...FORM_CLASSES.LABEL_TEXT,
    },
    canvasView: {
        overflow: "hidden",
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#EEEEEE',
        borderRadius: 5,
    },
    signatureText: {
        fontSize: 16,
        color: '#888888',
        position: 'absolute',
        top: 10,
        left: 10
    },
    clearButton: {
        height: 36,
        width: 54,
        borderRadius: 6,
        backgroundColor: '#0054A2',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10,
    },
    clearText: {
        fontSize: 16,
        color: 'white',
    }
});

export default styles;
