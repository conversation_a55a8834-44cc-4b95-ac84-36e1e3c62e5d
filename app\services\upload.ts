import { http } from 'redi-http';
import config from 'config/config';
import { AppStore } from 'stores/app-store';
import { ConnectionStatus } from './heartbeat';
import { sleep } from 'utils/sleep';
import { teamIdFilter } from 'utils/filter';
import Database from './database';
import { FileForUploadDto } from 'dtos/file';
import { JobDto } from 'dtos/job';
import JobService from 'services/job';
import { SyncStore } from 'stores/sync-store';
import { string } from 'redi-ui-utils';

const SLEEP_MS = 30 * 1000;

class uploadService {
	run(): void {
		AppStore.connectionStatus$.subscribe(x => {
			if (x === ConnectionStatus.Disconnected) {
				this.stop();
			}
			else {
				this.start();
			}
		});
	}

	private _processStarted = false;
	private _processRunning = false;

	private start(): void {
		if (!this._processRunning) {
			this._processRunning = true;
			if (!this._processStarted) {
				this.process();
			}
		}
	}
	private stop(): void {
		this._processRunning = false;
	}
	private async process(): Promise<void> {
		this._processStarted = true;
		while (this._processRunning) {
			let timer = new Date();
			let t1 = new Date();
			let t2 = new Date();
			console.log("processing uploadData");
			if (AppStore.getValue().teamId) {
				await this.uploadData();
				t1 = new Date();
				console.log("uploadData complete");
				await this.uploadImages();
				t2 = new Date();
				console.log("uploadImages complete");
			}

			const t1diff = (t1.setDate(t1.getDate()) - timer.setDate(timer.getDate()));
			console.log("timing1: " + t1diff);
			const t2diff = (t2.setDate(t2.getDate()) - timer.setDate(timer.getDate()));
			console.log("timing2: " + t1diff);

			await sleep(SLEEP_MS);
		}
		this._processStarted = false;
	}
	private async uploadData(): Promise<void> {
		try {
			let record = await this.getNextDataRecord();
			while (record && this._processRunning) {
				await this.setIsUploading(true, 'jobs', record.externalRecId);
				JobService.refreshSync();
				const result = await this.sendData(record.formData, record.commentsJson, record.workflowCode, record.externalRecId, record.workOrder);
				if (!result) {
					console.warn('Upload data no result');
				}
				if (!result.error) {
					console.warn('saving result', result);
					console.warn('saving result for externalRecId = ', record.externalRecId);
					console.warn('saving result modified on = ', record.modifiedOnUtc);
					const sql = `
						UPDATE jobs
						SET hasUploaded = 1, modifiedOnUtc = ?
						WHERE externalRecId = ?
						AND modifiedOnUtc = ?
					`;
					try {
						await Database.execute(sql, [
							new Date(),
							record.externalRecId,
							record.modifiedOnUtc
						]);
					} catch (error) {
						console.error(error);
					}
				} else {
				}

				await this.setIsUploading(false, 'jobs', record.externalRecId);
				JobService.refreshSync();

				record = await this.getNextDataRecord();
			}
		} catch (error) {
			console.error(error);
		}
	}
	private async uploadImages(): Promise<void> {
		const startTime = Date.now();
		let currentTime = startTime;
		const hasElapsed = () => currentTime - startTime >  10 * SLEEP_MS;
		try {
			let record = await this.getNextImageRecord();
			while (record && this._processRunning && !hasElapsed()) {

				await this.setIsUploading(true, 'files', record.fileId);
				await this.setIsUploading(true, 'jobs', record.externalRecId);
				JobService.refreshSync();

				const result = await this.sendImages(record.fileId, record.contentPath, record.assetName, record.externalRecId, record.fieldId);
				if (!result.error) {
					const sql = `
						UPDATE files
						SET hasUploaded = 1, modifiedOnUtc = ?
						WHERE fileId = ?
						AND modifiedOnUtc = ?
					`;
					await Database.execute(sql, [
						new Date(),
						record.fileId,
						record.modifiedOnUtc
					]);
				}

				await this.setIsUploading(false, 'files', record.fileId);
				await this.setIsUploading(false, 'jobs', record.externalRecId);
				JobService.refreshSync();

				record = await this.getNextImageRecord();
				currentTime = Date.now();
			}
		} catch (error) {
			console.error(error);
		}
	}
	private async getNextDataRecord(): Promise<JobDto> {
		const sql = `
			SELECT * FROM jobs
			WHERE hasUploaded = 0
			AND formData IS NOT NULL
			ORDER BY modifiedOnUtc
			LIMIT 1
		`;

		const result = await Database.select<JobDto>(sql);
		return result[0];
	}
	private async getNextImageRecord(): Promise<FileForUploadDto> {
		const sql = `
			SELECT
				f.fileId
				,f.contentPath
				,f.modifiedOnUtc
				,j.assetName
				,f.externalRecId
				,f.fieldId
			FROM files f
			JOIN jobs j ON j.externalRecId = f.externalRecId
			WHERE f.hasUploaded = 0
			ORDER BY j.modifiedOnUtc
			LIMIT 1
		`;

		const result = await Database.select<FileForUploadDto>(sql);
		return result[0];
	}
	private sendData(data: any, commentsJson: string, workflowCode: string, externalRecId: string, workOrder: string): Promise<any> {
		const url = `${config.apiUrl}MobileApp/UploadData`;
		return http({
			url,
			method: 'POST',
			data,
			commentsJson,
			workflowCode,
			externalRecId,
			workOrder
		});
	}
	private sendImages(name: string, uri: string, assetName: string, externalRecId: string, fieldId: string): Promise<any> {
		const url = `${config.apiUrl}MobileApp/UploadFile`;
		const formData = new FormData();
		formData.append('file', {
			name,
			uri,
			type: 'image/jpg'
		});
		formData.append('dateTime', new Date().toISOString());
		formData.append('assetName', assetName);
		formData.append('externalRecId', externalRecId);
		formData.append('fieldId', fieldId);
		return http({
			url,
			method: 'POST',
			data: formData,
			headers: {}
		});
	}
	private async setIsUploading(isUploading: boolean, table: 'jobs' | 'files', id: string | number): Promise<void> {
		if (table === 'jobs') {
			await Database.execute(`
				UPDATE jobs
				SET isUploading = ?
				WHERE externalRecId = ?
			`, [isUploading ? 1 : 0, id]);
		}
		else if (table === 'files') {
			await Database.execute(`
				UPDATE files
				SET isUploading = ?
				WHERE fileId = ?
			`, [isUploading ? 1 : 0, id]);
		}
		SyncStore.update({ isSyncing: isUploading });
	}
}
const UploadService = new uploadService();
export default UploadService;
