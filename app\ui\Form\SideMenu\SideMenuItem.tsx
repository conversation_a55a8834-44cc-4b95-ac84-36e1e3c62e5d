import React from 'react';
import { MenuPageDto } from '../Form';
import { View, Text, TouchableOpacity } from 'react-native';
import { string } from 'redi-ui-utils';
import styles from './styles';
import ChevronIcon from 'project_components/Icons/ChevronIcon';
import theme from 'config/styles/theme';

export default class SideMenuItem extends React.PureComponent<Props, State> {
	state = {
		isOpen: false
	};

	render() {
		return (
			<View style={styles.menuItemRoot}>
				<View style={styles.itemLabelContainer}>
					{!this.props.noChildren &&
						<TouchableOpacity
							style={{
								...styles.chevron,
								backgroundColor: this.props.isPage ? theme.PRIMARY_DARK_BLUE : '#fff'
							}}
							onPress={() => this.toggle()}>
							<ChevronIcon
								colour={this.props.isPage ? '#fff' : theme.PRIMARY_DARK_BLUE }
								position={this.state.isOpen ? 'down' : 'right'} />
						</TouchableOpacity>
					}
					<TouchableOpacity onPress={this.props.onClick}>
						<Text style={styles.menuItem}>{this.props.label}</Text>
					</TouchableOpacity>
				</View>
				{this.state.isOpen && this.props.children}
			</View>
		);
	}

	private toggle(): void {
		this.setState(s => ({ isOpen: !s.isOpen }));
	}
}

interface Props {
	label: string;
	onClick(): void;
	noChildren?: boolean;
	isPage?: boolean;
}

interface State {
	isOpen: boolean;
}