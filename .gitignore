# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml

# Visual Studio Code
#
.vscode/

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# Bundle artifact
*.jsbundle

# CocoaPods
/ios/Pods/
android/app/src/main/assets/index.android.bundle
android/app/src/main/res/drawable-mdpi/app_assets_logo.png
android/app/src/main/res/raw/app.json
android/app/src/main/res/raw/node_modules_cssselect_lib_procedure.json
android/app/src/main/res/raw/node_modules_csstree_dist_defaultsyntax.json
android/app/src/main/res/raw/node_modules_domserializer_foreignnames.json
android/app/src/main/res/raw/node_modules_entities_lib_maps_decode.json
android/app/src/main/res/raw/node_modules_entities_lib_maps_entities.json
android/app/src/main/res/raw/node_modules_entities_lib_maps_legacy.json
android/app/src/main/res/raw/node_modules_entities_lib_maps_xml.json
android/app/src/main/res/raw/node_modules_rollbar_package.json
android/app/src/main/res/raw/node_modules_rollbarreactnative_package.json
