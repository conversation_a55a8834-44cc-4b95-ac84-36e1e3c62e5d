import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Redi_NumberNeg from 'components/NumberNeg/Redi_NumberNeg';
import { RediTextProps } from 'components/Text/Redi_Text';
import { overrideStyles } from 'components/Form/form-styles';

export default class FormNumberNeg extends React.Component<WrappedFieldInterface<string, RediTextProps>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<Redi_NumberNeg
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error}
				overrideStyles={{ ...overrideStyles }} />
		);
	}
}