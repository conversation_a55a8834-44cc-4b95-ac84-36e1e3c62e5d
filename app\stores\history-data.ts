import { HistoryDto, JobStatusCode } from 'dtos/job';
import { DataServiceBase } from 'utils/data-service-base';
import Database from 'services/database';
import { HistoryFilter } from '../ui/Sync/History/History';
import { teamIdFilter } from 'utils/filter';
import { DateTime } from 'redi-ui-utils';

type Args = {
	filter: HistoryFilter;
};

class historyData extends DataServiceBase<HistorySectionData[], Args> {
	constructor() {
		super('history');
	}

	protected async _load(): Promise<void> {
		const currentCall = ++this._callCount;
		const { filter } = this.query.getValue().args;
		let statusSql = '';
		switch (filter) {
			case 'inProgress':
				statusSql = `AND statusCode = '${JobStatusCode.JInProgress}'`;
				break;
			case 'completed':
				statusSql = `AND statusCode = '${JobStatusCode.JComplete}'`;
				break;
			case 'all':
			default:
				statusSql = `AND (statusCode = '${JobStatusCode.JInProgress}' OR statusCode = '${JobStatusCode.JComplete}')`;
				break;
		}

		const sql = `
			SELECT
				externalRecId
				,modifiedOnUtc
				,statusCode
				,assetName
				,isUploading
			FROM jobs
			${teamIdFilter()}
			${statusSql}
			ORDER BY modifiedOnUtc DESC
		`;

		const data = await Database.select<HistoryDto>(sql);

		const sectionData = new Array<HistorySectionData>();

		for (const item of data) {
			const date = DateTime.format(new Date(item.modifiedOnUtc), 'yyyy-MM-dd');
			const search = sectionData.find(x => x.date === date);
			if (search) {
				search.data.push(item);
			}
			else {
				sectionData.push({
					data: [item],
					date,
					index: sectionData.length,
					title: ''
				});
			}
		}

		const today = DateTime.format(new Date(), 'yyyy-MM-dd');
		for (const item of sectionData) {
			item.title =
				(item.date === today ? 'TODAY' : DateTime.format(new Date(item.date), 'dd MMM')) +
				` (${item.data.length})`;
		}

		if (currentCall > this._latestCall) {
			this.store.update({ data: sectionData });
		}
	}

	private _callCount = 0;
	private _latestCall = 0;
}
export const HistoryData = new historyData();

export interface HistorySectionData {
	data: HistoryDto[];
	title: string;
	index: number;
	date: string;
}