import React from 'react';
import Svg, { G, Polyline } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class LeftIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_MEDIUM_BLUE
	};

	render() {
		let width: string = scaleToDesign(35) + "px";
		let height: string = scaleToDesign(35) + "px";
		return (
			<Svg width={width} height={height} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Polyline stroke={this.props.colour} strokeWidth="2" transform="translate(17.500000, 17.500000) rotate(-270.000000) translate(-17.500000, -17.500000) " points="13.1419404 15.3209702 17.5 19.6790298 21.8580596 15.3209702" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
}
