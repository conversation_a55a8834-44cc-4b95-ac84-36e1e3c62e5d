import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	wrapper: {
		flexDirection: 'column',
		display: "flex",
		padding: 10,
	},
	rowWrapper: {
		flexDirection: 'row',
		display: "flex",
		margin: 2,
		justifyContent: 'space-evenly'
	},
	optionWrapper: {
		flexDirection: 'row',
		display: "flex",
		height: 50,
		alignItems: 'center'
	},
	optionText: {
		fontSize: 22,
		color: '#002C55',
		height: 30,
		top: -2
	},
	radioOuter: {
		borderRadius: 10,
		borderWidth: 1,
		borderColor: '#002C55',
		backgroundColor: 'white',
		width: 20,
		height: 20,
		marginRight: 8,
		alignItems: 'center',
		justifyContent: 'center'
	},
	radioInner: {
		backgroundColor: '#002C55',
		height: 12,
		width: 12,
		borderRadius: 6
	}
});

export default styles;
