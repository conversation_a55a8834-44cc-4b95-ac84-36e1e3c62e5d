import React from 'react';
import { Router, Route } from 'react-router-native';
import Login from 'ui/Login/Login';
import MapPage from 'ui/Map/MapPage';
import Sync from 'ui/Sync/Sync';
import navigator from 'services/navigator';
import Settings from 'ui/Settings/Settings';
import FormPage from 'ui/Form/FormPage';
import { View, Alert } from 'react-native';
import NavBar from 'project_components/NavBar/NavBar';
import ToastProvider, { showToast } from 'components/Toast/ToastProvider';
import { AppStore } from 'app/stores/app-store';
import Database from 'services/database';
import { configureHttp } from 'redi-http';
import SecurityService from 'services/security';
import Splash from 'project_components/Splash/Splash';
import HeartbeatService from 'services/heartbeat';
import theme from 'config/styles/theme';
import Job from 'ui/Job/Job';
import JobList from 'ui/Job/JobList/JobList';
import JobDetail from 'ui/Job/JobDetail/JobDetail';
import JobService from 'services/job';
import UploadService from 'services/upload';
import CleanupService from 'services/cleanup';
import { ConfirmModal } from 'components/ConfirmModal/ConfirmModal';
import FormTemplateService from 'services/formTemplate';
import { Client, Configuration } from 'rollbar-react-native'
import config from 'config/config';
import RNExitApp from 'react-native-exit-app';

const rollbarConfig = new Configuration('2d73e372a94d4c8aa7da9f9a7c876d15', {
	logLevel: 'error',
	enabled: config.environment !== 'Local',
	environment: config.environment,
	payload: {
		client: {
			javascript: {
				source_map_enabled: true,
				code_version: 'test.droid',
			}
		}
	}
});
export const rollbar = new Client(rollbarConfig);

configureHttp({
	getToken: () => AppStore.getValue().jwt,
	defaultTimeoutMs: 300000
});

export default class Setup extends React.PureComponent<{}, State> {
	state = {
		isLoaded: false
	};

	componentDidMount() {
		setTimeout(() => this.startup(), 1000);
	}
	componentDidCatch(error: any): void {
		rollbar.error(error);
		const msg = `
		${error?.toString()}
		${JSON.stringify(error)}
		`;
		Alert.alert("Error", msg, [
			{
				text: 'Close App',
				onPress: () => RNExitApp.exitApp()
			},
			{
				text: 'Dismiss'
			}
		]);
	}

	render() {
		return (
			<React.Fragment>
				<View style={{ flex: 1, backgroundColor: theme.WHITE }}>
					{!this.state.isLoaded && <Splash />}
					{this.state.isLoaded &&
						<Router history={navigator.history}>
							<Route path="/form/:id" component={FormPage} exact={true} />
							<Route path="/job/:status" component={Job} exact={true} />
							<Route path="/job/:workflowCode/:workOrder/:maintenanceZone?" component={JobList} exact={true} />
							<Route path="/jobDetail/:id" component={JobDetail} exact={true} />
							<Route path="/login" component={Login} exact={true} />
							<Route path="/map" component={MapPage} exact={true} />
							<Route path="/settings" component={Settings} exact={true} />
							<Route path="/sync" component={Sync} />
							<Route path="/reset" component={() => <Splash reset />} exact={true} />
						</Router>
					}
				</View>
				<NavBar />
				<ToastProvider />
				<ConfirmModal />
			</React.Fragment>
		);
	}
	private async startup(): Promise<void> {
		//	setTimeout(() => TestService.test(), 1000);
		try {
			HeartbeatService.start();
			await Database.connect();
			await Database.migrate();
			await CleanupService.run();
			await SecurityService.loginActiveUser();
			JobService.start();
			UploadService.run();
			FormTemplateService.start();

			AppStore.isLoggedIn$.subscribe(isLoggedIn => {
				if (isLoggedIn) {
					navigator.go('/map');
					JobService.refreshSync();
				}
				else {
					navigator.go('/login');
				}
			});
			this.setState({ isLoaded: true });
		} catch (e) {
			console.warn(e);
			showToast('error', e);
		}
	}
}

interface State {
	isLoaded: boolean;
}
