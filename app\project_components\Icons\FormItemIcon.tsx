import React from 'react';
import Svg, { G, Circle, Line, Path } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class FormItemIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_MEDIUM_BLUE,
		size: 35
	};

	render() {
		return (
			<Svg width={scaleToDesign(this.props.size) + 'px'} height={scaleToDesign(this.props.size) + 'px'} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Path d="M11.6546287,1 L26.8712628,1 C28.528117,1 29.8712628,2.34314575 29.8712628,4 L29.8712628,31 C29.8712628,32.6568542 28.528117,34 26.8712628,34 L8.12873721,34 C6.47188296,34 5.12873721,32.6568542 5.12873721,31 L5.12873721,29" stroke={this.props.colour} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
					<Circle fill={this.props.colour} cx="5.12873721" cy="1" r="1.5" />
					<Circle fill={this.props.colour} cx="5.12873721" cy="8" r="1.5" />
					<Circle fill={this.props.colour} cx="5.12873721" cy="15" r="1.5" />
					<Circle fill={this.props.colour} cx="5.12873721" cy="22" r="1.5" />
					<Line x1="11.6546287" y1="8" x2="23.3453713" y2="8" stroke={this.props.colour} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
					<Line x1="11.6546287" y1="15" x2="23.3453713" y2="15" stroke={this.props.colour} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
					<Line x1="11.6546287" y1="22" x2="23.3453713" y2="22" stroke={this.props.colour} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
	size: number;
}
