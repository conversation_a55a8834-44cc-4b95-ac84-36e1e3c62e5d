import { JobListDto, JobStatusCode } from 'dtos/job';
import { Sort } from 'project_components/Table/HeaderCell/HeaderCell';
import { DataServiceBase } from 'utils/data-service-base';
import Database from 'services/database';
import { teamIdFilter } from 'utils/filter';

type Args = {
	workflowCode?: string;
	workOrder?: string;
	column?: keyof JobListDto;
	sort?: Sort;
	maintenanceZone?: string;
	search?: string;
	status?: 'AllJobs' | 'Completed' | 'CompletedToday' | 'InProgress' | 'Scheduled';
};

class jobListData extends DataServiceBase<JobListDto[], Args> {
	constructor() {
		super('joblist');
	}

	protected async _load(): Promise<void> {
		const currentCall = ++this._callCount;
		const { workflowCode, workOrder, column, sort, maintenanceZone, search, status } = this.query.getValue().args;

		let __column = 'createdOn';
		let __sort = 'ASC';

		if (column && sort && sort !== Sort.None) {
			__column = column;
			__sort = sort;
		}

		let maintenanceZoneSql = '';
		if (maintenanceZone) {
			maintenanceZoneSql = `AND maintenanceZone = '${maintenanceZone}'`;
		}

		let searchSql = '';
		if (search) {
			searchSql = `
				AND
				(
					assetName like '%${search}%' OR
					suburb like '%${search}%'
				)
			`;
		}

		let statusCodeSql = '';

		switch (status) {
			case 'Completed':
				statusCodeSql = `AND statusCode = '${JobStatusCode.JComplete}'`;
				break;
			case 'CompletedToday':
				const today = new Date();
				today.setHours(0, 0, 0, 0);
				statusCodeSql = `
					AND statusCode = '${JobStatusCode.JComplete}'
					AND modifiedOnUtc > '${today.toISOString()}'
				`;
				break;
			case 'InProgress':
				statusCodeSql = `AND statusCode = '${JobStatusCode.JInProgress}'`;
				break;
			case 'Scheduled':
				statusCodeSql = `AND statusCode = '${JobStatusCode.JPlanned}'`;
				break;
		}

		const sql = `
			SELECT
				externalRecId
				,createdOn
				,statusCode
				,assetName
				,suburb
				,CASE WHEN esaInfo IS NULL THEN 0 ELSE 1 END as hasEsa
				,CASE WHEN ((pipCustomer_Info IS NULL OR pipCustomer_Info = '') AND (pipComments IS NULL OR pipComments = '')) THEN 0 ELSE 1 END as hasPip
				,CASE WHEN decInfo IS NULL THEN 0 ELSE 1 END as hasDec
				,fireRiskZoneClass
			FROM jobs
			${teamIdFilter()}
			AND workflowCode = ?
			${statusCodeSql}
			AND CASE WHEN workOrder IS NULL OR workOrder = '' then 'NA' ELSE workOrder END = ?
			${maintenanceZoneSql}
			${searchSql}
			ORDER BY ${__column} ${__sort}
		`;

		const data = await Database.select<JobListDto>(sql, [workflowCode, workOrder]);
		for (const item of data) {
			item.createdOn = new Date(item.createdOn);
			item.hasDec = !!item.hasDec;
			item.hasEsa = !!item.hasEsa;
			item.hasPip = !!item.hasPip;
		}

		if (currentCall > this._latestCall) {
			this.store.update({ data });
		}
	}

	private _callCount = 0;
	private _latestCall = 0;
}
export const JobListData = new jobListData();