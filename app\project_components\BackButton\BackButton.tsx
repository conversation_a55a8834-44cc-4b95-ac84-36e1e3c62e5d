import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import LeftIcon from 'project_components/Icons/LeftIcon';
import textStyles from 'config/styles/text-styles';
import navigator from 'services/navigator';
import theme from 'config/styles/theme';

export default class BackButton extends React.PureComponent<Props, never> {
	render() {
		return (
			<TouchableOpacity style={{
				flexDirection: 'row',
				justifyContent: 'center',
				alignItems: 'center'
			}} onPress={() => this.goBack()}>
				<LeftIcon colour={theme.WHITE} />
				<Text style={textStyles.medium}>Back</Text>
			</TouchableOpacity>
		);
	}
	private goBack() {
		if (this.props.onPress) {
			this.props.onPress();
		}
		else {
			navigator.goBack();
		}
	}
}

interface Props {
	onPress?(): void;
}