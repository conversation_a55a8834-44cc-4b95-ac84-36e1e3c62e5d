import { FORM_CLASSES, FORM_STYLES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        ...FORM_CLASSES.WRAPPER
    },
    content:{
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: '80%'
    },
    textInputContainer:{
        ...FORM_CLASSES.INPUT_CONTAINER,
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: '100%'
    },
    placeholder: {
        ...FORM_CLASSES.PLACEHOLDER_TEXT,
        position: 'absolute',
        left: 7,
        paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT - 7,
        paddingRight: FORM_STYLES.INPUT_PADDING_LEFT - 7,
    },
    focusedLabel: {
        top: -10,
        fontSize: 12        
    },
    input: {
        minHeight: 120,
        width: '100%',
        fontSize: FORM_STYLES.LABEL_FONT_SIZE,
        color: FORM_STYLES.LABEL_FONT_COLOR,
        paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT,
    }
});

export default styles;
