import React from 'react';
import { View, TextInput, ViewStyle } from 'react-native';
import styles from './styles';
import SearchIcon from 'project_components/Icons/SearchIcon';
import theme from 'config/styles/theme';

export default class SearchBar extends React.PureComponent<Props, never> {
	render() {
		return (
			<View style={{ ...styles.root, ...this.props.style }}>
				<TextInput
					placeholder="Search"
					value={this.props.query}
					style={{ flex: 1 }}
					onChangeText={e => this.props.onSearch(e)}
				/>
				<SearchIcon colour={theme.PRIMARY_MEDIUM_BLUE} size={20} />
			</View>
		);
	}
}

interface Props {
	onSearch(value: string): void;
	query: string;
	style?: ViewStyle;
}