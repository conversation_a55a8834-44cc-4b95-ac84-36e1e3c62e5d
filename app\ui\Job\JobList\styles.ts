import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import { scaleAllToDesign } from 'utils/design-scale';
import theme from 'config/styles/theme';

const styles = scaleAllToDesign({
	root: {
		flex: 1,
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 60
	},
	title: {
		flexDirection: 'row',
		justifyContent: 'center',
		alignItems: 'center'
	},
	box: {
		borderRadius: 8,
		width: 46,
		height: 28,
		justifyContent: 'center'
	},
	assetCell: {
		flexDirection: 'row',
		justifyContent: 'flex-start'
	},
	assetName: {
		fontSize: 24,
		color: theme.TEXT_DARK,
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2.3,
		marginLeft: 18
	},
	suburb: {
		fontSize: 16,
		color: '#A0A0A0',
		fontFamily: theme.FONT_MEDIUM,
		letterSpacing: 1,
		textAlign: 'right'
	}
});
export default styles;
