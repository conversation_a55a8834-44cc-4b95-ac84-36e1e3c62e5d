import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Redi_TextMultiLine, { RediTextMultiLineProps } from 'components/TextMultiLine/Redi_TextMultiLine';

export default class FormTextMultiLine extends React.Component<WrappedFieldInterface<string, RediTextMultiLineProps>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<Redi_TextMultiLine
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				error={this.props.form.field.error}
				value={this.props.fieldProps.value}
				overrideStyles={{
					wrapper: {
						paddingTop: 15,
						paddingBottom: 15
					}
				}}
			/>
		);
	}
}