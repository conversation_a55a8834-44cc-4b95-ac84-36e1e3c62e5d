import { WrappedFieldInterface } from 'redi-form';
import { YesNoNASelection } from 'components/YesNoNA/YesNoNA';
import React from 'react';
import YesNoNA from 'components/YesNoNA/YesNoNA';

export default class FormYesNoNA extends React.Component<WrappedFieldInterface<boolean, FormYesNoNAProps>> {
	constructor(props: any) {
		super(props);
		this.onChange = this.onChange.bind(this);
	}

	render() {
		const props = this.props.componentProps();
		let selected: YesNoNASelection = undefined;
		if (this.props.fieldProps.value === true) {
			selected = YesNoNASelection.Yes;
		} else if (this.props.fieldProps.value === false) {
			selected = YesNoNASelection.No;
		} else if (this.props.fieldProps.value === null) {
			selected = YesNoNASelection.NA;
		}
		return (
			<YesNoNA
				{...props}
				selected={selected}
				onChange={this.onChange}
				error={this.props.form.field.error} />
		);
	}

	private onChange(selection: YesNoNASelection) {
		this.props.fieldProps.onFocus?.(null);
		let newValue: boolean = undefined;
		switch (selection) {
			case YesNoNASelection.Yes:
				newValue = true;
				break;
			case YesNoNASelection.No:
				newValue = false;
				break;
			case YesNoNASelection.NA:
				newValue = null;
				break;
		}
		this.props.fieldProps.onChange(newValue);
		this.props.fieldProps.onBlur?.(null);
	}
}

export interface FormYesNoNAProps {
	selected: boolean;
}

