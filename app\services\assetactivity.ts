import config from 'config/config';
import { http, HttpResult } from 'redi-http';
import { BaseAssetActivityDto } from 'dtos/assetActivity';

class assetActivityService {
	getAssetActivitesFromServer(externalRecId: string): Promise<HttpResult<BaseAssetActivityDto>> {
		const url = `${config.apiUrl}AssetActivity/GetAssetActivity`;
		return http<BaseAssetActivityDto>({ method: 'GET', url, externalRecId})
		.then((data) => {
			return data;
		}).catch(error => {
			return error;
		});
	}
}
const AssetActivityService = new assetActivityService();
export default AssetActivityService;


