import { JobSummaryDto, NetworkInspDto, OtherWorkflowDto, JobStatusCode, JobDto } from 'dtos/job';
import Database from 'services/database';
import { DataServiceBase } from 'utils/data-service-base';
import { teamIdFilter } from 'utils/filter';
import { AppFormTemplateDto } from 'dtos/formTemplate';

type Args = { status?: string };

class jobSummaryData extends DataServiceBase<JobSummaryDto, Args> {
	constructor() {
		super('jobsummary');
	}
	protected async _load(): Promise<void> {
		const currentCall = ++this._callCount;
		const networkInspSql = `
			SELECT
				CASE WHEN workOrder IS NULL then 'NA' ELSE workOrder END as workOrder
				,COUNT(*) as total
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JInProgress}' THEN 1 ELSE 0 END) as inProgress
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JPlanned}' THEN 1 ELSE 0 END) as planned
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JComplete}' THEN 1 ELSE 0 END) as completed
				,maintenanceZone
			FROM jobs
			${teamIdFilter()}
			AND workflowCode = 'NetworkInspection'
			GROUP BY workOrder, maintenanceZone
		`;
		const otherWorkflowSql = `
			SELECT
				workflowCode
				,CASE WHEN workOrder IS NULL OR workOrder = '' then 'NA' ELSE workOrder END as workOrder
				,COUNT(*) as total
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JInProgress}' THEN 1 ELSE 0 END) as inProgress
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JPlanned}' THEN 1 ELSE 0 END) as planned
				,SUM(CASE WHEN statusCode = '${JobStatusCode.JComplete}' THEN 1 ELSE 0 END) as completed
			FROM jobs
			${teamIdFilter()}
			AND NOT workflowCode = 'NetworkInspection'
			GROUP BY workflowCode, workOrder
		`;

		const checker = `
			SELECT * FROM formTemplate
		`;

		const networkData = await Database.select<NetworkInspDto>(networkInspSql);
		if (currentCall < this._latestCall) {
			return;
		}
		let otherData = await Database.select<OtherWorkflowDto>(otherWorkflowSql);
		const allWorkflows = await (await Database.select<AppFormTemplateDto>(checker)).map(x => x);
		let workflowsToAdd:AppFormTemplateDto[] = [];
		for(var ii = 0; ii < allWorkflows.length; ii++) {
			var foundWorkflow = false;
			for(var jj = 0; jj < otherData.length; jj++) {
				if(otherData[jj].workflowCode == allWorkflows[ii].workflowCode) {
					foundWorkflow = true;
					break;
				}
			}
			if(!foundWorkflow) {
				workflowsToAdd.push(allWorkflows[ii]);
			}
		}

		let newOtherData:OtherWorkflowDto[] = [];
		otherData.forEach(x => {
			if (x.workOrder === 'null') { x.workOrder = null; }
			var workflow = allWorkflows.find(a => a.workflowCode === x.workflowCode);
			if (workflow) {
				x.workflowDescription = workflow.workflowDescription;
			}
			newOtherData.push(x);
		});
		workflowsToAdd.forEach(x => {
			newOtherData.push({
				total: 0,
				planned: 0,
				inProgress: 0,
				completed: 0,
				workflowCode: x.workflowCode,
				workflowDescription: x.workflowDescription,
				workOrder: null
			});
		});

		if (currentCall > this._latestCall) {
			this._latestCall = currentCall;
			this.store.update({
				data: {
					networkInspections: networkData,
					other: newOtherData
				}
			});
		}
	}

	private _callCount = 0;
	private _latestCall = 0;
}
export const JobSummaryData = new jobSummaryData();