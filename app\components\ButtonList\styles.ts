import { StyleSheet } from 'react-native';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        flexDirection: 'column'
    },
    label: {
        fontSize: 22
    },
    buttonWrapper:{
        flexDirection: 'column',
    },
    baseButton: {
        height: 60,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 10
    },
    baseButtonText: {
        color: 'white',
        fontSize: 22,
    },
    yesButton:{
        backgroundColor: 'rgba(59, 178, 115, 0.5)'
    },
    yesButtonText:{
    },
    yesButtonSelected:{
        backgroundColor: 'rgb(59,178,115)'
    },
    noButton:{
        backgroundColor: 'rgba(0, 44, 85, 0.5)'
    },
    noButtonText:{

    },
    noButtonSelected:{
        backgroundColor: 'rgb(0,44,85)'
    }
});

export default styles;
