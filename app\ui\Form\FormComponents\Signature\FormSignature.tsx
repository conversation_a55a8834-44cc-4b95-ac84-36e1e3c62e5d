import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Signature, { SignatureProps } from 'components/Signature/Signature';
import { CommonFormComponentProps } from 'components/Form/form-props';

export default class FormSignature extends React.Component<WrappedFieldInterface<string, SignatureProps>> {
	render() {
		const props = this.props.componentProps();
		return (
			<Signature
				{...props}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error} />
		);
	}
}

export interface FormSignatureProps extends CommonFormComponentProps<boolean> {
	
}