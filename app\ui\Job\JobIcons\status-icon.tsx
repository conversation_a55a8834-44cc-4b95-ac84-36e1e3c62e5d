import React from 'react';
import { JobStatusCode } from 'dtos/job';
import CompletedIcon from 'project_components/Icons/CompletedIcon';
import InProgressFormIcon from 'project_components/Icons/InProgressFormIcon';
import ScheduledFormIcon from 'project_components/Icons/ScheduledFormIcon';
import theme from 'config/styles/theme';

export function getStatusIcon(status: JobStatusCode, size = 28) {
	switch (status) {
		case JobStatusCode.JComplete:
			return <CompletedIcon size={size} colour={theme.PRIMARY_GREEN} />;
		case JobStatusCode.JInProgress:
			return <InProgressFormIcon size={size} colour="#FE7F2D" />;
		case JobStatusCode.JPlanned:
		default:
			return <ScheduledFormIcon size={size} />;
	}
}