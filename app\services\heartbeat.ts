import config from 'config/config';
import { AppStore } from 'app/stores/app-store';
import { http, HttpResult } from 'redi-http';

const route = `${config.apiUrl}Heartbeat`;

//time in miliseconds between heartbeats
const HEARTBEAT_FREQUENCY: number = 2000;

//number of heartbeats to keep in the heartbeat history
const TRACKED_HEARTBEATS: number = 4;

//number of successful heartbeats IN A ROW for this service to say the connection is CONNECTING.
//MUST be more then TRACKED_HERATBEATS or will fail in the constructor
const CONNECTING_COUNT: number = 1;

enum HeartbeatStatus {
	Success,
	Fail
}

export enum ConnectionStatus {
	//All heratbeats are successful
	Connected = 'Connected',
	//All heartbeats are faailed
	Disconnected = 'Disconnected',
	//the last CONNECTING_COUNT hearbeats are passed, given one of teh above conditioons is not already true.
	Connecting = 'Connecting',
	//A spasmodic connection, that does nto already fall ionto one of the other connection status categories,
	Weak = 'Weak'
}

interface ConnectionTrackerDto{
	sequence: number;
	status: HeartbeatStatus;
}

class heartbeatService {
	private _started: boolean = false;
	private _intervalId: number;
	private _heartbeatHistory: Array<ConnectionTrackerDto>;
	private _sequence: number = 0;

	constructor() {
		this._heartbeatHistory = [];
		//validate that the settings will actually make sense
		if (CONNECTING_COUNT > TRACKED_HEARTBEATS) {
			throw new Error('Connecting count must be more than trakced heartbeats');
		} else if (CONNECTING_COUNT === TRACKED_HEARTBEATS) {
			console.warn('Setting connection count equal to tracked heartbeats means the connections status can never be CONNECTING, however the service will continue to work in all other aspects.');
		}
	}

	start() {
		if (!this._started) {
			this._started = true;
			this._intervalId = setInterval(() => this.sendHeartbeat(), HEARTBEAT_FREQUENCY);
			//kick it off once immediately
			this.sendHeartbeat();
		}
	}

	stop() {
		if (this._started) {
			this._started = true;
			clearInterval(this._intervalId);
			this._intervalId = null;
		}
	}

	private sendHeartbeat() {
		const url = `${route}/Heartbeat`;
		let wkSequence: number = ++this._sequence;

		http({ method: 'GET', url }, {useTimeout: true}).then(response => {
			if (response.error) {
				this.updateConnectionStatus(wkSequence, HeartbeatStatus.Fail);
			} else {
				this.updateConnectionStatus(wkSequence, HeartbeatStatus.Success);
			}
		}).catch((ex) => {
			console.log("Heartbeat: ", ex);
			this.updateConnectionStatus(wkSequence, HeartbeatStatus.Fail);
		});
	}

	private updateConnectionStatus(sequence: number, latestHeartbeat: HeartbeatStatus) {
		let heartbeatTracker: ConnectionTrackerDto = {
			sequence: sequence,
			status: latestHeartbeat
		};
		this._heartbeatHistory.push(heartbeatTracker);
		this._heartbeatHistory.sort((a, b) => b.sequence - a.sequence);

		//if we have hit max, remove last
		if (this._heartbeatHistory.length >= TRACKED_HEARTBEATS) {
			this._heartbeatHistory.pop();
		}

		//Test if we are in the connected status - all statuses must be successful
		if (this._heartbeatHistory.every(s => s.status === HeartbeatStatus.Success)) {
			AppStore.update({ connectionStatus: ConnectionStatus.Connected });
			return;
		}

		//Test if we are in the Disconnected Status - all statuses must be unsuccessful
		if (this._heartbeatHistory.every(s => s.status === HeartbeatStatus.Fail)) {
			AppStore.update({ connectionStatus: ConnectionStatus.Disconnected });
			return;
		}

		//test if we are in Connecting status - the most recent statuses must be successful
		//only valid if we have at least CONNECTING_COUNT + 1 heartbeats - if we had exactly CONNECTING_COUNT then they would all be successful and it would have alreayd be in CONNECTED state
		if (this._heartbeatHistory.length > CONNECTING_COUNT) {
			if (this._heartbeatHistory.slice(0, CONNECTING_COUNT).every(s => s.status === HeartbeatStatus.Success)) {
				AppStore.update({ connectionStatus: ConnectionStatus.Connecting });
				return;
			}
		}

		//every other scenario - i.e a mishmash of fails and successes
		AppStore.update({ connectionStatus: ConnectionStatus.Weak });
	}
}

const HeartbeatService = new heartbeatService();
export default HeartbeatService;
