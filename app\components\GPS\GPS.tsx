import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';
import GetLocation from 'react-native-get-location';
import GPSIcon from 'project_components/Icons/GPSIcon';
import { scaleToDesign } from 'utils/design-scale';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';
import FormComponentLabel from 'components/Form/FormComponentLabel';

export interface GPSCoordinatesDto {
	longitude: number;
	latitude: number;
}

export default class GPS extends React.PureComponent<GPSProps, State> {
	constructor(props: GPSProps) {
		super(props);

		this.state = {
			GPSBusy: false
		};

		this.getGPSLocation = this.getGPSLocation.bind(this);
	}

	componentDidMount() {
        if (!this.props.value) {
		this.getGPSLocation();
	}
	}
	render() {
		let value: GPSCoordinatesDto = this.props.value ? this.props.value : null;
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View style={[styles.content]}>
					<FormComponentLabel
						label={this.props.label}
						required={this.props.required}
						overrideStyles={{ ...this.props.overrideStyles }} />
					<TouchableOpacity activeOpacity={0.95} style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }} onPress={this.getGPSLocation}>
						<View style={[styles.actionButton]} >
							<React.Fragment>
								<GPSIcon colour="#FFFFFF" />
								<Text style={[styles.buttonText]}>Get GPS Location</Text>
							</React.Fragment>
						</View>
						<View style={[styles.displayWrapper, this.props.overrideStyles?.displayWrapper]}>
							<View style={{ width: scaleToDesign(20) }} />
							<View style={{ flexDirection: 'row' }}>
								{!!value &&
									<React.Fragment>
										<Text style={[styles.displayText, this.props.overrideStyles?.displayText]}>
											Lat: {value.latitude?.toPrecision(5)}
										</Text>
										<View style={{ backgroundColor: '#0054A2', width: 2 }} />
										<Text style={[styles.displayText, this.props.overrideStyles?.displayText]}>
											Lng: {value.longitude?.toPrecision(5)}
										</Text>
									</React.Fragment>
								}
								{!value &&
									<Text style={[styles.displayText, this.props.overrideStyles?.displayText]}>
										Unavailable. Tap to retry.
                                    </Text>
								}
							</View>

						</View>
					</TouchableOpacity>
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}

	private getGPSLocation() {
		this.props.onFocus?.(null);
		this.setState({ GPSBusy: true });
		return GetLocation.getCurrentPosition({
			enableHighAccuracy: true,
			timeout: 15000,
		}).then((location: any) => {
			this.setState({ GPSBusy: false });
			this.props.onChange({
				latitude: location.latitude,
				longitude: location.longitude
			});
			this.props.onBlur?.(null);
		}, () => {
			//this.props.onChange(null);
			this.setState({ GPSBusy: false });
			this.props.onBlur?.(null);
		});
	}
}

export interface GPSProps extends CommonFormComponentProps<GPSCoordinatesDto> {
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper: object,
		//styles that go on the View that wraps the label
		labelWrapper?: object,
		//styles tyhat go on the label Text element
		labelText?: object,
		displayWrapper?: object,
		//Styles to go on the view that wraps the text which displays the fomratted date value
		//Styles to go on the Text
		displayText?: object,
	}
}

interface State {
	GPSBusy: boolean;
}
