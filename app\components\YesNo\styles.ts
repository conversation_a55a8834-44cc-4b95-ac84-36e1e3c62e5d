import { StyleSheet } from 'react-native';
import { FORM_STYLES, FORM_CLASSES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
    wrapper: {
        ...FORM_CLASSES.WRAPPER
    },
    content: {
		width: '80%',
		flexDirection: 'column'
    },
    labelWrapper: {
        display: 'flex',
        paddingRight: 10,
        marginBottom: 15,
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: '80%',
    },
    labelText: {
        ...FORM_CLASSES.LABEL_TEXT
    },
    buttonContainer:{
        flexDirection: 'row'
    },
    baseButton: {
        width: 130,
        height: 60,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 20
    },
    baseButtonText: {
        color: 'white',
        fontSize: 22,
    },
    yesButton:{
        backgroundColor: 'rgba(59, 178, 115, 0.5)'
    },
    yesButtonText:{
    },
    yesButtonSelected:{
        backgroundColor: 'rgb(59,178,115)'
    },
    noButton:{
        backgroundColor: 'rgba(0, 44, 85, 0.5)'
    },
    noButtonText:{

    },
    noButtonSelected:{
        backgroundColor: 'rgb(0,44,85)'
    }
});

export default styles;
