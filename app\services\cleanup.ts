import Database from './database';
import { FileDto } from 'dtos/file';
import * as RNFS from 'react-native-fs';
import { JobDto, JobStatusCode } from 'dtos/job';
import { DateTime } from 'redi-ui-utils';

const _deleteOlderThanDays = 30;

class cleanupService {
	async run(): Promise<void> {
		const deleteAfter = DateTime.addDays(new Date(), -_deleteOlderThanDays);
		console.log('DELETE AFTER', deleteAfter);
		const toDelete = await Database.select<JobDto>(`
			SELECT * FROM jobs j
			WHERE j.statusCode = '${JobStatusCode.JComplete}'
			AND j.hasUploaded = 1
			AND j.modifiedOnUtc < ?
		`, [deleteAfter]);

		for (const job of toDelete) {
			try {
				const jobFiles = await Database.select<FileDto>(`
					SELECT * FROM files
					WHERE externalRecId = ?
				`, [job.externalRecId]);
				const allUploaded = jobFiles.every(x => x.hasUploaded);
				if (allUploaded) {
					for (const file of jobFiles) {
						await this.deleteFile(file);
					}
					await this.deleteJob(job.externalRecId);
				}
			} catch (error) {
				console.error(error);
			}
		}
	}

	private async deleteFile(file: FileDto): Promise<void> {
		try {
			await Database.execute(`
				DELETE FROM files
				WHERE fileId = ?
			`, [file.fileId]);

			const path = file.filePath;

			if (await RNFS.exists(path)) {
				await RNFS.unlink(path);
			}
			else {
				console.warn('NOTFOUND', path);
			}

		} catch (error) {
			console.error(error);
		}
	}
	private async deleteJob(externalRecId: string): Promise<void> {
		try {
			await Database.execute(`
				DELETE FROM jobs
				WHERE externalRecId = ?
			`, [externalRecId]);
		} catch (error) {
			console.error(error);
		}
	}
}
const CleanupService = new cleanupService();
export default CleanupService;