import React from 'react';
import { View, Text, Modal, TouchableOpacity } from 'react-native';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import modalStyles from 'config/styles/modal-styles';
import AppButton from 'components/AppButton/AppButton';
import { ResetPasswordDto } from 'dtos/security';
import { showToast } from 'components/Toast/ToastProvider';
import SecurityService from 'services/security';
import Redi_Text from 'components/Text/Redi_Text';
import { scaleToDesign } from 'utils/design-scale';

export default class ChangePassword extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			data: {
				confirmPassword: '',
				newPassword: '',
				oldPassword: ''
			}
		};
	}
	render() {
		return (
			<Modal
				transparent
				visible
				onRequestClose={this.props.onClose}>
				<View style={{ alignItems: 'center' }}>
					<View style={modalStyles.content}>
						<View style={modalStyles.header}>
							<Text style={textStyles.large_2}>Change Password</Text>
							<TouchableOpacity onPress={this.props.onClose}>
								<CloseIcon />
							</TouchableOpacity>
						</View>
						<Redi_Text
							placeholder="Current Password"
							secureTextEntry
							onChange={e => this.setState(s => ({ data: { ...s.data, oldPassword: e } }))}
							value={this.state.data.oldPassword} />
						<Redi_Text
							placeholder="New Password"
							value={this.state.data.newPassword}
							secureTextEntry
							onChange={e => this.setState(s => ({ data: { ...s.data, newPassword: e } }))} />
						<Redi_Text
							placeholder="Confirm New Password"
							value={this.state.data.confirmPassword}
							secureTextEntry
							onChange={e => this.setState(s => ({ data: { ...s.data, confirmPassword: e } }))} />
						<View style={{ alignItems: 'flex-end' }} >
							<AppButton
								theme="green"
								marginVertical={scaleToDesign(10)}
								width={scaleToDesign(130)}
								onPress={() => this.save()}
								content="Save" />
						</View>
					</View>
				</View>
			</Modal>
		);
	}

	private save(): void {
		if (this.state.data.newPassword !== this.state.data.confirmPassword) {
			showToast('error', `New Passwords aren't the same`);
		}
		else {
			SecurityService.changePassword(this.state.data).then(x => {
				if (!x.error) {
					this.props.onClose();
				}
			});
		}
	}
}

interface Props {
	onClose(): void;
}

interface State {
	data: ResetPasswordDto;
}