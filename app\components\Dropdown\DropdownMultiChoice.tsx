import React from 'react';
import { View, Text, Animated, Easing, TouchableOpacity } from 'react-native';
import styles from './styles';
import Checkbox from 'components/Checkbox/Checkbox';
import ChevronIcon from 'project_components/Icons/ChevronIcon';
import { CommonFormComponentProps } from 'components/Form/form-props';
import FormComponentError from 'components/Form/FormComponentError';

export default class DropdownMultiChoice<T> extends React.PureComponent<DropdownMultiChoiceProps<T>, State> {
	constructor(props: DropdownMultiChoiceProps<T>) {
		super(props);

		this.state = {
			focused: false,
			scrollViewHeight: new Animated.Value(0)
		};

		this.onFocus = this.onFocus.bind(this);
		this.onSelect = this.onSelect.bind(this);
		this.getDisplayValue = this.getDisplayValue.bind(this);
	}

	componentDidUpdate(prevProps: DropdownMultiChoiceProps<T>, prevState: State) {
		if (prevState.focused && !this.state.focused) {
			//moving from focused to not focused. i.e closing the dropdown
			Animated.timing(this.state.scrollViewHeight, {
				toValue: 0,
				easing: Easing.ease,
				duration: 150
			}).start();
		} else if (!prevState.focused && this.state.focused) {
			//moving from not focused to focused. i.e opening the dropdown
			Animated.timing(this.state.scrollViewHeight, {
				toValue: 200,
				easing: Easing.ease,
				duration: 150
			}).start();
		}
	}
	render() {
		return (
			<View style={[styles.wrapper, this.props.overrideStyles?.wrapper]}>
				<View>
					<View style={[styles.selectWrapper, this.state.focused ? { borderBottomRightRadius: 0, borderBottomLeftRadius: 0 } : {}]}>
						<TouchableOpacity onPress={this.onFocus} style={{ width: "100%", height: "100%" }}>
							<View style={[{ width: "100%", height: "100%" }]}>
								{!!this.props.selected && !!this.props.selected.length &&
									<Text style={[styles.selectedText]}>
										{this.getDisplayValue()}
									</Text>
								}
								{!(this.props.selected && this.props.selected.length) && !!this.props.placeholder &&
									<Text style={[styles.placeholderText]}>{this.props.placeholder}</Text>
								}
								{/* just to push chevron to end */}
								<View style={{ flex: 1 }} />
								<ChevronIcon position="down" colour="#0054A2" />
							</View>
						</TouchableOpacity>
					</View>
					{this.state.focused &&
						<Animated.ScrollView nestedScrollEnabled={true} style={[styles.optionsWrapper, { height: this.state.scrollViewHeight }]}>
							{this.props.values.map((value: T, index: number) =>
								<TouchableOpacity key={index} onPress={() => this.onSelect(this.props.selected.indexOf(value) == -1, value)}>
									<View style={[styles.optionRow]}>
										<Checkbox onChange={(selected) => this.onSelect(selected, value)} selected={this.props.selected.indexOf(value) != -1} />
										<Text style={[styles.optionText]}>
											{this.props.displayTransform ? this.props.displayTransform(value) : value}
										</Text>
									</View>
								</TouchableOpacity>
							)}
						</Animated.ScrollView>
					}
					<FormComponentError error={this.props.error} />
				</View>
			</View>
		);
	}



	private onFocus() {
		if (this.state.focused) {
			this.props.onBlur?.(null);
		}
		else {
			this.props.onFocus?.(null);
		}
		this.setState({ focused: !this.state.focused });
	}
	private onSelect(selected: boolean, value: T) {
		this.props.onChange(selected, value);
		this.setState({ focused: false });
		this.props.onBlur?.(null);
	}
	private getDisplayValue() {
		let displayValue: string = "";
		if (this.props.selected && this.props.selected.length) {
			for (var ii = 0; ii < this.props.values.length; ii++) {
				let wkSelected = this.props.selected[ii];
				let selected: boolean = this.props.selected.indexOf(wkSelected) != -1;
				if (selected) {
					if (displayValue) {
						displayValue += ", ";
					}
					displayValue += this.props.displayTransform ? this.props.displayTransform(wkSelected) : wkSelected;
				}
			}
		}
		return displayValue;
	}
}

export interface DropdownMultiChoiceProps<T> extends Omit<CommonFormComponentProps<T>, 'onChange' | 'value'> {
	//This function is called whenever the selected value changes. The newly selected value is passed in as an argument.
	onChange: (selected: boolean, value: T) => void;
	selected: Array<T>;
	//optional function to transform the type of value to a string for display.
	displayTransform?: (value: T) => string;
	values: Array<T>;
	//optional. Ovveride the styles for this component
	overrideStyles?: {
		wrapper: object;
	}
}

interface State {
	focused: boolean;
	scrollViewHeight: Animated.Value;
}