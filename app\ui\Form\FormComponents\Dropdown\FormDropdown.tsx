import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import Dropdown, { DropdownProps } from 'components/Dropdown/Dropdown';

export default class FormDropdown<T> extends React.Component<WrappedFieldInterface<T, DropdownProps<T>>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<Dropdown<T>
				{...props}
				value={this.props.fieldProps.value}
				error={this.props.form.field.error}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				overrideStyles={{
					wrapper: {
						paddingTop: 15,
						paddingBottom: 15
					},
					content: {
						width: '80%'
					}
				}} />
		);
	}
}