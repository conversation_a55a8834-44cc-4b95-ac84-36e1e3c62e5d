import React from 'react';
import Svg, { G, Circle, Line } from 'react-native-svg';
import theme from 'config/styles/theme';
import { scaleToDesign } from 'utils/design-scale';

export default class SearchIcon extends React.PureComponent<Props, never> {
	static defaultProps = {
		colour: theme.PRIMARY_LIGHT_BLUE,
		size: 35
	};

	render() {
		return (
			<Svg width={scaleToDesign(this.props.size) + 'px'} height={scaleToDesign(this.props.size) + 'px'} viewBox="0 0 35 35">
				<G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
					<Circle stroke={this.props.colour} strokeWidth="2" cx="14.5975711" cy="14.5975711" r="13.2682462" />
					<Line x1="33.6706751" y1="33.6706751" x2="24.5487558" y2="24.5487558" stroke={this.props.colour} strokeWidth="2" />
				</G>
			</Svg>
		);
	}
}

interface Props {
	colour: string;
	size: number;
}
