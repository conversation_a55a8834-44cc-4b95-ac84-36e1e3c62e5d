import React from 'react';
import { WrappedFieldInterface } from 'redi-form';
import CheckBox, { CheckboxProps } from 'components/Checkbox/Checkbox';
import CheckboxLarge from 'components/CheckboxLarge/CheckboxLarge';

export default class FormCheckbox extends React.Component<WrappedFieldInterface<boolean, CheckboxProps>, never> {
	render() {
		const props = this.props.componentProps();
		return (
			<CheckboxLarge
				{...props}
				selected={!!this.props.fieldProps.value}
				onChange={(value) => this.props.fieldProps.onChange(value)}
				onBlur={(e) => this.props.fieldProps.onBlur?.(e as React.FocusEvent)}
				onFocus={(e) => this.props.fieldProps.onFocus?.(e as React.FocusEvent)}
				error={this.props.form.field.error} />
		);
	}
}