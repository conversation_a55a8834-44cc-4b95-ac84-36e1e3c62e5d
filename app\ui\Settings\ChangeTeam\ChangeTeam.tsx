import React from 'react';
import { View, Text, Modal, TouchableOpacity } from 'react-native';
import textStyles from 'config/styles/text-styles';
import CloseIcon from 'project_components/Icons/CloseIcon';
import Dropdown from 'components/Dropdown/Dropdown';
import { ReactComponent } from 'redi-types';
import { StoreComponent } from 'utils/store-component';
import { AppStore } from 'stores/app-store';
import { map } from 'rxjs/operators';
import { ConnectionStatus } from 'services/heartbeat';
import modalStyles from 'config/styles/modal-styles';
import AppButton from 'components/AppButton/AppButton';
import { TeamDto } from 'dtos/team';
import TeamService from 'services/team';
import Database from 'services/database';

@StoreComponent({
	isOffline: AppStore.connectionStatus$.pipe(map(x => x === ConnectionStatus.Disconnected)),
	currentTeam: AppStore.team$
})
class ChangeTeam extends React.PureComponent<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			team: '',
			teams: new Array<TeamDto>()
		};
	}

	async componentDidMount() {
		this.setState({ team: this.props.currentTeam });
		await TeamService.saveTeamsFromServer();
		TeamService.getTeams().then(x => this.setState({ teams: x.sort((a, b) => a.sortOrder - b.sortOrder) }));
	}

	render() {
		return (
			<Modal
				transparent
				visible
				onRequestClose={this.props.onClose}>
				<View style={{ alignItems: 'center' }}>
					<View style={modalStyles.content}>
						<View style={modalStyles.header}>
							<Text style={textStyles.large_2}>Change Team</Text>
							<TouchableOpacity onPress={this.props.onClose}>
								<CloseIcon />
							</TouchableOpacity>
						</View>
						<Dropdown
							placeholder="Select Team"
							value={this.state.team}
							onChange={e => this.setState({ team: e })}
							options={this.state.teams.map(x => x.name)}
							overrideStyles={{
								wrapper: {
									paddingLeft: 0,
									paddingRight: 0
								},
								optionsWrapper: {
									maxHeight: 180
								}
							}} />
						<AppButton
							rootStyle={{ marginTop: 150 }}
							theme="lightblue"
							content="Confirm"
							onPress={() => this.add()} />
					</View>
				</View>
			</Modal>
		);
	}

	private async add(): Promise<void> {
		const teamId = this.state.teams.find(x => x.name === this.state.team).teamId;
		AppStore.update({
			team: this.state.team,
			teamId,
			lastModified: null
		});
		Database.execute(`
			UPDATE user
			SET teamId = ?, teamName = ?, lastModified = NULL
			WHERE userId = ?
		`, [teamId, this.state.team, AppStore.getValue().user.UserId]);
		this.props.onClose();
	}
}
export default ChangeTeam as ReactComponent<PublicProps>;

interface Props extends PublicProps {
	isOffline: boolean;
	currentTeam: string;
}

interface PublicProps {
	onClose(): void;
}

interface State {
	team: string;
	teams: TeamDto[];
}