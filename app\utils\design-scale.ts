import { Dimensions, ViewStyle, TextStyle, ImageStyle, StyleSheet } from "react-native";

export const DEFAULT_PROPS = [
    "fontSize", 
    "width", 
    "height",
    "borderRadius",
    "borderTopLeftRadius", 
    "borderTopRightRadius", 
    "borderBottomRightRadius",
    "borderBottomLeftRadius",
    "top",
    "marginHorizontal"
];

type NamedStyles<T> = { [P in keyof T]: ViewStyle | TextStyle | ImageStyle };

export function scaleAllToDesign<T extends NamedStyles<T> | NamedStyles<any>>(styles: T | NamedStyles<T>, forStyles: Array<string> = DEFAULT_PROPS): T {
    Object.keys(styles).forEach((prop) => {
        let styleClass = (styles as any)[prop];
        if(typeof(styleClass) === 'object'){
            let styleClassKeys = Object.keys(styleClass);
            styleClassKeys.forEach((classProp) => {

                let styleValue = styleClass[classProp];
                if(typeof(styleValue) === 'number' && forStyles.includes(classProp)){
                    let newValue = scaleToDesign(styleValue);
                    styleClass[classProp] = newValue;
                }
            });
        }
    });
    return StyleSheet.create(styles);
}


export function scaleToDesign(size: number, round: boolean = true): number{
    let newSize: number = size;

    let designWidth: number = 800;
    let designHeight: number = 1280;

    var dimensions = Dimensions.get('window');
    let width: number = round ? Math.round(dimensions.width) : dimensions.width;
    let height: number = round ? Math.round(dimensions.height) : dimensions.height;
    let scale: number = dimensions.scale;

    //newSize *= (1 / scale);

    let designRatio = designWidth / designHeight;
    let currentRatio = width / height;

    //If ratios are identical then any scaling has kept the same resolution
    //
    //If currentRatio is greater than designRatio it means the current width is the thing pushing the 
    //resolution so if we tried to keep our own resultion it would overflow vertically so we must adjust the height
    //
    //otherwise if it is less then the height is pushing the resolution it means the current height is the thing pushing the 
    //resolution so if we tried to keep our own resultion it would overflow horizontally so we must adjust the width
    if(currentRatio > designRatio){
        //needa adjust the fontsize using the height as the restraining point
        //come up with a new scale based on the percent heigh diff between design and current heights
        let heightScale: number = height / designHeight;
        newSize *= heightScale;
    }else if(currentRatio < designRatio){
        let widthScale: number = width / designWidth;
        newSize *= widthScale;
    }
    
    return round ? Math.ceil(newSize) : newSize;
}