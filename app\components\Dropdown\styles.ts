import { StyleSheet } from 'react-native';
import { FORM_STYLES, FORM_CLASSES } from 'config/styles/form-component-common';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	wrapper: {
		paddingTop: 10,
		paddingBottom: 10,
		alignItems: 'center'
	},
	content: {
		width: '100%'
	},
	selectWrapper: {
		...FORM_CLASSES.INPUT_CONTAINER,
		alignItems: 'center',
		flexDirection: 'row',
		height: FORM_STYLES.INPUT_HEIGHT,
		justifyContent: 'space-between',
		paddingRight: 10,
		paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT
	},
	selectedText: {
		...FORM_CLASSES.LABEL_TEXT
	},
	focusedLabel: {
		left: 7,
		paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT - 7,
		paddingRight: FORM_STYLES.INPUT_PADDING_LEFT - 7,
		top: -10,
		fontSize: 12,
		position: 'absolute',
	},
	placeholderText: {
		fontSize: 20,
		maxHeight: '100%',
		maxWidth: '80%',
		color: '#777777',
		fontFamily: 'Montserrat-Medium',
		letterSpacing: 1,
		backgroundColor: 'white'
	},
	optionsWrapper: {
		width: '100%',
		position: 'absolute',
		backgroundColor: 'white',
		top: '100%',
		borderColor: '#979797',
		borderLeftWidth: StyleSheet.hairlineWidth,
		borderRightWidth: StyleSheet.hairlineWidth,
		borderBottomWidth: StyleSheet.hairlineWidth,
		borderBottomRightRadius: FORM_STYLES.BORDER_RADIUS,
		borderBottomLeftRadius: FORM_STYLES.BORDER_RADIUS,
		elevation: 3,
		zIndex: 5,
		maxHeight: 300
	},
	optionRow: {
		paddingLeft: FORM_STYLES.INPUT_PADDING_LEFT,
		paddingTop: 6,
		paddingBottom: 6,
		flexDirection: 'row'
	},
	optionText: {
		...FORM_CLASSES.LABEL_TEXT,
	}
});

export default styles;
