import { StyleSheet } from 'react-native';
import textStyles from 'config/styles/text-styles';
import theme from 'config/styles/theme';
import { scaleAllToDesign } from 'utils/design-scale';

const styles = scaleAllToDesign({
	username: {
		fontSize: 22,
		textAlign: 'center',
		fontFamily: 'Cabin-SemiBold',
		letterSpacing: 2,
		marginTop: 30,
		marginBottom: 15,
		color: theme.TEXT_DARK
	},
	backContainer: {
		flex: 1,
		justifyContent: 'flex-start',
		flexDirection: 'row'
	},
	headerContainer: {
		flex: 1,
		flexDirection: 'row',
		justifyContent: 'center',
		alignItems: 'center'
	},
	content: {
		flex: 1,
		paddingBottom: 90,
		paddingHorizontal: 40
	}
});
export default styles;